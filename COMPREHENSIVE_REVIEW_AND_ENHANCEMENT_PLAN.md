# 🚀 Word Generator Directory - Comprehensive Review & Enhancement Plan

## 📁 Current Directory Structure (Cleaned Up!)

```
word-generator/
├── Core Generators
│   ├── csv_based_phoneme_generator.py    # Main CSV-driven generator
│   ├── orton_gillingham_generator.py     # Original OG generator
│   ├── decodable_passage_generator.py    # Creates reading passages
│   └── word_search_generator.py          # Creates word search puzzles
├── Configuration
│   ├── phonics_master_rules.csv          # Original rules
│   └── updated_phonics_master_rules_complete.csv  # Enhanced rules
├── Heart Words System
│   ├── heart_words_reference.csv         # Master list
│   ├── heart_words_by_skill.csv         # Skill introduction mapping
│   └── heart_words_analysis.md          # Documentation
├── Web Interface
│   └── og_web_app.py                    # Streamlit web app
└── Documentation & Reports
    ├── README.md
    └── reports/                          # Generated outputs

```

## 🎯 CSV Enhancement Implementation Plan

### Phase 1: Immediate Improvements (Do Today)

1. **Create Enhanced CSV with Key Columns**
```python
import pandas as pd

# Load current CSV
df = pd.read_csv('updated_phonics_master_rules_complete.csv')

# Add critical new columns
df['prerequisite_skills'] = ''
df['position_by_grapheme'] = ''
df['generation_constraints'] = ''
df['heart_word_triggers'] = ''

# Save enhanced version
df.to_csv('enhanced_phonics_master_rules_v2.csv', index=False)
```

2. **Update Position Constraints for Multi-Pattern Skills**
```python
# Skills that need position_by_grapheme
multi_pattern_skills = {
    '18.1': {'ai': 'medial', 'ay': 'final', 'ei': 'medial', 'eigh': 'medial', 'ey': 'final', 'ea': 'medial'},
    '18.2': {'ea': 'any', 'ee': 'any', 'ie': 'medial', 'ey': 'final', 'ei': 'medial'},
    '18.3': {'oa': 'medial', 'oe': 'final', 'ow': 'final', 'ou': 'medial'},
    '18.11': {'oi': 'medial', 'oy': 'final'},
    '20.2': {'or': 'any', 'ore': 'final'}
}
```

### Phase 2: Generator Integration

1. **Enhance csv_based_phoneme_generator.py**
```python
class EnhancedCSVPhonemeGenerator(CSVBasedPhonemeGenerator):
    def check_prerequisites(self, skill_id: str, available_skills: List[str]) -> bool:
        """Check if all prerequisites are met"""
        prereqs = self.rules_df[self.rules_df['skill_id'] == skill_id]['prerequisite_skills'].iloc[0]
        if pd.isna(prereqs) or prereqs == '':
            return True
        
        required = [s.strip() for s in prereqs.split(',')]
        return all(req in available_skills for req in required)
    
    def apply_generation_constraints(self, words: List[str], constraints: str) -> List[str]:
        """Apply generation constraints from CSV"""
        if pd.isna(constraints):
            return words
            
        # Parse constraints like "min_5_per_pattern, verify_pronunciation"
        constraint_dict = {}
        for constraint in constraints.split(','):
            if ':' in constraint:
                key, value = constraint.split(':')
                constraint_dict[key.strip()] = value.strip()
        
        # Apply constraints
        if 'min_examples_per_pattern' in constraint_dict:
            # Ensure minimum examples per pattern
            pass
        
        return words
```

### Phase 3: New Features

1. **Position-Aware Word Filtering**
```python
def filter_by_position_constraints(word: str, grapheme: str, position: str) -> bool:
    """Check if grapheme appears in required position"""
    if position == 'initial':
        return word.startswith(grapheme)
    elif position == 'final':
        return word.endswith(grapheme)
    elif position == 'medial':
        return grapheme in word[1:-1]
    elif position == 'initial_or_final':
        return word.startswith(grapheme) or word.endswith(grapheme)
    return True
```

2. **Heart Word Integration**
```python
def get_heart_words_for_skill(skill_id: str) -> List[str]:
    """Get heart words that become irregular at this skill level"""
    triggers = df[df['skill_id'] == skill_id]['heart_word_triggers'].iloc[0]
    if pd.isna(triggers):
        return []
    return [w.strip() for w in triggers.split(',')]
```

## 🔥 Top 5 High-Impact Improvements

### 1. **Prerequisite Chain Validation**
```csv
skill_id,prerequisite_skills
1.1,""
1.2,"1.1"
4.5,"1.1,1.2,1.3,1.4,1.5"
5.1,"1.1,1.2,1.3,1.4,1.5,4.1"
```

### 2. **Position-Specific Generation**
```csv
skill_id,graphemes,position_by_grapheme
18.1,"ai, ay","ai:medial, ay:final"
```

### 3. **Frequency-Based Word Selection**
```csv
skill_id,generation_constraints
1.1,"prefer_high_frequency:true, min_frequency:100"
```

### 4. **Error Prevention Rules**
```csv
skill_id,common_errors,generation_constraints
4.5,"bak, ckap","exclude_errors:[bak,ckap]"
```

### 5. **Assessment-Focused Examples**
```csv
skill_id,assessment_focus,generation_constraints
18.1,"distinguish ai/ay usage","include_minimal_pairs:true"
```

## 🛠️ Implementation Scripts

### 1. CSV Enhancer Script
```python
# enhance_csv.py
import pandas as pd

def enhance_phonics_csv(input_path, output_path):
    df = pd.read_csv(input_path)
    
    # Add new columns
    new_columns = [
        'prerequisite_skills',
        'position_by_grapheme', 
        'generation_constraints',
        'heart_word_triggers',
        'frequency_rank',
        'common_errors',
        'teaching_tips'
    ]
    
    for col in new_columns:
        if col not in df.columns:
            df[col] = ''
    
    # Auto-fill some data
    # ... (prerequisite logic)
    
    df.to_csv(output_path, index=False)
```

### 2. Validation Script
```python
# validate_csv.py
def validate_phonics_csv(csv_path):
    df = pd.read_csv(csv_path)
    errors = []
    
    # Check prerequisites don't reference later skills
    # Check examples contain graphemes
    # Check position constraints match grapheme count
    
    return errors
```

## 📈 Next Steps Priority

1. **Today**: Add the 4 critical columns (prerequisites, position_by_grapheme, generation_constraints, heart_word_triggers)
2. **This Week**: Update multi-pattern skills with detailed positions
3. **Next Week**: Integrate enhanced CSV with generators
4. **Month Goal**: Full teaching tips and assessment integration

Would you like me to create the enhancement scripts to automate these improvements?
