# CSV Format Translation Guide

## Your CSV Deviations Are Now Fully Supported! 

### Here's how your CSV format translates to the generator:

## 1. **Grapheme Patterns Column**
Your format uses descriptive prefixes, which the translator now handles:

| Your Format | Translates To |
|------------|---------------|
| `single vowel: a` | `{'vowel': 'a'}` |
| `bl, cl, fl, gl, pl, sl` | `{'initial_blends': ['bl', 'cl', 'fl', 'gl', 'pl', 'sl']}` |
| `specific words: be, he, me` | `{'specific_words': ['be', 'he', 'me']}` |
| `consonant+le` | `{'c_le': True}` |
| `word+word` | `{'compound': True}` |
| `VCe` | `{'magic_e': True}` |

## 2. **Word Length Column**
Your range format is automatically parsed:

| Your Format | Translates To |
|------------|---------------|
| `3` | `{'min': 3, 'max': 3}` |
| `4-5` | `{'min': 4, 'max': 5}` |
| `6-10` | `{'min': 6, 'max': 10}` |

## 3. **Phoneme Requirement Column**
Your descriptive format is parsed into components:

| Your Format | Translates To |
|------------|---------------|
| `AE (short a)` | Code: `AE`, Description: `short a` |
| `TH or DH` | Codes: `['TH', 'DH']`, Description: `multiple options` |
| `consonant + L` | Code: `consonant + L`, Description: `""` |

## 4. **Position Column**
Now includes all position options including INITIAL_OR_FINAL:
- `initial`
- `medial`
- `final`
- `any`
- `initial_or_medial`
- `medial_or_final`
- `initial_or_final` ← **NEW!**
- `multiple`
- `various`

## 5. **Additional Constraints Column**
Pattern-specific positions are parsed from this column:

| Your Format | Translates To |
|------------|---------------|
| `ai/ei=medial, ay=final` | `{'ai': 'medial', 'ei': 'medial', 'ay': 'final'}` |
| `kn/wr=initial, mb=final` | `{'kn': 'initial', 'wr': 'initial', 'mb': 'final'}` |

## Using the Translator

```python
from csv_config_translator import SkillConfigTranslator

# Initialize translator
translator = SkillConfigTranslator()

# Get configuration for a skill
config = translator.get_skill_config('18.1')

# Returns:
# {
#     'skill_id': '18.1',
#     'name': 'Long a teams',
#     'patterns': {'vowel_teams': ['ai', 'ay', 'ei', 'eigh']},
#     'position': 'any',
#     'position_by_pattern': {'ai': 'medial', 'ay': 'final', ...},
#     'word_length': {'min': 4, 'max': 8},
#     'pattern_type': 'vowel_team',
#     'phoneme_code': 'EY',
#     'phoneme_description': 'long a'
# }
```

## Integration with Word Generator

The `PositionAwareWordGenerator` class automatically:
1. Loads your CSV configuration
2. Translates all the formats
3. Applies position requirements during word generation
4. Filters words based on all your constraints

```python
from position_aware_word_generator_integration import PositionAwareWordGenerator

# Create generator that uses your CSV
generator = PositionAwareWordGenerator()

# Generate words for a skill
words = generator.generate_for_skill('5.3')

# Words will be filtered by:
# - Decodability at that skill level
# - Position requirements from your CSV
# - Word length constraints
# - Pattern-specific positions
```

## Your CSV Format is Perfect! 

No need to change anything - the translator handles all the conversions automatically. Your descriptive format makes the CSV much more readable while still providing all the data the generator needs.

The system now fully supports:
- ✅ Your grapheme pattern descriptions
- ✅ Your word length ranges
- ✅ Your phoneme descriptions
- ✅ All position requirements (including INITIAL_OR_FINAL)
- ✅ Pattern-specific position constraints
- ✅ All your additional constraints

Just keep using your CSV as-is, and the translator will handle everything! 🎉
