# 📍 Position Constraints Quick Reference

## All Available Position Options

| Position | Description | Example | Pattern Examples |
|----------|-------------|---------|------------------|
| **initial** | Beginning of word only | **ch**ip | ch, wh, bl, tr |
| **medial** | Middle of word only | r**ai**n | ai, ea, oo |
| **final** | End of word only | ba**ck** | ck, ay, tch |
| **any** | Anywhere in word | **ch**ur**ch** | ch, sh, th |
| **initial_or_medial** | Beginning or middle, not end | **tr**ain, a**tr**ium | various patterns |
| **medial_or_final** | Middle or end, not beginning | ha**nd**, ra**nd**om | nd, ff, ll |
| **initial_or_final** | Beginning or end, not middle | **s**un, bu**s** | s, x, ph |
| **multiple** | Appears multiple times | **ch**ur**ch** | compound patterns |
| **various** | Different patterns have different positions | See position_by_grapheme | vowel teams |

## Using Position by Grapheme

For skills with "various" position, use the position_by_grapheme column:

```csv
skill_id: 18.1
graphemes: ai, ay, ei, eigh
position_constraints: various
position_by_grapheme: ai:medial, ay:final, ei:medial, eigh:medial
```

## Examples in Context

### Skill 4.5 (ck digraph)
- Position: **final**
- ✓ back, duck, stick
- ✗ ckick (can't start with ck)

### Skill 5.3 (s-blends)
- Position: **initial**
- ✓ stop, swim, scan
- ✗ fast (s-blend at end)

### Skill 18.1 (Long a teams)
- Position: **various**
- ai → medial: r**ai**n ✓, ai ✗
- ay → final: pl**ay** ✓, ayful ✗

## Implementation in Code

```python
def check_position(word, pattern, position):
    if position == "initial":
        return word.startswith(pattern)
    elif position == "final":
        return word.endswith(pattern)
    elif position == "medial":
        return pattern in word[1:-1]
    elif position == "initial_or_final":
        return word.startswith(pattern) or word.endswith(pattern)
    # ... etc
```

## Quick Decision Tree

1. **Single pattern?** → Use position_constraints
2. **Multiple patterns, same position?** → Use position_constraints  
3. **Multiple patterns, different positions?** → Use position_by_grapheme
4. **Complex rules?** → Add to additional_constraints

---
*Save this reference for quick lookups when updating your CSV!*
