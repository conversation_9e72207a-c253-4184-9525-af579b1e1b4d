# Orton-Gillingham Word Generator

A comprehensive web application for generating phonics-based word lists, decodable passages, and word searches following the Orton-Gillingham approach.

## Quick Start

1. **Install dependencies:**
   ```bash
   pip install streamlit pandas nltk plotly
   python download_nltk_data.py
   ```

2. **Run the web app:**
   ```bash
   streamlit run og_web_app.py
   ```

3. **Access at:** http://localhost:8501

## Features

- **Word List Generation**: Create customized word lists for 82+ Orton-Gillingham skills
- **Decodable Passages**: Generate reading passages using only previously taught patterns
- **Word Searches**: Create printable word search puzzles
- **CSV Export**: Export all generated content for classroom use

## How It Works

The system is data-driven using a CSV file that defines all phonics patterns:

1. **Rules CSV** (`updated_phonics_master_rules_complete.csv`): Contains all skills with:
   - Grapheme patterns (spelling)
   - Required phonemes (sounds)
   - Word length constraints
   - Position requirements
   - Special rules

2. **Word Generation**: 
   - Phoneme-based: Generates words matching specific sound patterns
   - Transformation-based: Applies spelling rules to existing words
   - Structural: Identifies syllabication patterns

3. **Customization**: Edit the CSV file to:
   - Modify which patterns are taught at each level
   - Change phoneme requirements
   - Add or remove skills
   - Adjust word selection criteria

## Directory Structure

```
word-generator/
├── og_web_app.py                              # Streamlit web interface
├── csv_based_phoneme_generator.py             # Core word generation engine
├── orton_gillingham_generator.py              # Adapter for Streamlit compatibility
├── decodable_passage_generator.py             # Creates reading passages
├── word_search_generator.py                   # Creates word search puzzles
├── updated_phonics_master_rules_complete.csv  # All phonics rules (customizable)
├── download_nltk_data.py                      # Setup script
├── heart_words/                               # Irregular "heart" words
│   ├── heart_words_reference_by_skill.csv
│   ├── heart_words_introduction_schedule.md
│   └── heart_words_analysis.md
├── word_lists/                                # Pre-generated word lists
│   └── ... (82 skill files)
├── reports/                                   # Analysis reports
└── _archive_development/                      # Development files (archived)
```

## Customizing Rules

To modify the phonics patterns:

1. Open `updated_phonics_master_rules_complete.csv`
2. Edit the relevant columns:
   - `graphemes`: Spelling patterns to look for
   - `required_phonemes`: Sounds that must be produced
   - `word_length`: Acceptable word lengths
   - `special_rules`: Additional constraints

3. Save the CSV and restart the app

## Examples

### Skill 1.1 (CVC short a):
- Graphemes: `a`
- Required phonemes: `AE` (short /a/ sound)
- Examples: cat, hat, mat

### Skill 18.5 (Long i teams):
- Graphemes: `ie, igh`
- Required phonemes: `AY` (long /i/ sound)
- Examples: pie, night, bright

### Skill 24 (1-1-1 doubling):
- Base words: hop, sit, run
- Transformation: Double final consonant before -ing
- Results: hopping, sitting, running

## Word List Statistics
- Total skills: 82+
- Total words: ~3,000
- Heart words: 122 properly annotated
- High-frequency words marked throughout

## Support

For questions or issues, please create an issue on GitHub or contact the development team.

---

*Built with Streamlit and NLTK for systematic phonics instruction*
