# Decodable Word Generator for Orton-Gillingham

## Overview

This enhanced word generator ensures that **every word is 100% decodable** at the skill level where it's introduced. No more "stood" at skill 5.3!

## The Problem We Solved

The original word generator would find words containing the target pattern (e.g., "st" blend) but didn't check if ALL patterns in the word were decodable. This led to words like:
- "stood" at skill 5.3 (contains "oo" vowel team not taught until 18.7/18.8)
- "state" at skill 5.3 (contains Magic E pattern not taught until 11.0)
- "paint" at skill 6.1 (contains "ai" vowel team not taught until 18.1)

## How the Fix Works

The new generator:
1. **Analyzes every phonics pattern** in each word
2. **Checks if all patterns have been taught** by that skill level
3. **Only includes words that are 100% decodable**
4. **Properly tracks heart words** that are allowed as exceptions

## Usage

### Generate Decodable Word Lists
```bash
python decodable_word_generator.py
```
This creates fully decodable word lists in `word_lists_decodable/`

### Validate Existing Word Lists
```bash
python comprehensive_decodability_validator.py
```
This checks your current word lists and reports all decodability violations.

### Compare Old vs New
```bash
python compare_decodability.py
```
Shows specific examples of words that were incorrectly included before.

### Demo for Skill 5.3
```bash
python demo_decodable_5_3.py
```
Demonstrates how "stood" and similar words are correctly rejected.

## Key Files

- **`decodable_word_generator.py`** - The new generator with decodability checking
- **`comprehensive_decodability_validator.py`** - Validates existing word lists
- **`compare_decodability.py`** - Compares old vs new approach
- **`demo_decodable_5_3.py`** - Specific demo for skill 5.3 issues

## Example: Skill 5.3 (s-blends)

**Before (Original Generator):**
- ❌ stood (contains 'oo' vowel team)
- ❌ state (contains Magic E)
- ❌ sweet (contains 'ee' vowel team)
- ✓ stop
- ✓ step

**After (Decodable Generator):**
- ✓ stop
- ✓ step  
- ✓ stick
- ✓ stand
- ✓ spend
- (All words use only patterns taught by skill 5.3)

## Benefits

1. **True Decodability**: Students can decode every phonics element
2. **No Guessing**: Eliminates the need to guess or memorize
3. **Systematic Progress**: Maintains the cumulative nature of O-G
4. **Builds Confidence**: Students experience success with every word
5. **Teacher Trust**: Teachers can trust that word lists are appropriate

## Next Steps

1. Run `python decodable_word_generator.py` to generate new word lists
2. Review the generated lists in `word_lists_decodable/`
3. Replace your current word lists with the decodable versions
4. Use the validator periodically to ensure new words maintain decodability

## Technical Details

The generator tracks what's decodable at each level:
- **Skills 1.x**: Only CVC with specific short vowels
- **Skills 4.x**: Adds digraphs (ch, sh, th, wh, ck)
- **Skills 5.x**: Adds initial blends (but NO vowel teams)
- **Skill 11.0**: Adds Magic E patterns
- **Skills 18.x**: Adds vowel teams (ai, ee, oo, etc.)
- **Skills 20.x**: Adds r-controlled vowels

Every word is checked against this progression before inclusion.
