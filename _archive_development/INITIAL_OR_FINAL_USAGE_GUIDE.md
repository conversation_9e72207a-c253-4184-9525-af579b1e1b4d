# Adding INITIAL_OR_FINAL Position to Your CSV

## How to Use the New Position Option

To use the `initial_or_final` position in your CSV, simply add it to the `position` column for any skill where patterns should appear at the beginning OR end of words, but NOT in the middle.

### Example CSV Entries

Here are some examples of how you might use `initial_or_final` in your CSV:

```csv
skill_id,skill_name,grapheme_patterns,phoneme_requirement,position,word_length,pattern_type,additional_constraints
31.1,s at boundaries,s,S,initial_or_final,3-6,consonant_boundary,s only at word edges
31.2,x endings and beginnings,x,K S,initial_or_final,3-6,consonant_boundary,x-ray or box patterns
31.3,ph boundaries,ph,F,initial_or_final,4-8,digraph_boundary,"photo, graph but not alphabet"
```

### Pattern Examples with INITIAL_OR_FINAL

1. **Letter 's'**:
   - ✓ Initial: sun, say, stop
   - ✓ Final: bus, yes, this
   - ✗ Medial: lesson, basic, person

2. **Letter 'x'**:
   - ✓ Initial: x-ray, xylophone (rare)
   - ✓ Final: box, fox, six
   - ✗ Medial: taxi, exit, next

3. **Digraph 'ph'**:
   - ✓ Initial: phone, photo, phrase
   - ✓ Final: graph, paragraph
   - ✗ Medial: alphabet, elephant, nephew

### When to Use INITIAL_OR_FINAL

This position is useful for:

1. **Consonants with positional constraints**: Some consonants appear primarily at word boundaries
2. **Morphological patterns**: Patterns that can be either prefixes or suffixes
3. **Advanced spelling patterns**: Where position affects pronunciation or spelling rules
4. **Phonological constraints**: Sounds that naturally occur at word edges

### Integration with Existing Patterns

The position value works seamlessly with your existing system:

```python
# All position values available:
- initial              # Beginning only
- medial               # Middle only
- final                # End only
- any                  # Anywhere in word
- initial_or_medial    # Beginning or middle
- medial_or_final      # Middle or end
- initial_or_final     # Beginning or end (NEW!)
- multiple             # Multiple occurrences
- various              # Pattern-specific positions
```

### Adding to Additional Constraints

For skills with `various` position, you can specify `initial_or_final` for specific patterns:

```csv
skill_id,skill_name,grapheme_patterns,phoneme_requirement,position,word_length,pattern_type,additional_constraints
32.1,Mixed position patterns,"s, x, ph",various,various,3-8,mixed,"s=initial_or_final, x=final, ph=initial"
```

This would mean:
- 's' can be initial or final
- 'x' must be final
- 'ph' must be initial

### Testing Your Patterns

After adding skills with `initial_or_final`, test them using:

```python
from position_aware_skill_config import check_pattern_position

# Test examples
print(check_pattern_position("sun", "s", "initial_or_final"))    # True
print(check_pattern_position("bus", "s", "initial_or_final"))    # True
print(check_pattern_position("lesson", "s", "initial_or_final")) # False
```

## Implementation Complete! ✅

The INITIAL_OR_FINAL position is now fully implemented and ready to use in your CSV configuration.
