#!/bin/bash
# Archive non-essential files for word-generator cleanup

ARCHIVE_DIR="_archive_development"
echo "Archiving development files to $ARCHIVE_DIR..."

# Files to archive (all test, demo, analysis, and old generator files)
FILES_TO_ARCHIVE=(
    "check_violations.py"
    "cleanup_directory.py"
    "cmu_phoneme_reference.csv"
    "compare_decodability.py"
    "complete_fix_summary.py"
    "comprehensive_decodability_report.txt"
    "comprehensive_decodability_validator.py"
    "create_complete_phonics_rules_csv.py"
    "create_master_wordlist.py"
    "create_phonics_rules_csv.py"
    "csv_config_translator.py"
    "decodability_constraints.csv"
    "decodability_fix_summary.py"
    "decodability_validator.py"
    "DECODABLE_GENERATOR_README.md"
    "decodable_word_generator.py"
    "demo_decodable_5_3.py"
    "enhanced_decodability_checker.py"
    "extract_skill_configurations.py"
    "homophone_skills.csv"
    "INITIAL_OR_FINAL_USAGE_GUIDE.md"
    "og_skill_pattern_configurations_with_position.csv"
    "og_skill_pattern_configurations.csv"
    "orton_gillingham_generator.py"  # Old generator - replaced by CSV-based
    "phoneme_aware_decodable_generator.py"  # Old version
    "phoneme_based_generator.py"
    "phonics_master_rules_complete.csv"  # Old version
    "phonics_master_rules.csv"
    "position_aware_skill_config.py"
    "position_aware_word_generator_integration.py"
    "position_requirements_demo.py"
    "run_cleanup.sh"
    "skill_18.5_PHONEME_DECODABLE.csv"
    "skill_config.py"
    "spelling_rules_demo.py"
    "spellingrules.csv"
    "syllabication_for_main_csv.csv"
    "syllabication_integration_notes.md"
    "syllabication_patterns.csv"
    "syllabication.csv"
    "test_csv_generator.py"
    "test_initial_or_final_position.py"
    "updated_syllabication_patterns.csv"
    "vowel_team_phoneme_requirements.csv"
    "archive_files.sh"  # This script itself
)

# Directories to archive
DIRS_TO_ARCHIVE=(
    "word_lists_decodable"
    "_archive_20250702"  # Previous archive if exists
)

# Archive files
for file in "${FILES_TO_ARCHIVE[@]}"; do
    if [ -f "$file" ]; then
        mv "$file" "$ARCHIVE_DIR/"
        echo "Archived: $file"
    fi
done

# Archive directories
for dir in "${DIRS_TO_ARCHIVE[@]}"; do
    if [ -d "$dir" ]; then
        mv "$dir" "$ARCHIVE_DIR/"
        echo "Archived directory: $dir"
    fi
done

echo "Archive complete!"
echo ""
echo "Remaining essential files:"
ls -la | grep -E "(og_web_app|csv_based_phoneme_generator|decodable_passage_generator|word_search_generator|updated_phonics_master_rules_complete|heart_words|word_lists|reports|README|download_nltk)"