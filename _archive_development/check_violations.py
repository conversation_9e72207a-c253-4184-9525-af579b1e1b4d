#!/usr/bin/env python3
"""
Quick Decodability Check - Show specific examples of violations
"""

import pandas as pd
import os

# Common problematic patterns that appear too early
VOWEL_TEAMS = ['ai', 'ay', 'ea', 'ee', 'oa', 'oe', 'oi', 'oo', 'ou', 'ow', 'oy', 'ue', 'ew', 'igh', 'au', 'aw']
R_CONTROLLED = ['ar', 'er', 'ir', 'or', 'ur']

def check_specific_violations():
    """Check for specific known violations"""
    
    word_lists_dir = '/Users/<USER>/word-generator/word_lists'
    
    print("DECODABILITY VIOLATIONS - SPECIFIC EXAMPLES")
    print("=" * 80)
    print("\nWords containing patterns not yet taught:\n")
    
    # Check skill 5 (blends) for vowel teams
    skill_5_files = ['skill_5.1_l-blends.csv', 'skill_5.2_r-blends.csv', 
                     'skill_5.3_s-blends.csv', 'skill_5.4_3-letter_blends.csv']
    
    print("SKILL 5 (BLENDS) - Should NOT have vowel teams:")
    print("-" * 60)
    
    for skill_file in skill_5_files:
        filepath = os.path.join(word_lists_dir, skill_file)
        if os.path.exists(filepath):
            df = pd.read_csv(filepath)
            
            violations = []
            for _, row in df.iterrows():
                word = row['word'].lower()
                
                # Check for vowel teams
                for vt in VOWEL_TEAMS:
                    if vt in word and not row.get('is_heart', False):
                        violations.append(f"{word} (contains '{vt}')")
                        break
            
            if violations:
                print(f"\n{skill_file}:")
                for v in violations[:5]:
                    print(f"  ❌ {v}")
                if len(violations) > 5:
                    print(f"  ... and {len(violations)-5} more")
    
    # Check skill 6 (final blends) for vowel teams
    print("\n\nSKILL 6 (FINAL BLENDS) - Should NOT have vowel teams:")
    print("-" * 60)
    
    skill_6_example = 'skill_6.1_nt_ending.csv'
    filepath = os.path.join(word_lists_dir, skill_6_example)
    if os.path.exists(filepath):
        df = pd.read_csv(filepath)
        
        violations = []
        for _, row in df.iterrows():
            word = row['word'].lower()
            
            for vt in VOWEL_TEAMS:
                if vt in word and not row.get('is_heart', False):
                    violations.append(f"{word} (contains '{vt}')")
                    break
        
        if violations:
            print(f"\n{skill_6_example}:")
            for v in violations[:10]:
                print(f"  ❌ {v}")
    
    # Check early skills for r-controlled
    print("\n\nEARLY SKILLS (1-10) - Should NOT have r-controlled vowels:")
    print("-" * 60)
    
    for skill_num in range(1, 11):
        skill_files = [f for f in os.listdir(word_lists_dir) 
                      if f.startswith(f'skill_{skill_num}.') and f.endswith('.csv')]
        
        for skill_file in skill_files:
            filepath = os.path.join(word_lists_dir, skill_file)
            df = pd.read_csv(filepath)
            
            violations = []
            for _, row in df.iterrows():
                word = row['word'].lower()
                
                for rc in R_CONTROLLED:
                    if rc in word and not row.get('is_heart', False):
                        # Special exception for 'er' at end of comparative
                        if rc == 'er' and word.endswith('er'):
                            continue
                        violations.append(f"{word} (contains '{rc}')")
                        break
            
            if violations:
                print(f"\n{skill_file}:")
                for v in violations[:5]:
                    print(f"  ❌ {v}")
                if len(violations) > 5:
                    print(f"  ... and {len(violations)-5} more")
    
    # Show specific problematic words
    print("\n\nSPECIFIC PROBLEMATIC WORDS:")
    print("-" * 60)
    
    problematic_examples = {
        'stood': ('skill_5.3_s-blends.csv', "contains 'oo' vowel team (skill 18.7)"),
        'play': ('skill_5.1_l-blends.csv', "contains 'ay' vowel team (skill 18.1)"),
        'paint': ('skill_6.1_nt_ending.csv', "contains 'ai' vowel team (skill 18.1)"),
        'each': ('skill_4.1_ch_digraph.csv', "contains 'ea' vowel team (skill 18.2)"),
        'reach': ('skill_4.1_ch_digraph.csv', "contains 'ea' vowel team (skill 18.2)"),
        'small': ('skill_7.0_FLOSS_rule.csv', "contains 'all' pattern saying /ôl/"),
        'walk': ('skill_6.7_lk_ending.csv', "contains silent 'l' pattern"),
        'storm': ('skill_5.3_s-blends.csv', "contains 'or' r-controlled (skill 20.2)")
    }
    
    for word, (expected_file, reason) in problematic_examples.items():
        filepath = os.path.join(word_lists_dir, expected_file)
        if os.path.exists(filepath):
            df = pd.read_csv(filepath)
            if word in df['word'].str.lower().values:
                print(f"\n'{word}' found in {expected_file}")
                print(f"  Problem: {reason}")
                row = df[df['word'].str.lower() == word].iloc[0]
                if row.get('is_heart', False):
                    print(f"  Status: Marked as heart word ✓")
                else:
                    print(f"  Status: NOT marked as heart word ❌")

if __name__ == "__main__":
    check_specific_violations()
    
    print("\n\nTo run full validation:")
    print("  python3 decodability_validator.py")
    print("\nTo generate clean word lists:")
    print("  python3 enhanced_decodability_checker.py")
