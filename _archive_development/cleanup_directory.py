#!/usr/bin/env python3
"""
Clean up the word-generator directory
Removes temporary scripts and organizes final structure
"""

import os
import shutil
from datetime import datetime

def cleanup_directory():
    """Clean up temporary files and organize directory structure"""
    
    base_dir = '/Users/<USER>/word-generator'
    
    # Create archive directory for old files
    archive_dir = os.path.join(base_dir, '_archive_' + datetime.now().strftime('%Y%m%d'))
    os.makedirs(archive_dir, exist_ok=True)
    
    # Files to remove (temporary scripts)
    files_to_remove = [
        'analyze_corrections.py',
        'test_corrections.py',
        'word_list_corrector.py',
        'run_corrector.sh',
        'analyze_book6.py',
        'integrate_book6.py',
        'copy_heart_words.sh',
        'create_master_wordlist.sh'
    ]
    
    # Files to keep
    essential_files = [
        'README.md',
        'orton_gillingham_generator.py',
        'og_web_app.py',
        'decodable_passage_generator.py',
        'word_search_generator.py',
        'download_nltk_data.py',
        'create_master_wordlist.py',  # Keep this for future regeneration
        'master_wordlist_book6_format.csv',
        'master_wordlist_compact.csv'
    ]
    
    # Directories to keep
    essential_dirs = [
        'word_lists_corrected',  # Our final word lists
        'heart_words',          # Heart word references
        'reports'               # Analysis reports
    ]
    
    print("CLEANING UP WORD-GENERATOR DIRECTORY")
    print("=" * 60)
    
    # Archive and remove temporary files
    removed_count = 0
    for filename in files_to_remove:
        filepath = os.path.join(base_dir, filename)
        if os.path.exists(filepath):
            # Move to archive
            archive_path = os.path.join(archive_dir, filename)
            shutil.move(filepath, archive_path)
            print(f"Archived: {filename}")
            removed_count += 1
    
    # Archive original word_lists directory (we now use word_lists_corrected)
    original_lists = os.path.join(base_dir, 'word_lists')
    if os.path.exists(original_lists):
        archive_lists = os.path.join(archive_dir, 'word_lists_original')
        shutil.move(original_lists, archive_lists)
        print("Archived: word_lists/ (original uncorrected lists)")
    
    # Rename word_lists_corrected to word_lists for simplicity
    corrected_lists = os.path.join(base_dir, 'word_lists_corrected')
    new_lists = os.path.join(base_dir, 'word_lists')
    if os.path.exists(corrected_lists) and not os.path.exists(new_lists):
        shutil.move(corrected_lists, new_lists)
        print("Renamed: word_lists_corrected/ → word_lists/")
    
    # Create a new README with current state
    readme_content = """# Orton-Gillingham Word Generator

## Overview
This directory contains the complete Orton-Gillingham word list generation system with:
- 30 skills covering CVC through morphology
- ~2,500+ validated and corrected words
- Heart word reference with skill-based introduction schedule
- Tools for generating decodable passages and word searches

## Directory Structure
```
word-generator/
├── README.md                           # This file
├── orton_gillingham_generator.py      # Main word generation engine
├── og_web_app.py                      # Streamlit web interface
├── decodable_passage_generator.py     # Generate decodable texts
├── word_search_generator.py           # Create word search puzzles
├── create_master_wordlist.py          # Combine all lists into master CSV
├── master_wordlist_book6_format.csv   # Complete word list with headers
├── master_wordlist_compact.csv        # Complete word list (data only)
├── word_lists/                        # Individual skill word lists (corrected)
│   ├── skill_1.1_CVC_short_a.csv
│   ├── skill_1.2_CVC_short_e.csv
│   └── ... (79 skill files)
├── heart_words/                       # Heart word references
│   ├── heart_words_reference_by_skill.csv
│   ├── heart_words_introduction_schedule.md
│   └── heart_words_analysis.md
└── reports/                           # Analysis and correction reports
```

## Quick Start

### View Words for a Specific Skill
```python
import pandas as pd
df = pd.read_csv('word_lists/skill_4.1_ch_digraph.csv')
print(df.head())
```

### Generate Master Word List
```bash
python3 create_master_wordlist.py
```

### Run Web Interface
```bash
streamlit run og_web_app.py
```

## Key Files

- **master_wordlist_book6_format.csv**: All words organized by skill with category headers
- **heart_words/heart_words_reference_by_skill.csv**: 70+ heart words mapped to OG skills
- **word_lists/**: Individual CSV files for each of the 30 skills

## Word List Statistics
- Total skills: 30
- Total words: ~2,500
- Heart words: 122 properly annotated
- High-frequency words marked throughout

Last updated: """ + datetime.now().strftime('%Y-%m-%d') + """
"""
    
    readme_path = os.path.join(base_dir, 'README.md')
    with open(readme_path, 'w') as f:
        f.write(readme_content)
    print("\nUpdated README.md with current structure")
    
    # Clean up pycache
    pycache = os.path.join(base_dir, '__pycache__')
    if os.path.exists(pycache):
        shutil.rmtree(pycache)
        print("Removed: __pycache__/")
    
    # Summary
    print("\n" + "=" * 60)
    print("CLEANUP COMPLETE!")
    print(f"- Archived {removed_count} temporary files to {archive_dir}")
    print("- Renamed word_lists_corrected/ to word_lists/")
    print("- Updated README.md")
    print("\nEssential files preserved:")
    for f in essential_files:
        if os.path.exists(os.path.join(base_dir, f)):
            print(f"  ✓ {f}")
    
    print("\nEssential directories:")
    for d in ['word_lists', 'heart_words', 'reports']:
        if os.path.exists(os.path.join(base_dir, d)):
            print(f"  ✓ {d}/")

if __name__ == "__main__":
    cleanup_directory()
