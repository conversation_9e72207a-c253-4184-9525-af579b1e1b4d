#!/usr/bin/env python3
"""
Compare old word lists with decodable word lists to show improvements
"""

import pandas as pd
import os

def compare_word_lists():
    """Compare original and decodable word lists"""
    original_dir = '/Users/<USER>/word-generator/word_lists'
    decodable_dir = '/Users/<USER>/word-generator/word_lists_decodable'
    
    # Example comparisons for key skills
    skills_to_check = [
        ('5.3', 's-blends', ['stood', 'state', 'sweet', 'snow', 'stage', 'smile']),
        ('5.1', 'l-blends', ['play', 'blow', 'blue', 'slow']),
        ('4.1', 'ch_digraph', ['each', 'reach', 'teach']),
        ('6.1', 'nt_ending', ['paint', 'point']),
        ('7.0', 'FLOSS_rule', ['small', 'ball', 'call'])
    ]
    
    print("DECODABILITY COMPARISON REPORT")
    print("=" * 80)
    print("Showing words that were incorrectly included in original lists\n")
    
    for skill_id, skill_name, problem_words in skills_to_check:
        print(f"\nSkill {skill_id} ({skill_name}):")
        print("-" * 50)
        
        # Read original list
        original_file = f"skill_{skill_id}_{skill_name}.csv"
        original_path = os.path.join(original_dir, original_file)
        
        if os.path.exists(original_path):
            df_original = pd.read_csv(original_path)
            original_words = set(df_original['word'].str.lower())
            
            # Check which problem words were in original
            found_problems = []
            for word in problem_words:
                if word.lower() in original_words:
                    found_problems.append(word)
            
            if found_problems:
                print(f"  ❌ Original list contained non-decodable words:")
                for word in found_problems:
                    # Explain why each word is problematic
                    if word == 'stood':
                        print(f"     - '{word}': contains 'oo' vowel team (not taught until 18.7/18.8)")
                    elif word in ['state', 'stage', 'space', 'spoke', 'smile']:
                        print(f"     - '{word}': contains Magic E pattern (not taught until 11.0)")
                    elif word == 'sweet':
                        print(f"     - '{word}': contains 'ee' vowel team (not taught until 18.2)")
                    elif word == 'snow' or word == 'slow' or word == 'blow':
                        print(f"     - '{word}': contains 'ow' vowel team (not taught until 18.3)")
                    elif word == 'play':
                        print(f"     - '{word}': contains 'ay' vowel team (not taught until 18.1)")
                    elif word == 'blue':
                        print(f"     - '{word}': contains 'ue' vowel team (not taught until 18.6)")
                    elif word in ['each', 'reach', 'teach']:
                        print(f"     - '{word}': contains 'ea' vowel team (not taught until 18.2)")
                    elif word == 'paint':
                        print(f"     - '{word}': contains 'ai' vowel team (not taught until 18.1)")
                    elif word == 'point':
                        print(f"     - '{word}': contains 'oi' vowel team (not taught until 18.11)")
                    elif word in ['small', 'ball', 'call']:
                        print(f"     - '{word}': contains 'all' pattern (exception taught at 12.1)")
                    else:
                        print(f"     - '{word}': contains patterns not yet taught")
            else:
                print(f"  ✓ None of the sample problem words found")
                
            # Show total word counts
            print(f"\n  Original list: {len(df_original)} words total")
            
            # Check if decodable version exists
            decodable_path = os.path.join(decodable_dir, original_file)
            if os.path.exists(decodable_path):
                df_decodable = pd.read_csv(decodable_path)
                print(f"  Decodable list: {len(df_decodable)} words (fully decodable)")
                
                # Check that problem words are NOT in decodable list
                decodable_words = set(df_decodable['word'].str.lower())
                still_present = [w for w in found_problems if w.lower() in decodable_words]
                
                if still_present:
                    print(f"  ⚠️  WARNING: Some problem words still present: {still_present}")
                else:
                    print(f"  ✓ All problem words successfully removed")
    
    print("\n\nSUMMARY:")
    print("=" * 80)
    print("The new decodable generator ensures that students can decode EVERY")
    print("phonics element in EVERY word using only patterns they've been taught.")
    print("\nThis maintains the systematic, cumulative nature of Orton-Gillingham!")

def check_specific_word(word, skill_id):
    """Check if a specific word is decodable at a given skill level"""
    from decodable_word_generator import DecodableWordGenerator
    
    generator = DecodableWordGenerator()
    is_decodable, reason = generator.is_word_decodable(word, skill_id)
    
    print(f"\nDecodability check for '{word}' at skill {skill_id}:")
    print(f"  Result: {'✓ DECODABLE' if is_decodable else '❌ NOT DECODABLE'}")
    if not is_decodable:
        print(f"  Reason: {reason}")
    
    return is_decodable

def main():
    print("WORD LIST DECODABILITY COMPARISON")
    print("=" * 80)
    
    # Run comparison
    compare_word_lists()
    
    # Check specific examples
    print("\n\nSPECIFIC EXAMPLES:")
    print("=" * 80)
    
    # Check "stood" at different skill levels
    check_specific_word("stood", "5.3")  # Should fail
    check_specific_word("stood", "18.8")  # Should pass (after short oo is taught)
    
    # Check "state" at different skill levels  
    check_specific_word("state", "5.3")  # Should fail
    check_specific_word("state", "11.0")  # Should pass (after Magic E is taught)
    
    # Check a truly decodable s-blend word
    check_specific_word("stop", "5.3")  # Should pass
    check_specific_word("skip", "5.3")  # Should pass

if __name__ == "__main__":
    main()
