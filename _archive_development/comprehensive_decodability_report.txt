COMPREHENSIVE DECODABILITY VALIDATION REPORT
====================================================================================================
Words containing patterns that haven't been taught yet

Total violations found: 763
Skills with violations: 51 / 82

TOP 10 MOST PROBLEMATIC SKILLS:
----------------------------------------------------------------------------------------------------

Skill 5.2 (skill_5.2_r-blends.csv):
  Total words: 50
  Violations: 42 (84.0%)
  Examples:
    1. "great" [High Frequency]
       - Pattern 'ea' (vowel_team) requires skill 18.2
    2. "group" [High Frequency]
       - Pattern 'ou' (vowel_team) requires skill 18.4
    3. "free" [High Frequency]
       - Pattern 'ee' (vowel_team) requires skill 18.2
    4. "true" [High Frequency]
       - Pattern 'ue' (vowel_team) requires skill 18.6
    5. "brown"
       - Pattern 'ow' (vowel_team) requires skill 18.3
    ... and 37 more violations

Skill 5.4 (skill_5.4_3-letter_blends.csv):
  Total words: 50
  Violations: 40 (80.0%)
  Examples:
    1. "three" [High Frequency]
       - Pattern 'ee' (vowel_team) requires skill 18.2
    2. "street" [High Frequency]
       - Pattern 'ee' (vowel_team) requires skill 18.2
    3. "strong" [High Frequency]
       - Pattern 'ng' (final_blend) requires skill 6.12
    4. "square"
       - Pattern 'ar' (r_controlled) requires skill 20.3
       - Pattern 'VCe' (magic_e) requires skill 11.0
    5. "spring"
       - Pattern 'ng' (final_blend) requires skill 6.12
    ... and 35 more violations

Skill 3.1 (skill_3.1_c_before_a,o,u.csv):
  Total words: 50
  Violations: 38 (76.0%)
  Examples:
    1. "came" [High Frequency]
       - Pattern 'VCe' (magic_e) requires skill 11.0
    2. "case" [High Frequency]
       - Pattern 'VCe' (magic_e) requires skill 11.0
    3. "car" [High Frequency]
       - Pattern 'ar' (r_controlled) requires skill 20.3
    4. "cost" [High Frequency]
       - Pattern 'st' (final_blend) requires skill 6.14
       - Pattern 'ost' (closed_syllable_exception) requires skill 12.1
    5. "call"
       - Pattern 'FLOSS' (special_rule) requires skill 7.0
       - Pattern 'all' (closed_syllable_exception) requires skill 12.1
    ... and 33 more violations

Skill 5.3 (skill_5.3_s-blends.csv):
  Total words: 50
  Violations: 38 (76.0%)
  Examples:
    1. "state" [High Frequency]
       - Pattern 'VCe' (magic_e) requires skill 11.0
    2. "still" [High Frequency]
       - Pattern 'FLOSS' (special_rule) requires skill 7.0
    3. "small" [High Frequency]
       - Pattern 'FLOSS' (special_rule) requires skill 7.0
       - Pattern 'all' (closed_syllable_exception) requires skill 12.1
    4. "study" [High Frequency]
       - Pattern 'y_as_vowel' (y_vowel) requires skill 14.1
    5. "stood" [High Frequency]
       - Pattern 'oo' (vowel_team) requires skill 18.7
    ... and 33 more violations

Skill 16.0 (skill_16.0_Silent_letters.csv):
  Total words: 50
  Violations: 36 (72.0%)
  Examples:
    1. "through" [High Frequency]
       - Pattern 'ough' (vowel_team) requires skill 18.10
       - Pattern 'ou' (vowel_team) requires skill 18.4
    2. "know" [High Frequency]
       - Pattern 'ow' (vowel_team) requires skill 18.3
    3. "might" [High Frequency]
       - Pattern 'igh' (vowel_team) requires skill 18.5
    4. "right" [High Frequency]
       - Pattern 'igh' (vowel_team) requires skill 18.5
    5. "thought" [High Frequency]
       - Pattern 'ough' (vowel_team) requires skill 18.10
       - Pattern 'ou' (vowel_team) requires skill 18.4
    ... and 31 more violations

Skill 18.3 (skill_18.3_Long_o_teams.csv):
  Total words: 50
  Violations: 31 (62.0%)
  Examples:
    1. "about" [High Frequency]
       - Pattern 'ou' (vowel_team) requires skill 18.4
    2. "through" [High Frequency]
       - Pattern 'ough' (vowel_team) requires skill 18.10
       - Pattern 'ou' (vowel_team) requires skill 18.4
    3. "house" [High Frequency]
       - Pattern 'ou' (vowel_team) requires skill 18.4
    4. "without" [High Frequency]
       - Pattern 'ou' (vowel_team) requires skill 18.4
    5. "around" [High Frequency]
       - Pattern 'ou' (vowel_team) requires skill 18.4
       - Pattern 'ar' (r_controlled) requires skill 20.3
    ... and 26 more violations

Skill 8.0 (skill_8.0_Backpack_rule.csv):
  Total words: 50
  Violations: 31 (62.0%)
  Examples:
    1. "without" [High Frequency]
       - Pattern 'ou' (vowel_team) requires skill 18.4
    2. "american" [High Frequency]
       - Pattern 'er' (r_controlled) requires skill 20.1
    3. "however" [High Frequency]
       - Pattern 'ow' (vowel_team) requires skill 18.3
       - Pattern 'er' (r_controlled) requires skill 20.1
    4. "although" [High Frequency]
       - Pattern 'ough' (vowel_team) requires skill 18.10
       - Pattern 'ou' (vowel_team) requires skill 18.4
       - Pattern 'gh' (silent_letter) requires skill 16.0
    5. "history" [High Frequency]
       - Pattern 'or' (r_controlled) requires skill 20.2
       - Pattern 'y_as_vowel' (y_vowel) requires skill 14.1
    ... and 26 more violations

Skill 18.1 (skill_18.1_Long_a_teams.csv):
  Total words: 42
  Violations: 30 (71.4%)
  Examples:
    1. "each" [High Frequency]
       - Pattern 'ea' (vowel_team) requires skill 18.2
    2. "great" [High Frequency]
       - Pattern 'ea' (vowel_team) requires skill 18.2
    3. "year" [High Frequency]
       - Pattern 'ea' (vowel_team) requires skill 18.2
       - Pattern 'ar' (r_controlled) requires skill 20.3
    4. "away" [High Frequency]
       - Pattern 'aw' (vowel_team) requires skill 18.10
    5. "eyes" [High Frequency]
       - Pattern 'ey' (vowel_team) requires skill 18.2
    ... and 25 more violations

Skill 14.1 (skill_14.1_Y_as_vowel.csv):
  Total words: 50
  Violations: 29 (58.0%)
  Examples:
    1. "may" [High Frequency]
       - Pattern 'ay' (vowel_team) requires skill 18.1
    2. "way" [High Frequency]
       - Pattern 'ay' (vowel_team) requires skill 18.1
    3. "very" [High Frequency]
       - Pattern 'er' (r_controlled) requires skill 20.1
    4. "day" [High Frequency]
       - Pattern 'ay' (vowel_team) requires skill 18.1
    5. "say" [High Frequency]
       - Pattern 'ay' (vowel_team) requires skill 18.1
    ... and 24 more violations

Skill 5.1 (skill_5.1_l-blends.csv):
  Total words: 50
  Violations: 29 (58.0%)
  Examples:
    1. "place" [High Frequency]
       - Pattern 'VCe' (magic_e) requires skill 11.0
       - Pattern 'ce' (soft_c) requires skill 13.2
    2. "close" [High Frequency]
       - Pattern 'VCe' (magic_e) requires skill 11.0
    3. "clear" [High Frequency]
       - Pattern 'ea' (vowel_team) requires skill 18.2
       - Pattern 'ar' (r_controlled) requires skill 20.3
    4. "class" [High Frequency]
       - Pattern 'FLOSS' (special_rule) requires skill 7.0
    5. "play" [High Frequency]
       - Pattern 'ay' (vowel_team) requires skill 18.1
       - Pattern 'y_as_vowel' (y_vowel) requires skill 14.1
    ... and 24 more violations



DETAILED VIOLATIONS BY SKILL:
====================================================================================================

Skill 1.2 - skill_1.2_CVC_short_e.csv:
--------------------------------------------------------------------------------

  Soft G violations (2):
    - "get": ge (needs skill 13.1)
    - "gem": ge (needs skill 13.1)

Skill 1.3 - skill_1.3_CVC_short_i.csv:
--------------------------------------------------------------------------------

  Soft G violations (1):
    - "gin": gi (needs skill 13.1)

Skill 10.0 - skill_10.0_Bridge_rule.csv:
--------------------------------------------------------------------------------

  R Controlled violations (1):
    - "ledger": er (needs skill 20.1)

  Soft G violations (22):
    - "bridge": ge (needs skill 13.1)
    - "judge": ge (needs skill 13.1)
    - "budget": ge (needs skill 13.1)
    - "edges": ge (needs skill 13.1)
    - "judges": ge (needs skill 13.1)
    - "lodge": ge (needs skill 13.1)
    - "ridge": ge (needs skill 13.1)
    - "judged": ge (needs skill 13.1)
    - "hodges": ge (needs skill 13.1)
    - "dodge": ge (needs skill 13.1)
    ... and 12 more

Skill 11.0 - skill_11.0_Magic_E.csv:
--------------------------------------------------------------------------------

  R Controlled violations (4):
    - "more": or (needs skill 20.2)
    - "more": ore (needs skill 20.2)
    - "here": er (needs skill 20.1)
    - "fire": ir (needs skill 20.1)

  Silent Letter violations (1):
    - "wrote": wr (needs skill 16.0)

  Soft C violations (3):
    - "place": ce (needs skill 13.2)
    - "face": ce (needs skill 13.2)
    - "space": ce (needs skill 13.2)

  Soft G violations (1):
    - "stage": ge (needs skill 13.1)

Skill 12.1 - skill_12.1_Closed_syllable_exceptions.csv:
--------------------------------------------------------------------------------

  R Controlled violations (5):
    - "older": er (needs skill 20.1)
    - "harold": ar (needs skill 20.3)
    - "holder": er (needs skill 20.1)
    - "arnold": ar (needs skill 20.3)
    - "foster": er (needs skill 20.1)

  Vowel Team violations (3):
    - "indeed": ee (needs skill 18.2)
    - "window": ow (needs skill 18.3)
    - "boost": oo (needs skill 18.7)

  Y Vowel violations (3):
    - "mostly": y_as_vowel (needs skill 14.1)
    - "wildly": y_as_vowel (needs skill 14.1)
    - "costly": y_as_vowel (needs skill 14.1)

Skill 13.1 - skill_13.1_Soft_g.csv:
--------------------------------------------------------------------------------

  R Controlled violations (20):
    - "general": er (needs skill 20.1)
    - "large": ar (needs skill 20.3)
    - "girl": ir (needs skill 20.1)
    - "longer": er (needs skill 20.1)
    - "girls": ir (needs skill 20.1)
    - "average": er (needs skill 20.1)
    - "george": or (needs skill 20.2)
    - "larger": ar (needs skill 20.3)
    - "larger": er (needs skill 20.1)
    - "charge": ar (needs skill 20.3)
    ... and 10 more

  Soft C violations (1):
    - "agency": cy (needs skill 13.2)

  Y Vowel violations (4):
    - "energy": y_as_vowel (needs skill 14.1)
    - "germany": y_as_vowel (needs skill 14.1)
    - "largely": y_as_vowel (needs skill 14.1)
    - "agency": y_as_vowel (needs skill 14.1)

Skill 13.2 - skill_13.2_Soft_c.csv:
--------------------------------------------------------------------------------

  R Controlled violations (11):
    - "service": er (needs skill 20.1)
    - "certain": er (needs skill 20.1)
    - "force": or (needs skill 20.2)
    - "center": er (needs skill 20.1)
    - "century": ur (needs skill 20.1)
    - "surface": ur (needs skill 20.1)
    - "forces": or (needs skill 20.2)
    - "officer": er (needs skill 20.1)
    - "concern": er (needs skill 20.1)
    - "source": ur (needs skill 20.1)
    ... and 1 more

  Vowel Team violations (12):
    - "certain": ai (needs skill 18.1)
    - "society": ie (needs skill 18.2)
    - "voice": oi (needs skill 18.11)
    - "peace": ea (needs skill 18.2)
    - "science": ie (needs skill 18.2)
    - "piece": ie (needs skill 18.2)
    - "choice": oi (needs skill 18.11)
    - "cities": ie (needs skill 18.2)
    - "council": ou (needs skill 18.4)
    - "source": ou (needs skill 18.4)
    ... and 2 more

  Y Vowel violations (4):
    - "city": y_as_vowel (needs skill 14.1)
    - "society": y_as_vowel (needs skill 14.1)
    - "policy": y_as_vowel (needs skill 14.1)
    - "century": y_as_vowel (needs skill 14.1)

Skill 14.1 - skill_14.1_Y_as_vowel.csv:
--------------------------------------------------------------------------------

  R Controlled violations (8):
    - "very": er (needs skill 20.1)
    - "every": er (needs skill 20.1)
    - "party": ar (needs skill 20.3)
    - "story": or (needs skill 20.2)
    - "nearly": ar (needs skill 20.3)
    - "merely": er (needs skill 20.1)
    - "army": ar (needs skill 20.3)
    - "theory": or (needs skill 20.2)

  Vowel Team violations (23):
    - "may": ay (needs skill 18.1)
    - "way": ay (needs skill 18.1)
    - "day": ay (needs skill 18.1)
    - "say": ay (needs skill 18.1)
    - "always": ay (needs skill 18.1)
    - "away": ay (needs skill 18.1)
    - "away": aw (needs skill 18.10)
    - "eyes": ey (needs skill 18.2)
    - "days": ay (needs skill 18.1)
    - "today": ay (needs skill 18.1)
    ... and 13 more

Skill 15.1 - skill_15.1_o_says_uh.csv:
--------------------------------------------------------------------------------

  R Controlled violations (3):
    - "person": er (needs skill 20.1)
    - "portion": or (needs skill 20.2)
    - "version": er (needs skill 20.1)

  Silent Letter violations (1):
    - "symbol": mb (needs skill 16.0)

  Vowel Team violations (10):
    - "school": oo (needs skill 18.7)
    - "question": ue (needs skill 18.6)
    - "reason": ea (needs skill 18.2)
    - "soon": oo (needs skill 18.7)
    - "reaction": ea (needs skill 18.2)
    - "pool": oo (needs skill 18.7)
    - "season": ea (needs skill 18.2)
    - "cool": oo (needs skill 18.7)
    - "moon": oo (needs skill 18.7)
    - "creation": ea (needs skill 18.2)

Skill 15.2 - skill_15.2_a_says_uh.csv:
--------------------------------------------------------------------------------

  R Controlled violations (14):
    - "general": er (needs skill 20.1)
    - "several": er (needs skill 20.1)
    - "federal": er (needs skill 20.1)
    - "personal": er (needs skill 20.1)
    - "material": er (needs skill 20.1)
    - "natural": ur (needs skill 20.1)
    - "moral": or (needs skill 20.2)
    - "normal": or (needs skill 20.2)
    - "original": or (needs skill 20.2)
    - "liberal": er (needs skill 20.1)
    ... and 4 more

  Silent Letter violations (1):
    - "signal": gn (needs skill 16.0)

  Vowel Team violations (6):
    - "real": ea (needs skill 18.2)
    - "deal": ea (needs skill 18.2)
    - "appeal": ea (needs skill 18.2)
    - "ideal": ea (needs skill 18.2)
    - "goal": oa (needs skill 18.3)
    - "royal": oy (needs skill 18.11)

Skill 15.3 - skill_15.3_e_says_uh.csv:
--------------------------------------------------------------------------------

  R Controlled violations (2):
    - "article": ar (needs skill 20.3)
    - "circle": ir (needs skill 20.1)

  Silent Letter violations (1):
    - "written": wr (needs skill 16.0)

  Vowel Team violations (7):
    - "been": ee (needs skill 18.2)
    - "between": ee (needs skill 18.2)
    - "seen": ee (needs skill 18.2)
    - "feel": ee (needs skill 18.2)
    - "trouble": ou (needs skill 18.4)
    - "couple": ou (needs skill 18.4)
    - "green": ee (needs skill 18.2)

Skill 16.0 - skill_16.0_Silent_letters.csv:
--------------------------------------------------------------------------------

  R Controlled violations (11):
    - "number": er (needs skill 20.1)
    - "members": er (needs skill 20.1)
    - "perhaps": er (needs skill 20.1)
    - "higher": er (needs skill 20.1)
    - "foreign": or (needs skill 20.2)
    - "foreign": ore (needs skill 20.2)
    - "member": er (needs skill 20.1)
    - "numbers": er (needs skill 20.1)
    - "corps": or (needs skill 20.2)
    - "writer": er (needs skill 20.1)
    ... and 1 more

  Vowel Team violations (41):
    - "through": ough (needs skill 18.10)
    - "through": ou (needs skill 18.4)
    - "know": ow (needs skill 18.3)
    - "might": igh (needs skill 18.5)
    - "right": igh (needs skill 18.5)
    - "thought": ough (needs skill 18.10)
    - "thought": ou (needs skill 18.4)
    - "high": igh (needs skill 18.5)
    - "though": ough (needs skill 18.10)
    - "though": ou (needs skill 18.4)
    ... and 31 more

Skill 17.0 - skill_17.0_3_sounds_of_ed.csv:
--------------------------------------------------------------------------------

  R Controlled violations (14):
    - "turned": ur (needs skill 20.1)
    - "started": ar (needs skill 20.3)
    - "required": ir (needs skill 20.1)
    - "appeared": ar (needs skill 20.3)
    - "worked": wor (needs skill 20.5)
    - "worked": or (needs skill 20.2)
    - "carried": ar (needs skill 20.3)
    - "reported": or (needs skill 20.2)
    - "served": er (needs skill 20.1)
    - "learned": ar (needs skill 20.3)
    ... and 4 more

  Vowel Team violations (19):
    - "looked": oo (needs skill 18.7)
    - "need": ee (needs skill 18.2)
    - "seemed": ee (needs skill 18.2)
    - "needed": ee (needs skill 18.2)
    - "followed": ow (needs skill 18.3)
    - "tried": ie (needs skill 18.2)
    - "reached": ea (needs skill 18.2)
    - "received": ei (needs skill 18.1)
    - "indeed": ee (needs skill 18.2)
    - "showed": ow (needs skill 18.3)
    ... and 9 more

Skill 18.10 - skill_18.10_aw_sound.csv:
--------------------------------------------------------------------------------

  R Controlled violations (15):
    - "aware": war (needs skill 20.4)
    - "aware": ar (needs skill 20.3)
    - "daughter": er (needs skill 20.1)
    - "author": or (needs skill 20.2)
    - "award": war (needs skill 20.4)
    - "award": ar (needs skill 20.3)
    - "bureau": ur (needs skill 20.1)
    - "lawyer": er (needs skill 20.1)
    - "delaware": war (needs skill 20.4)
    - "delaware": ar (needs skill 20.3)
    ... and 5 more

Skill 18.11 - skill_18.11_oy_sound.csv:
--------------------------------------------------------------------------------

  R Controlled violations (3):
    - "heroic": er (needs skill 20.1)
    - "employer": er (needs skill 20.1)
    - "turmoil": ur (needs skill 20.1)

Skill 18.12 - skill_18.12_ea_says_e.csv:
--------------------------------------------------------------------------------

  R Controlled violations (18):
    - "years": ar (needs skill 20.3)
    - "year": ar (needs skill 20.3)
    - "area": ar (needs skill 20.3)
    - "areas": ar (needs skill 20.3)
    - "clear": ar (needs skill 20.3)
    - "near": ar (needs skill 20.3)
    - "greater": er (needs skill 20.1)
    - "research": ar (needs skill 20.3)
    - "hear": ar (needs skill 20.3)
    - "earlier": ar (needs skill 20.3)
    ... and 8 more

Skill 18.1 - skill_18.1_Long_a_teams.csv:
--------------------------------------------------------------------------------

  R Controlled violations (11):
    - "year": ar (needs skill 20.3)
    - "area": ar (needs skill 20.3)
    - "certain": er (needs skill 20.1)
    - "either": er (needs skill 20.1)
    - "areas": ar (needs skill 20.3)
    - "clear": ar (needs skill 20.3)
    - "near": ar (needs skill 20.3)
    - "greater": er (needs skill 20.1)
    - "research": ar (needs skill 20.3)
    - "foreign": or (needs skill 20.2)
    ... and 1 more

  Vowel Team violations (27):
    - "each": ea (needs skill 18.2)
    - "great": ea (needs skill 18.2)
    - "year": ea (needs skill 18.2)
    - "away": aw (needs skill 18.10)
    - "eyes": ey (needs skill 18.2)
    - "least": ea (needs skill 18.2)
    - "area": ea (needs skill 18.2)
    - "means": ea (needs skill 18.2)
    - "really": ea (needs skill 18.2)
    - "already": ea (needs skill 18.2)
    ... and 17 more

Skill 18.2 - skill_18.2_Long_e_teams.csv:
--------------------------------------------------------------------------------

  R Controlled violations (8):
    - "years": ar (needs skill 20.3)
    - "year": ar (needs skill 20.3)
    - "area": ar (needs skill 20.3)
    - "either": er (needs skill 20.1)
    - "areas": ar (needs skill 20.3)
    - "clear": ar (needs skill 20.3)
    - "near": ar (needs skill 20.3)
    - "greater": er (needs skill 20.1)

  Vowel Team violations (1):
    - "view": ew (needs skill 18.6)

Skill 18.3 - skill_18.3_Long_o_teams.csv:
--------------------------------------------------------------------------------

  R Controlled violations (11):
    - "around": ar (needs skill 20.3)
    - "however": er (needs skill 20.1)
    - "course": ur (needs skill 20.1)
    - "four": ur (needs skill 20.1)
    - "power": er (needs skill 20.1)
    - "board": ar (needs skill 20.3)
    - "court": ur (needs skill 20.1)
    - "various": ar (needs skill 20.3)
    - "hours": ur (needs skill 20.1)
    - "hour": ur (needs skill 20.1)
    ... and 1 more

  Vowel Team violations (33):
    - "about": ou (needs skill 18.4)
    - "through": ough (needs skill 18.10)
    - "through": ou (needs skill 18.4)
    - "house": ou (needs skill 18.4)
    - "without": ou (needs skill 18.4)
    - "around": ou (needs skill 18.4)
    - "found": ou (needs skill 18.4)
    - "course": ou (needs skill 18.4)
    - "though": ough (needs skill 18.10)
    - "though": ou (needs skill 18.4)
    ... and 23 more

Skill 18.4 - skill_18.4_ow_sound.csv:
--------------------------------------------------------------------------------

  R Controlled violations (10):
    - "around": ar (needs skill 20.3)
    - "however": er (needs skill 20.1)
    - "course": ur (needs skill 20.1)
    - "four": ur (needs skill 20.1)
    - "power": er (needs skill 20.1)
    - "court": ur (needs skill 20.1)
    - "various": ar (needs skill 20.3)
    - "hours": ur (needs skill 20.1)
    - "hour": ur (needs skill 20.1)
    - "southern": er (needs skill 20.1)

  Vowel Team violations (5):
    - "through": ough (needs skill 18.10)
    - "though": ough (needs skill 18.10)
    - "enough": ough (needs skill 18.10)
    - "although": ough (needs skill 18.10)
    - "brought": ough (needs skill 18.10)

Skill 18.5 - skill_18.5_Long_i_teams.csv:
--------------------------------------------------------------------------------

  R Controlled violations (11):
    - "either": er (needs skill 20.1)
    - "higher": er (needs skill 20.1)
    - "foreign": or (needs skill 20.2)
    - "foreign": ore (needs skill 20.2)
    - "earlier": ar (needs skill 20.3)
    - "earlier": er (needs skill 20.1)
    - "neither": er (needs skill 20.1)
    - "series": er (needs skill 20.1)
    - "carried": ar (needs skill 20.3)
    - "married": ar (needs skill 20.3)
    ... and 1 more

  Vowel Team violations (2):
    - "view": ew (needs skill 18.6)
    - "audience": au (needs skill 18.10)

Skill 18.6 - skill_18.6_Long_u_teams.csv:
--------------------------------------------------------------------------------

  R Controlled violations (7):
    - "europe": ur (needs skill 20.1)
    - "european": ur (needs skill 20.1)
    - "fewer": er (needs skill 20.1)
    - "virtue": ir (needs skill 20.1)
    - "argue": ar (needs skill 20.3)
    - "argued": ar (needs skill 20.3)
    - "newport": or (needs skill 20.2)

Skill 18.7 - skill_18.7_Long_oo.csv:
--------------------------------------------------------------------------------

  R Controlled violations (8):
    - "around": ar (needs skill 20.3)
    - "course": ur (needs skill 20.1)
    - "four": ur (needs skill 20.1)
    - "door": or (needs skill 20.2)
    - "court": ur (needs skill 20.1)
    - "various": ar (needs skill 20.3)
    - "required": ir (needs skill 20.1)
    - "hours": ur (needs skill 20.1)

  Vowel Team violations (4):
    - "though": ough (needs skill 18.10)
    - "enough": ough (needs skill 18.10)
    - "although": ough (needs skill 18.10)
    - "brought": ough (needs skill 18.10)

Skill 18.8 - skill_18.8_Short_oo.csv:
--------------------------------------------------------------------------------

  R Controlled violations (4):
    - "door": or (needs skill 20.2)
    - "floor": or (needs skill 20.2)
    - "doors": or (needs skill 20.2)
    - "outdoor": or (needs skill 20.2)

Skill 18.9 - skill_18.9_ou_says_u.csv:
--------------------------------------------------------------------------------

  R Controlled violations (11):
    - "around": ar (needs skill 20.3)
    - "course": ur (needs skill 20.1)
    - "four": ur (needs skill 20.1)
    - "court": ur (needs skill 20.1)
    - "various": ar (needs skill 20.3)
    - "hours": ur (needs skill 20.1)
    - "hour": ur (needs skill 20.1)
    - "southern": er (needs skill 20.1)
    - "serious": er (needs skill 20.1)
    - "source": ur (needs skill 20.1)
    ... and 1 more

  Vowel Team violations (4):
    - "though": ough (needs skill 18.10)
    - "enough": ough (needs skill 18.10)
    - "although": ough (needs skill 18.10)
    - "brought": ough (needs skill 18.10)

Skill 19.0 - skill_19.0_Homophones.csv:
--------------------------------------------------------------------------------

  R Controlled violations (2):
    - "flower": er (needs skill 20.1)
    - "flour": ur (needs skill 20.1)

Skill 2.3 - skill_2.3_CV_long_i.csv:
--------------------------------------------------------------------------------

  Y Vowel violations (2):
    - "by": y_as_vowel (needs skill 14.1)
    - "my": y_as_vowel (needs skill 14.1)

Skill 20.1 - skill_20.1_er_sound.csv:
--------------------------------------------------------------------------------

  R Controlled violations (1):
    - "order": or (needs skill 20.2)

Skill 20.2 - skill_20.2_or_sound.csv:
--------------------------------------------------------------------------------

  R Controlled violations (6):
    - "words": wor (needs skill 20.5)
    - "working": wor (needs skill 20.5)
    - "works": wor (needs skill 20.5)
    - "worked": wor (needs skill 20.5)
    - "forward": war (needs skill 20.4)
    - "forward": ar (needs skill 20.3)

Skill 3.1 - skill_3.1_c_before_a,o,u.csv:
--------------------------------------------------------------------------------

  Closed Syllable Exception violations (4):
    - "cost": ost (needs skill 12.1)
    - "call": all (needs skill 12.1)
    - "cold": old (needs skill 12.1)
    - "colt": olt (needs skill 12.1)

  Digraph violations (1):
    - "cash": sh (needs skill 4.2)

  Final Blend violations (7):
    - "cost": st (needs skill 6.14)
    - "cold": ld (needs skill 6.5)
    - "camp": mp (needs skill 6.10)
    - "cast": st (needs skill 6.14)
    - "calm": lm (needs skill 6.8)
    - "colt": lt (needs skill 6.4)
    - "calf": lf (needs skill 6.6)

  Magic E violations (12):
    - "came": VCe (needs skill 11.0)
    - "case": VCe (needs skill 11.0)
    - "care": VCe (needs skill 11.0)
    - "core": VCe (needs skill 11.0)
    - "code": VCe (needs skill 11.0)
    - "cure": VCe (needs skill 11.0)
    - "cope": VCe (needs skill 11.0)
    - "cafe": VCe (needs skill 11.0)
    - "cape": VCe (needs skill 11.0)
    - "cake": VCe (needs skill 11.0)
    ... and 2 more

  R Controlled violations (11):
    - "car": ar (needs skill 20.3)
    - "care": ar (needs skill 20.3)
    - "cars": ar (needs skill 20.3)
    - "core": or (needs skill 20.2)
    - "core": ore (needs skill 20.2)
    - "carl": ar (needs skill 20.3)
    - "corn": or (needs skill 20.2)
    - "curt": ur (needs skill 20.1)
    - "cure": ur (needs skill 20.1)
    - "card": ar (needs skill 20.3)
    ... and 1 more

  Silent Letter violations (2):
    - "cops": ps (needs skill 16.0)
    - "cups": ps (needs skill 16.0)

  Special Rule violations (1):
    - "call": FLOSS (needs skill 7.0)

  Vowel Team violations (6):
    - "cool": oo (needs skill 18.7)
    - "cook": oo (needs skill 18.7)
    - "coat": oa (needs skill 18.3)
    - "coal": oa (needs skill 18.3)
    - "cow": ow (needs skill 18.3)
    - "cows": ow (needs skill 18.3)

  Y Vowel violations (2):
    - "copy": y_as_vowel (needs skill 14.1)
    - "cady": y_as_vowel (needs skill 14.1)

Skill 3.2 - skill_3.2_k_before_e,i,y.csv:
--------------------------------------------------------------------------------

  Closed Syllable Exception violations (1):
    - "kind": ind (needs skill 12.1)

  Digraph violations (1):
    - "kick": ck (needs skill 4.5)

  Final Blend violations (3):
    - "kind": nd (needs skill 6.2)
    - "king": ng (needs skill 6.12)
    - "kent": nt (needs skill 6.1)

  R Controlled violations (2):
    - "kern": er (needs skill 20.1)
    - "kerr": er (needs skill 20.1)

  Special Rule violations (1):
    - "kiss": FLOSS (needs skill 7.0)

  Vowel Team violations (5):
    - "keep": ee (needs skill 18.2)
    - "key": ey (needs skill 18.2)
    - "keys": ey (needs skill 18.2)
    - "keen": ee (needs skill 18.2)
    - "keel": ee (needs skill 18.2)

  Y Vowel violations (1):
    - "key": y_as_vowel (needs skill 14.1)

Skill 4.1 - skill_4.1_ch_digraph.csv:
--------------------------------------------------------------------------------

  Digraph violations (2):
    - "itch": tch (needs skill 9.0)
    - "which": wh (needs skill 4.4)

  R Controlled violations (1):
    - "arch": ar (needs skill 20.3)

  Vowel Team violations (1):
    - "each": ea (needs skill 18.2)

Skill 4.2 - skill_4.2_sh_digraph.csv:
--------------------------------------------------------------------------------

  Vowel Team violations (3):
    - "show": ow (needs skill 18.3)
    - "shea": ea (needs skill 18.2)
    - "shaw": aw (needs skill 18.10)

  Y Vowel violations (1):
    - "shy": y_as_vowel (needs skill 14.1)

Skill 4.3 - skill_4.3_th_digraph.csv:
--------------------------------------------------------------------------------

  Initial Blend violations (1):
    - "thru": thr (needs skill 5.4)

  R Controlled violations (1):
    - "thor": or (needs skill 20.2)

  Vowel Team violations (4):
    - "thee": ee (needs skill 18.2)
    - "thou": ou (needs skill 18.4)
    - "oath": oa (needs skill 18.3)
    - "thaw": aw (needs skill 18.10)

  Y Vowel violations (1):
    - "thy": y_as_vowel (needs skill 14.1)

Skill 4.4 - skill_4.4_wh_digraph.csv:
--------------------------------------------------------------------------------

  Magic E violations (2):
    - "while": VCe (needs skill 11.0)
    - "white": VCe (needs skill 11.0)

  Y Vowel violations (1):
    - "why": y_as_vowel (needs skill 14.1)

Skill 5.1 - skill_5.1_l-blends.csv:
--------------------------------------------------------------------------------

  Closed Syllable Exception violations (1):
    - "blind": ind (needs skill 12.1)

  Final Blend violations (2):
    - "plant": nt (needs skill 6.1)
    - "blind": nd (needs skill 6.2)

  Magic E violations (6):
    - "place": VCe (needs skill 11.0)
    - "close": VCe (needs skill 11.0)
    - "plane": VCe (needs skill 11.0)
    - "blame": VCe (needs skill 11.0)
    - "slave": VCe (needs skill 11.0)
    - "plate": VCe (needs skill 11.0)

  R Controlled violations (5):
    - "clear": ar (needs skill 20.3)
    - "floor": or (needs skill 20.2)
    - "clark": ar (needs skill 20.3)
    - "clerk": er (needs skill 20.1)
    - "glory": or (needs skill 20.2)

  Soft C violations (1):
    - "place": ce (needs skill 13.2)

  Special Rule violations (2):
    - "class": FLOSS (needs skill 7.0)
    - "glass": FLOSS (needs skill 7.0)

  Vowel Team violations (16):
    - "clear": ea (needs skill 18.2)
    - "play": ay (needs skill 18.1)
    - "floor": oo (needs skill 18.7)
    - "blue": ue (needs skill 18.6)
    - "clay": ay (needs skill 18.1)
    - "claim": ai (needs skill 18.1)
    - "clean": ea (needs skill 18.2)
    - "flow": ow (needs skill 18.3)
    - "plays": ay (needs skill 18.1)
    - "sleep": ee (needs skill 18.2)
    ... and 6 more

  Y Vowel violations (3):
    - "play": y_as_vowel (needs skill 14.1)
    - "clay": y_as_vowel (needs skill 14.1)
    - "glory": y_as_vowel (needs skill 14.1)

Skill 5.2 - skill_5.2_r-blends.csv:
--------------------------------------------------------------------------------

  Final Blend violations (6):
    - "bring": ng (needs skill 6.12)
    - "drink": nk (needs skill 6.3)
    - "frank": nk (needs skill 6.3)
    - "trust": st (needs skill 6.14)
    - "grand": nd (needs skill 6.2)
    - "grant": nt (needs skill 6.1)

  Magic E violations (7):
    - "trade": VCe (needs skill 11.0)
    - "price": VCe (needs skill 11.0)
    - "drive": VCe (needs skill 11.0)
    - "frame": VCe (needs skill 11.0)
    - "broke": VCe (needs skill 11.0)
    - "drove": VCe (needs skill 11.0)
    - "prove": VCe (needs skill 11.0)

  Soft C violations (1):
    - "price": ce (needs skill 13.2)

  Special Rule violations (5):
    - "press": FLOSS (needs skill 7.0)
    - "dress": FLOSS (needs skill 7.0)
    - "gross": FLOSS (needs skill 7.0)
    - "cross": FLOSS (needs skill 7.0)
    - "grass": FLOSS (needs skill 7.0)

  Vowel Team violations (23):
    - "great": ea (needs skill 18.2)
    - "group": ou (needs skill 18.4)
    - "free": ee (needs skill 18.2)
    - "true": ue (needs skill 18.6)
    - "brown": ow (needs skill 18.3)
    - "tried": ie (needs skill 18.2)
    - "green": ee (needs skill 18.2)
    - "trees": ee (needs skill 18.2)
    - "break": ea (needs skill 18.2)
    - "broad": oa (needs skill 18.3)
    ... and 13 more

  Y Vowel violations (2):
    - "gray": y_as_vowel (needs skill 14.1)
    - "truly": y_as_vowel (needs skill 14.1)

Skill 5.3 - skill_5.3_s-blends.csv:
--------------------------------------------------------------------------------

  Closed Syllable Exception violations (1):
    - "small": all (needs skill 12.1)

  Final Blend violations (5):
    - "stand": nd (needs skill 6.2)
    - "spent": nt (needs skill 6.1)
    - "spend": nd (needs skill 6.2)
    - "swung": ng (needs skill 6.12)
    - "swift": ft (needs skill 6.15)

  Initial Blend violations (2):
    - "split": spl (needs skill 5.4)
    - "strip": str (needs skill 5.4)

  Magic E violations (13):
    - "state": VCe (needs skill 11.0)
    - "space": VCe (needs skill 11.0)
    - "stage": VCe (needs skill 11.0)
    - "scene": VCe (needs skill 11.0)
    - "spoke": VCe (needs skill 11.0)
    - "store": VCe (needs skill 11.0)
    - "score": VCe (needs skill 11.0)
    - "scale": VCe (needs skill 11.0)
    - "smile": VCe (needs skill 11.0)
    - "stone": VCe (needs skill 11.0)
    ... and 3 more

  R Controlled violations (7):
    - "start": ar (needs skill 20.3)
    - "story": or (needs skill 20.2)
    - "store": or (needs skill 20.2)
    - "store": ore (needs skill 20.2)
    - "score": or (needs skill 20.2)
    - "score": ore (needs skill 20.2)
    - "stars": ar (needs skill 20.3)

  Silent Letter violations (1):
    - "steps": ps (needs skill 16.0)

  Soft C violations (2):
    - "space": ce (needs skill 13.2)
    - "scene": ce (needs skill 13.2)

  Soft G violations (1):
    - "stage": ge (needs skill 13.1)

  Special Rule violations (6):
    - "still": FLOSS (needs skill 7.0)
    - "small": FLOSS (needs skill 7.0)
    - "staff": FLOSS (needs skill 7.0)
    - "skill": FLOSS (needs skill 7.0)
    - "smell": FLOSS (needs skill 7.0)
    - "stuff": FLOSS (needs skill 7.0)

  Vowel Team violations (7):
    - "stood": oo (needs skill 18.7)
    - "stay": ay (needs skill 18.1)
    - "speak": ea (needs skill 18.2)
    - "speed": ee (needs skill 18.2)
    - "sweet": ee (needs skill 18.2)
    - "snow": ow (needs skill 18.3)
    - "steel": ee (needs skill 18.2)

  Y Vowel violations (3):
    - "study": y_as_vowel (needs skill 14.1)
    - "story": y_as_vowel (needs skill 14.1)
    - "stay": y_as_vowel (needs skill 14.1)

Skill 5.4 - skill_5.4_3-letter_blends.csv:
--------------------------------------------------------------------------------

  Closed Syllable Exception violations (1):
    - "squall": all (needs skill 12.1)

  Final Blend violations (7):
    - "strong": ng (needs skill 6.12)
    - "spring": ng (needs skill 6.12)
    - "thrust": st (needs skill 6.14)
    - "string": ng (needs skill 6.12)
    - "sprang": ng (needs skill 6.12)
    - "sprung": ng (needs skill 6.12)
    - "strand": nd (needs skill 6.2)

  Magic E violations (8):
    - "square": VCe (needs skill 11.0)
    - "strike": VCe (needs skill 11.0)
    - "stroke": VCe (needs skill 11.0)
    - "stride": VCe (needs skill 11.0)
    - "strode": VCe (needs skill 11.0)
    - "strive": VCe (needs skill 11.0)
    - "strife": VCe (needs skill 11.0)
    - "spruce": VCe (needs skill 11.0)

  R Controlled violations (1):
    - "square": ar (needs skill 20.3)

  Silent Letter violations (1):
    - "strips": ps (needs skill 16.0)

  Soft C violations (1):
    - "spruce": ce (needs skill 13.2)

  Special Rule violations (2):
    - "stress": FLOSS (needs skill 7.0)
    - "squall": FLOSS (needs skill 7.0)

  Vowel Team violations (22):
    - "three": ee (needs skill 18.2)
    - "street": ee (needs skill 18.2)
    - "stream": ea (needs skill 18.2)
    - "throat": oa (needs skill 18.3)
    - "screen": ee (needs skill 18.2)
    - "threw": ew (needs skill 18.6)
    - "threat": ea (needs skill 18.2)
    - "throw": ow (needs skill 18.3)
    - "thrown": ow (needs skill 18.3)
    - "strain": ai (needs skill 18.1)
    ... and 12 more

  Y Vowel violations (2):
    - "spray": y_as_vowel (needs skill 14.1)
    - "stray": y_as_vowel (needs skill 14.1)

Skill 6.11 - skill_6.11_nd_ending.csv:
--------------------------------------------------------------------------------

  Closed Syllable Exception violations (7):
    - "find": ind (needs skill 12.1)
    - "mind": ind (needs skill 12.1)
    - "kind": ind (needs skill 12.1)
    - "wind": ind (needs skill 12.1)
    - "blind": ind (needs skill 12.1)
    - "bind": ind (needs skill 12.1)
    - "hind": ind (needs skill 12.1)

  Vowel Team violations (9):
    - "found": ou (needs skill 18.4)
    - "sound": ou (needs skill 18.4)
    - "round": ou (needs skill 18.4)
    - "bound": ou (needs skill 18.4)
    - "pound": ou (needs skill 18.4)
    - "wound": ou (needs skill 18.4)
    - "mound": ou (needs skill 18.4)
    - "hound": ou (needs skill 18.4)
    - "fiend": ie (needs skill 18.2)

Skill 6.12 - skill_6.12_ng_ending.csv:
--------------------------------------------------------------------------------

  Silent Letter violations (1):
    - "wrong": wr (needs skill 16.0)

  Soft G violations (1):
    - "aging": gi (needs skill 13.1)

  Vowel Team violations (5):
    - "being": ei (needs skill 18.1)
    - "going": oi (needs skill 18.11)
    - "young": ou (needs skill 18.4)
    - "doing": oi (needs skill 18.11)
    - "owing": ow (needs skill 18.3)

Skill 6.14 - skill_6.14_st_ending.csv:
--------------------------------------------------------------------------------

  Closed Syllable Exception violations (7):
    - "most": ost (needs skill 12.1)
    - "cost": ost (needs skill 12.1)
    - "lost": ost (needs skill 12.1)
    - "post": ost (needs skill 12.1)
    - "host": ost (needs skill 12.1)
    - "boost": ost (needs skill 12.1)
    - "frost": ost (needs skill 12.1)

  R Controlled violations (5):
    - "first": ir (needs skill 20.1)
    - "worst": wor (needs skill 20.5)
    - "worst": or (needs skill 20.2)
    - "burst": ur (needs skill 20.1)
    - "ernst": er (needs skill 20.1)

  Silent Letter violations (1):
    - "wrist": wr (needs skill 16.0)

  Vowel Team violations (12):
    - "least": ea (needs skill 18.2)
    - "east": ea (needs skill 18.2)
    - "coast": oa (needs skill 18.3)
    - "guest": ue (needs skill 18.6)
    - "toast": oa (needs skill 18.3)
    - "quest": ue (needs skill 18.6)
    - "boost": oo (needs skill 18.7)
    - "moist": oi (needs skill 18.11)
    - "waist": ai (needs skill 18.1)
    - "roast": oa (needs skill 18.3)
    ... and 2 more

Skill 6.15 - skill_6.15_ft_ending.csv:
--------------------------------------------------------------------------------

  Soft G violations (1):
    - "gift": gi (needs skill 13.1)

Skill 6.1 - skill_6.1_nt_ending.csv:
--------------------------------------------------------------------------------

  R Controlled violations (1):
    - "burnt": ur (needs skill 20.1)

  Soft C violations (2):
    - "cent": ce (needs skill 13.2)
    - "scent": ce (needs skill 13.2)

  Soft G violations (2):
    - "agent": ge (needs skill 13.1)
    - "giant": gi (needs skill 13.1)

  Vowel Team violations (11):
    - "point": oi (needs skill 18.11)
    - "count": ou (needs skill 18.4)
    - "joint": oi (needs skill 18.11)
    - "paint": ai (needs skill 18.1)
    - "mount": ou (needs skill 18.4)
    - "faint": ai (needs skill 18.1)
    - "aunt": au (needs skill 18.10)
    - "saint": ai (needs skill 18.1)
    - "gaunt": au (needs skill 18.10)
    - "haunt": au (needs skill 18.10)
    ... and 1 more

Skill 6.2 - skill_6.2_nd_ending.csv:
--------------------------------------------------------------------------------

  Closed Syllable Exception violations (7):
    - "find": ind (needs skill 12.1)
    - "mind": ind (needs skill 12.1)
    - "kind": ind (needs skill 12.1)
    - "wind": ind (needs skill 12.1)
    - "blind": ind (needs skill 12.1)
    - "bind": ind (needs skill 12.1)
    - "hind": ind (needs skill 12.1)

  Vowel Team violations (9):
    - "found": ou (needs skill 18.4)
    - "sound": ou (needs skill 18.4)
    - "round": ou (needs skill 18.4)
    - "bound": ou (needs skill 18.4)
    - "pound": ou (needs skill 18.4)
    - "wound": ou (needs skill 18.4)
    - "mound": ou (needs skill 18.4)
    - "hound": ou (needs skill 18.4)
    - "fiend": ie (needs skill 18.2)

Skill 6.4 - skill_6.4_lt_ending.csv:
--------------------------------------------------------------------------------

  Closed Syllable Exception violations (3):
    - "colt": olt (needs skill 12.1)
    - "bolt": olt (needs skill 12.1)
    - "jolt": olt (needs skill 12.1)

  Silent Letter violations (1):
    - "knelt": kn (needs skill 16.0)

  Soft G violations (1):
    - "gilt": gi (needs skill 13.1)

  Vowel Team violations (2):
    - "dealt": ea (needs skill 18.2)
    - "fault": au (needs skill 18.10)

Skill 6.5 - skill_6.5_ld_ending.csv:
--------------------------------------------------------------------------------

  Closed Syllable Exception violations (14):
    - "told": old (needs skill 12.1)
    - "child": ild (needs skill 12.1)
    - "cold": old (needs skill 12.1)
    - "hold": old (needs skill 12.1)
    - "build": ild (needs skill 12.1)
    - "wild": ild (needs skill 12.1)
    - "gold": old (needs skill 12.1)
    - "sold": old (needs skill 12.1)
    - "mold": old (needs skill 12.1)
    - "bold": old (needs skill 12.1)
    ... and 4 more

  Vowel Team violations (4):
    - "would": ou (needs skill 18.4)
    - "could": ou (needs skill 18.4)
    - "field": ie (needs skill 18.2)
    - "yield": ie (needs skill 18.2)

Skill 6.7 - skill_6.7_lk_ending.csv:
--------------------------------------------------------------------------------

  Closed Syllable Exception violations (1):
    - "chalk": alk (needs skill 12.1)

Skill 6.8 - skill_6.8_lm_ending.csv:
--------------------------------------------------------------------------------

  Silent Letter violations (1):
    - "psalm": ps (needs skill 16.0)

  Vowel Team violations (1):
    - "realm": ea (needs skill 18.2)

Skill 7.0 - skill_7.0_FLOSS_rule.csv:
--------------------------------------------------------------------------------

  Closed Syllable Exception violations (4):
    - "shall": all (needs skill 12.1)
    - "call": all (needs skill 12.1)
    - "calls": all (needs skill 12.1)
    - "walls": all (needs skill 12.1)

  R Controlled violations (1):
    - "effort": or (needs skill 20.2)

  Soft C violations (2):
    - "cells": ce (needs skill 13.2)
    - "cell": ce (needs skill 13.2)

  Vowel Team violations (1):
    - "issue": ue (needs skill 18.6)

Skill 8.0 - skill_8.0_Backpack_rule.csv:
--------------------------------------------------------------------------------

  Closed Syllable Exception violations (2):
    - "finally": all (needs skill 12.1)
    - "building": ild (needs skill 12.1)

  Magic E violations (8):
    - "provide": VCe (needs skill 11.0)
    - "outside": VCe (needs skill 11.0)
    - "therefore": VCe (needs skill 11.0)
    - "surface": VCe (needs skill 11.0)
    - "increase": VCe (needs skill 11.0)
    - "pressure": VCe (needs skill 11.0)
    - "picture": VCe (needs skill 11.0)
    - "purpose": VCe (needs skill 11.0)

  R Controlled violations (19):
    - "american": er (needs skill 20.1)
    - "however": er (needs skill 20.1)
    - "history": or (needs skill 20.2)
    - "necessary": ar (needs skill 20.3)
    - "morning": or (needs skill 20.2)
    - "therefore": er (needs skill 20.1)
    - "therefore": or (needs skill 20.2)
    - "therefore": ore (needs skill 20.2)
    - "surface": ur (needs skill 20.1)
    - "america": er (needs skill 20.1)
    ... and 9 more

  Silent Letter violations (1):
    - "although": gh (needs skill 16.0)

  Soft C violations (2):
    - "necessary": ce (needs skill 13.2)
    - "surface": ce (needs skill 13.2)

  Soft G violations (2):
    - "getting": ge (needs skill 13.1)
    - "beginning": gi (needs skill 13.1)

  Vowel Team violations (12):
    - "without": ou (needs skill 18.4)
    - "however": ow (needs skill 18.3)
    - "although": ough (needs skill 18.10)
    - "although": ou (needs skill 18.4)
    - "available": ai (needs skill 18.1)
    - "outside": ou (needs skill 18.4)
    - "increase": ea (needs skill 18.2)
    - "looking": oo (needs skill 18.7)
    - "feeling": ee (needs skill 18.2)
    - "meeting": ee (needs skill 18.2)
    ... and 2 more

  Y Vowel violations (4):
    - "history": y_as_vowel (needs skill 14.1)
    - "probably": y_as_vowel (needs skill 14.1)
    - "necessary": y_as_vowel (needs skill 14.1)
    - "finally": y_as_vowel (needs skill 14.1)

