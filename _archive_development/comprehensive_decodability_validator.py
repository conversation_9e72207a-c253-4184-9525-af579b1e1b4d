#!/usr/bin/env python3
"""
Comprehensive Decodability Validator for Orton-Gillingham Word Lists

This validator ensures that EVERY phonics element in a word has been taught
before the word is introduced. No more "stood" in skill 5!
"""

import pandas as pd
import os
import re
from collections import defaultdict
import json

class ComprehensiveDecodabilityValidator:
    def __init__(self):
        self.word_lists_dir = '/Users/<USER>/word-generator/word_lists'
        self.heart_words_path = '/Users/<USER>/word-generator/heart_words/heart_words_reference_by_skill.csv'
        
        # Load heart words
        self.heart_words_by_skill = self._load_heart_words()
        
        # Define comprehensive skill sequence
        self.skill_sequence = self._define_skill_sequence()
        
        # Define what patterns are taught at each skill
        self.patterns_by_skill = self._define_patterns_by_skill()
        
    def _load_heart_words(self):
        """Load heart words organized by introduction skill"""
        heart_words = defaultdict(set)
        if os.path.exists(self.heart_words_path):
            df = pd.read_csv(self.heart_words_path)
            for _, row in df.iterrows():
                word = row['word'].lower()
                skill = str(row['skill_introduction'])
                heart_words[skill].add(word)
        return heart_words
    
    def _define_skill_sequence(self):
        """Define the exact order skills are taught"""
        # This should match your actual curriculum sequence
        return [
            # Level 1: CVC
            '1.1', '1.2', '1.3', '1.4', '1.5',
            # Level 2: CV Open syllables  
            '2.1', '2.2', '2.3',
            # Level 3: C/K rule
            '3.1', '3.2',
            # Level 4: Digraphs
            '4.1', '4.2', '4.3', '4.4', '4.5',
            # Level 5: Initial blends
            '5.1', '5.2', '5.3', '5.4',
            # Level 6: Final blends
            '6.1', '6.2', '6.3', '6.4', '6.5', '6.6', '6.7', '6.8', 
            '6.9', '6.10', '6.11', '6.12', '6.13', '6.14', '6.15',
            # Level 7-10: Special rules
            '7.0', '8.0', '9.0', '10.0',
            # Level 11: Magic E
            '11.0',
            # Level 12: Closed syllable exceptions
            '12.1',
            # Level 13: Soft sounds
            '13.1', '13.2',
            # Level 14: Y as vowel
            '14.1',
            # Level 15: Schwa
            '15.1', '15.2', '15.3',
            # Level 16: Silent letters
            '16.0',
            # Level 17: 3 sounds of ED
            '17.0',
            # Level 18: Vowel teams
            '18.1', '18.2', '18.3', '18.4', '18.5', '18.6', 
            '18.7', '18.8', '18.9', '18.10', '18.11', '18.12',
            # Level 19: Homophones
            '19.0',
            # Level 20: R-controlled
            '20.1', '20.2', '20.3', '20.4', '20.5',
            # Level 21+: Advanced patterns
            '21.1', '21.2', '21.5',
            '22.0', '23.1', '23.2', '24.0', '25.0', '26.0',
            '27.1', '27.2', '27.3', '27.5', '28.0', '29.0', '30.0'
        ]
    
    def _define_patterns_by_skill(self):
        """Define exactly what phonics patterns are taught at each skill"""
        patterns = {
            # CVC Skills - ONLY short vowels, no digraphs, no blends
            '1.1': {
                'vowels': {'a': 'short'},
                'allowed_consonants': ['b', 'c', 'd', 'f', 'g', 'h', 'j', 'k', 'l', 'm', 'n', 'p', 'r', 's', 't', 'v', 'w', 'x', 'z'],
                'pattern': 'CVC',
                'excludes': ['blends', 'digraphs', 'vowel_teams']
            },
            '1.2': {
                'vowels': {'e': 'short'},
                'allowed_consonants': ['b', 'c', 'd', 'f', 'g', 'h', 'j', 'k', 'l', 'm', 'n', 'p', 'r', 's', 't', 'v', 'w', 'x', 'z'],
                'pattern': 'CVC'
            },
            '1.3': {
                'vowels': {'i': 'short'},
                'allowed_consonants': ['b', 'c', 'd', 'f', 'g', 'h', 'j', 'k', 'l', 'm', 'n', 'p', 'r', 's', 't', 'v', 'w', 'x', 'z'],
                'pattern': 'CVC'
            },
            '1.4': {
                'vowels': {'o': 'short'},
                'allowed_consonants': ['b', 'c', 'd', 'f', 'g', 'h', 'j', 'k', 'l', 'm', 'n', 'p', 'r', 's', 't', 'v', 'w', 'x', 'z'],
                'pattern': 'CVC'
            },
            '1.5': {
                'vowels': {'u': 'short'},
                'allowed_consonants': ['b', 'c', 'd', 'f', 'g', 'h', 'j', 'k', 'l', 'm', 'n', 'p', 'r', 's', 't', 'v', 'w', 'x', 'z'],
                'pattern': 'CVC'
            },
            
            # CV Open syllables - specific words only
            '2.1': {'specific_words': ['be', 'he', 'me', 'we', 'she']},
            '2.2': {'specific_words': ['go', 'no', 'so']},
            '2.3': {'specific_words': ['I', 'hi']},
            
            # C/K rule
            '3.1': {'rule': 'c before a, o, u'},
            '3.2': {'rule': 'k before e, i, y'},
            
            # Digraphs
            '4.1': {'digraphs': ['ch']},
            '4.2': {'digraphs': ['sh']},
            '4.3': {'digraphs': ['th']},
            '4.4': {'digraphs': ['wh']},
            '4.5': {'digraphs': ['ck']},
            
            # Initial blends
            '5.1': {'initial_blends': ['bl', 'cl', 'fl', 'gl', 'pl', 'sl']},
            '5.2': {'initial_blends': ['br', 'cr', 'dr', 'fr', 'gr', 'pr', 'tr']},
            '5.3': {'initial_blends': ['sc', 'sk', 'sm', 'sn', 'sp', 'st', 'sw']},
            '5.4': {'initial_blends': ['scr', 'spr', 'str', 'spl', 'squ', 'thr']},
            
            # Final blends
            '6.1': {'final_blends': ['nt']},
            '6.2': {'final_blends': ['nd']},
            '6.3': {'final_blends': ['nk']},
            '6.4': {'final_blends': ['lt']},
            '6.5': {'final_blends': ['ld']},
            '6.6': {'final_blends': ['lf']},
            '6.7': {'final_blends': ['lk']},
            '6.8': {'final_blends': ['lm']},
            '6.9': {'final_blends': ['lp']},
            '6.10': {'final_blends': ['mp']},
            '6.11': {'final_blends': ['nd']},  # duplicate?
            '6.12': {'final_blends': ['ng']},
            '6.13': {'final_blends': ['sk']},
            '6.14': {'final_blends': ['st']},
            '6.15': {'final_blends': ['ft']},
            
            # Special rules
            '7.0': {'floss': ['ff', 'll', 'ss', 'zz']},
            '8.0': {'pattern': 'compound_words'},
            '9.0': {'pattern': 'tch'},
            '10.0': {'pattern': 'dge'},
            
            # Magic E
            '11.0': {'pattern': 'VCe', 'makes_vowel_long': True},
            
            # Closed syllable exceptions
            '12.1': {
                'exceptions': ['ind', 'ild', 'old', 'ost', 'olt', 'all', 'alk', 'ald'],
                'note': 'vowel sounds different than expected'
            },
            
            # Soft sounds
            '13.1': {'soft_g': ['ge', 'gi', 'gy', 'dge']},
            '13.2': {'soft_c': ['ce', 'ci', 'cy']},
            
            # Y as vowel
            '14.1': {
                'y_sounds': {
                    'y_end_one_syllable': '/ī/',  # my, by, cry
                    'y_end_multi_syllable': '/ē/',  # happy, silly
                    'y_middle': '/i/'  # gym, myth
                }
            },
            
            # Schwa
            '15.1': {'schwa': 'o'},
            '15.2': {'schwa': 'a'},
            '15.3': {'schwa': 'e'},
            
            # Silent letters
            '16.0': {'silent': ['kn', 'wr', 'mb', 'gh', 'gn', 'ps', 'rh']},
            
            # 3 sounds of ED
            '17.0': {'ed_sounds': ['/t/', '/d/', '/id/']},
            
            # Vowel teams - CRITICAL FOR DECODABILITY
            '18.1': {'vowel_teams': ['ai', 'ay', 'ei', 'eigh'], 'sound': 'long_a'},
            '18.2': {'vowel_teams': ['ea', 'ee', 'ie', 'ey'], 'sound': 'long_e'},
            '18.3': {'vowel_teams': ['oa', 'oe', 'ow'], 'sound': 'long_o'},
            '18.4': {'vowel_teams': ['ou', 'ow'], 'sound': '/ow/'},
            '18.5': {'vowel_teams': ['ie', 'igh'], 'sound': 'long_i'},
            '18.6': {'vowel_teams': ['ue', 'ew', 'eu'], 'sound': 'long_u'},
            '18.7': {'vowel_teams': ['oo'], 'sound': 'long_oo'},  # moon, food
            '18.8': {'vowel_teams': ['oo'], 'sound': 'short_oo'},  # book, stood
            '18.9': {'vowel_teams': ['ou'], 'sound': 'short_u'},
            '18.10': {'vowel_teams': ['au', 'aw', 'augh', 'ough'], 'sound': '/aw/'},
            '18.11': {'vowel_teams': ['oi', 'oy'], 'sound': '/oy/'},
            '18.12': {'vowel_teams': ['ea'], 'sound': 'short_e'},  # bread, head
            
            # R-controlled
            '20.1': {'r_controlled': ['er', 'ir', 'ur'], 'sound': '/er/'},
            '20.2': {'r_controlled': ['or', 'ore'], 'sound': '/or/'},
            '20.3': {'r_controlled': ['ar'], 'sound': '/ar/'},
            '20.4': {'r_controlled': ['war'], 'sound': '/wor/'},
            '20.5': {'r_controlled': ['wor'], 'sound': '/wer/'},
        }
        
        return patterns
    
    def get_all_patterns_in_word(self, word):
        """Extract ALL phonics patterns from a word"""
        word_lower = word.lower()
        patterns_found = []
        
        # Check for vowel teams first (before single vowels)
        all_vowel_teams = [
            'eigh', 'augh', 'ough',  # 4-letter teams first
            'ai', 'ay', 'ea', 'ee', 'ei', 'ey', 'ie', 'oa', 'oe', 
            'oi', 'oo', 'ou', 'ow', 'oy', 'ue', 'ui', 'au', 'aw',
            'ew', 'eu', 'igh'
        ]
        
        for vt in all_vowel_teams:
            if vt in word_lower:
                # Find which skill teaches this
                for skill, data in self.patterns_by_skill.items():
                    if 'vowel_teams' in data and vt in data['vowel_teams']:
                        patterns_found.append({
                            'pattern': vt,
                            'type': 'vowel_team',
                            'skill_required': skill,
                            'position': word_lower.find(vt)
                        })
                        break
        
        # Check for r-controlled patterns
        r_patterns = ['war', 'wor', 'ar', 'er', 'ir', 'or', 'ur', 'ore']
        for rp in r_patterns:
            if rp in word_lower:
                for skill, data in self.patterns_by_skill.items():
                    if 'r_controlled' in data and rp in data['r_controlled']:
                        patterns_found.append({
                            'pattern': rp,
                            'type': 'r_controlled',
                            'skill_required': skill,
                            'position': word_lower.find(rp)
                        })
                        break
        
        # Check for digraphs
        digraphs = ['ch', 'sh', 'th', 'wh', 'ck', 'ph', 'gh', 'ng', 'tch', 'dge']
        for dg in digraphs:
            if dg in word_lower:
                # Special handling for tch and dge
                if dg == 'tch':
                    skill_required = '9.0'
                elif dg == 'dge':
                    skill_required = '10.0'
                else:
                    # Find regular digraph skill
                    skill_required = None
                    for skill, data in self.patterns_by_skill.items():
                        if 'digraphs' in data and dg in data['digraphs']:
                            skill_required = skill
                            break
                
                if skill_required:
                    patterns_found.append({
                        'pattern': dg,
                        'type': 'digraph',
                        'skill_required': skill_required,
                        'position': word_lower.find(dg)
                    })
        
        # Check for initial blends
        initial_blends = [
            'scr', 'spr', 'str', 'spl', 'squ', 'thr',  # 3-letter first
            'bl', 'cl', 'fl', 'gl', 'pl', 'sl', 
            'br', 'cr', 'dr', 'fr', 'gr', 'pr', 'tr',
            'sc', 'sk', 'sm', 'sn', 'sp', 'st', 'sw'
        ]
        
        for blend in initial_blends:
            if word_lower.startswith(blend):
                for skill, data in self.patterns_by_skill.items():
                    if 'initial_blends' in data and blend in data['initial_blends']:
                        patterns_found.append({
                            'pattern': blend,
                            'type': 'initial_blend',
                            'skill_required': skill,
                            'position': 0
                        })
                        break
        
        # Check for final blends
        final_blends = ['nt', 'nd', 'nk', 'lt', 'ld', 'lf', 'lk', 'lm', 'lp', 
                       'mp', 'ng', 'sk', 'st', 'ft']
        
        for blend in final_blends:
            if word_lower.endswith(blend):
                for skill, data in self.patterns_by_skill.items():
                    if 'final_blends' in data and blend in data['final_blends']:
                        patterns_found.append({
                            'pattern': blend,
                            'type': 'final_blend',
                            'skill_required': skill,
                            'position': len(word_lower) - len(blend)
                        })
                        break
        
        # Check for FLOSS rule
        if any(pattern in word_lower for pattern in ['ff', 'll', 'ss', 'zz']):
            patterns_found.append({
                'pattern': 'FLOSS',
                'type': 'special_rule',
                'skill_required': '7.0'
            })
        
        # Check for Magic E
        if (len(word_lower) >= 4 and 
            word_lower[-1] == 'e' and 
            word_lower[-2] not in 'aeiou' and
            word_lower[-3] in 'aeiou'):
            patterns_found.append({
                'pattern': 'VCe',
                'type': 'magic_e',
                'skill_required': '11.0'
            })
        
        # Check for Y as vowel
        if 'y' in word_lower:
            if word_lower.endswith('y'):
                patterns_found.append({
                    'pattern': 'y_as_vowel',
                    'type': 'y_vowel',
                    'skill_required': '14.1'
                })
        
        # Check for soft C and G
        for i in range(len(word_lower)-1):
            if word_lower[i] == 'c' and word_lower[i+1] in 'eiy':
                patterns_found.append({
                    'pattern': f'c{word_lower[i+1]}',
                    'type': 'soft_c',
                    'skill_required': '13.2'
                })
            if word_lower[i] == 'g' and word_lower[i+1] in 'eiy':
                patterns_found.append({
                    'pattern': f'g{word_lower[i+1]}',
                    'type': 'soft_g',
                    'skill_required': '13.1'
                })
        
        # Check for silent letters
        silent_patterns = ['kn', 'wr', 'mb', 'gh', 'gn', 'ps', 'rh']
        for sp in silent_patterns:
            if sp in word_lower:
                patterns_found.append({
                    'pattern': sp,
                    'type': 'silent_letter',
                    'skill_required': '16.0'
                })
        
        # Check for closed syllable exceptions
        exceptions = ['all', 'alk', 'ald', 'ind', 'ild', 'old', 'ost', 'olt']
        for exc in exceptions:
            if exc in word_lower:
                patterns_found.append({
                    'pattern': exc,
                    'type': 'closed_syllable_exception',
                    'skill_required': '12.1'
                })
        
        return patterns_found
    
    def is_word_decodable(self, word, current_skill, available_skills, available_heart_words):
        """Check if ALL patterns in a word have been taught"""
        word_lower = word.lower()
        
        # Check if it's a heart word
        if word_lower in available_heart_words:
            return True, "heart word", []
        
        # Get all patterns in the word
        patterns = self.get_all_patterns_in_word(word)
        
        # Check each pattern
        violations = []
        for pattern_info in patterns:
            skill_required = pattern_info['skill_required']
            
            # Check if this pattern has been taught
            if skill_required not in available_skills:
                violations.append(pattern_info)
        
        # For words at early skill levels, also check basic constraints
        if current_skill.startswith('1.'):  # CVC level
            # Should not have any blends, digraphs, or vowel teams
            if any(p['type'] in ['initial_blend', 'final_blend', 'digraph', 'vowel_team'] 
                  for p in patterns):
                for p in patterns:
                    if p['type'] in ['initial_blend', 'final_blend', 'digraph', 'vowel_team']:
                        if p not in violations:
                            violations.append(p)
        
        if violations:
            return False, "violations found", violations
        
        return True, "decodable", []
    
    def validate_all_word_lists(self):
        """Validate all word lists and generate detailed report"""
        all_results = []
        
        # Get all word list files
        csv_files = sorted([f for f in os.listdir(self.word_lists_dir) 
                           if f.startswith('skill_') and f.endswith('.csv')])
        
        for csv_file in csv_files:
            # Extract skill ID
            skill_id = csv_file.split('_')[1].replace('.csv', '')
            
            # Get available skills (all skills up to and including current)
            skill_index = self.skill_sequence.index(skill_id) if skill_id in self.skill_sequence else -1
            available_skills = set(self.skill_sequence[:skill_index + 1]) if skill_index >= 0 else {skill_id}
            
            # Get available heart words
            available_heart_words = set()
            for skill in available_skills:
                available_heart_words.update(self.heart_words_by_skill.get(skill, set()))
            
            # Read word list
            filepath = os.path.join(self.word_lists_dir, csv_file)
            df = pd.read_csv(filepath)
            
            # Validate each word
            skill_violations = []
            for _, row in df.iterrows():
                word = row['word']
                
                # Skip if already marked as heart word
                if row.get('is_heart', False):
                    continue
                
                is_decodable, reason, violations = self.is_word_decodable(
                    word, skill_id, available_skills, available_heart_words
                )
                
                if not is_decodable:
                    skill_violations.append({
                        'word': word,
                        'violations': violations,
                        'is_HF': row.get('is_HF', False)
                    })
            
            # Record results
            all_results.append({
                'skill_id': skill_id,
                'skill_file': csv_file,
                'total_words': len(df),
                'violations': skill_violations,
                'violation_count': len(skill_violations),
                'violation_rate': len(skill_violations) / len(df) * 100 if len(df) > 0 else 0
            })
            
            # Print progress
            if skill_violations:
                print(f"Skill {skill_id}: {len(skill_violations)} violations found")
        
        return all_results
    
    def generate_detailed_report(self, results):
        """Generate comprehensive decodability report"""
        report_path = '/Users/<USER>/word-generator/comprehensive_decodability_report.txt'
        
        with open(report_path, 'w') as f:
            f.write("COMPREHENSIVE DECODABILITY VALIDATION REPORT\n")
            f.write("=" * 100 + "\n")
            f.write("Words containing patterns that haven't been taught yet\n\n")
            
            # Summary statistics
            total_violations = sum(r['violation_count'] for r in results)
            skills_with_violations = len([r for r in results if r['violations']])
            
            f.write(f"Total violations found: {total_violations}\n")
            f.write(f"Skills with violations: {skills_with_violations} / {len(results)}\n\n")
            
            # Most problematic skills
            problematic = sorted(results, key=lambda x: x['violation_count'], reverse=True)[:10]
            
            f.write("TOP 10 MOST PROBLEMATIC SKILLS:\n")
            f.write("-" * 100 + "\n\n")
            
            for result in problematic:
                if result['violation_count'] == 0:
                    continue
                    
                f.write(f"Skill {result['skill_id']} ({result['skill_file']}):\n")
                f.write(f"  Total words: {result['total_words']}\n")
                f.write(f"  Violations: {result['violation_count']} ({result['violation_rate']:.1f}%)\n")
                f.write(f"  Examples:\n")
                
                # Show up to 5 examples with details
                for i, v in enumerate(result['violations'][:5]):
                    f.write(f"    {i+1}. \"{v['word']}\"")
                    if v['is_HF']:
                        f.write(" [High Frequency]")
                    f.write("\n")
                    
                    # Show specific violations
                    for viol in v['violations']:
                        f.write(f"       - Pattern '{viol['pattern']}' ({viol['type']}) ")
                        f.write(f"requires skill {viol['skill_required']}\n")
                
                if len(result['violations']) > 5:
                    f.write(f"    ... and {len(result['violations']) - 5} more violations\n")
                f.write("\n")
            
            # Detailed section for all skills
            f.write("\n\nDETAILED VIOLATIONS BY SKILL:\n")
            f.write("=" * 100 + "\n\n")
            
            for result in results:
                if not result['violations']:
                    continue
                    
                f.write(f"Skill {result['skill_id']} - {result['skill_file']}:\n")
                f.write("-" * 80 + "\n")
                
                # Group by violation type
                by_type = defaultdict(list)
                for v in result['violations']:
                    for viol in v['violations']:
                        by_type[viol['type']].append((v['word'], viol))
                
                for vtype, items in sorted(by_type.items()):
                    f.write(f"\n  {vtype.replace('_', ' ').title()} violations ({len(items)}):\n")
                    for word, viol in items[:10]:
                        f.write(f"    - \"{word}\": {viol['pattern']} (needs skill {viol['skill_required']})\n")
                    if len(items) > 10:
                        f.write(f"    ... and {len(items) - 10} more\n")
                
                f.write("\n")
        
        # Generate CSV for analysis
        csv_path = '/Users/<USER>/word-generator/comprehensive_violations.csv'
        
        violations_data = []
        for result in results:
            for v in result['violations']:
                for viol in v['violations']:
                    violations_data.append({
                        'skill_id': result['skill_id'],
                        'word': v['word'],
                        'pattern': viol['pattern'],
                        'pattern_type': viol['type'],
                        'skill_required': viol['skill_required'],
                        'is_HF': v['is_HF']
                    })
        
        if violations_data:
            violations_df = pd.DataFrame(violations_data)
            violations_df.to_csv(csv_path, index=False)
        
        print(f"\nReports generated:")
        print(f"  - {report_path}")
        print(f"  - {csv_path}")
        
        return total_violations
    
    def fix_word_lists(self, results, output_dir=None):
        """Create cleaned word lists with only decodable words"""
        if output_dir is None:
            output_dir = '/Users/<USER>/word-generator/word_lists_decodable'
        
        os.makedirs(output_dir, exist_ok=True)
        
        summary = []
        
        for result in results:
            # Read original file
            filepath = os.path.join(self.word_lists_dir, result['skill_file'])
            df = pd.read_csv(filepath)
            
            # Get words to remove
            words_to_remove = {v['word'] for v in result['violations']}
            
            # Create clean dataframe
            clean_df = df[~df['word'].isin(words_to_remove)].copy()
            
            # Save clean file
            clean_path = os.path.join(output_dir, result['skill_file'])
            clean_df.to_csv(clean_path, index=False)
            
            # Track summary
            summary.append({
                'skill_id': result['skill_id'],
                'original_count': len(df),
                'removed_count': len(words_to_remove),
                'final_count': len(clean_df),
                'retention_rate': len(clean_df) / len(df) * 100 if len(df) > 0 else 0
            })
        
        # Save summary
        summary_df = pd.DataFrame(summary)
        summary_df.to_csv(os.path.join(output_dir, 'decodability_fix_summary.csv'), index=False)
        
        print(f"\nCleaned word lists saved to: {output_dir}")
        print(f"Summary saved to: {output_dir}/decodability_fix_summary.csv")
        
        return summary

def main():
    print("COMPREHENSIVE DECODABILITY VALIDATOR")
    print("=" * 100)
    print("Ensuring EVERY phonics element in each word has been taught...\n")
    
    validator = ComprehensiveDecodabilityValidator()
    
    # Validate all lists
    print("Validating word lists...")
    results = validator.validate_all_word_lists()
    
    # Generate report
    print("\nGenerating detailed report...")
    total_violations = validator.generate_detailed_report(results)
    
    print(f"\nValidation complete!")
    print(f"Total violations found: {total_violations}")
    
    # Ask if user wants to create cleaned lists
    if input("\nCreate cleaned word lists with only decodable words? (y/n): ").lower() == 'y':
        print("\nCreating cleaned word lists...")
        summary = validator.fix_word_lists(results)
        
        total_removed = sum(s['removed_count'] for s in summary)
        print(f"\nRemoved {total_removed} non-decodable words across all lists.")

if __name__ == "__main__":
    main()
