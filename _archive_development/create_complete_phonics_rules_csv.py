#!/usr/bin/env python3
"""
Create COMPLETE CSV files documenting ALL phonics rules and patterns
This version includes all 82 skills from the Orton-Gillingham sequence
"""

import pandas as pd
import csv

def create_complete_master_rules_csv():
    """Create comprehensive rules CSV for ALL 82 skills"""
    
    rules = []
    
    # Skills with patterns defined in the code
    defined_skills = {
        # CVC Rules (1.1-1.5)
        '1.1': {'name': 'CVC short a', 'type': 'CVC', 'graphemes': 'a', 'phonemes': 'AE', 'desc': 'short /a/ as in cat'},
        '1.2': {'name': 'CVC short e', 'type': 'CVC', 'graphemes': 'e', 'phonemes': 'EH', 'desc': 'short /e/ as in bed'},
        '1.3': {'name': 'CVC short i', 'type': 'CVC', 'graphemes': 'i', 'phonemes': 'IH', 'desc': 'short /i/ as in sit'},
        '1.4': {'name': 'CVC short o', 'type': 'CVC', 'graphemes': 'o', 'phonemes': 'AA', 'desc': 'short /o/ as in hot'},
        '1.5': {'name': 'CVC short u', 'type': 'CVC', 'graphemes': 'u', 'phonemes': 'AH', 'desc': 'short /u/ as in cup'},
        
        # CV Rules (2.1-2.3)
        '2.1': {'name': 'CV long e', 'type': 'CV open syllable', 'graphemes': 'e', 'phonemes': 'IY', 'desc': 'long /e/ as in be'},
        '2.2': {'name': 'CV long o', 'type': 'CV open syllable', 'graphemes': 'o', 'phonemes': 'OW', 'desc': 'long /o/ as in go'},
        '2.3': {'name': 'CV long i', 'type': 'CV open syllable', 'graphemes': 'i', 'phonemes': 'AY', 'desc': 'long /i/ as in I'},
        
        # C/K Rules (3.1-3.2)
        '3.1': {'name': 'c before a,o,u', 'type': 'hard c rule', 'graphemes': 'c', 'phonemes': 'K', 'desc': 'hard /k/ sound'},
        '3.2': {'name': 'k before e,i,y', 'type': 'k spelling rule', 'graphemes': 'k', 'phonemes': 'K', 'desc': 'hard /k/ sound'},
        
        # Digraphs (4.1-4.5)
        '4.1': {'name': 'ch digraph', 'type': 'digraph', 'graphemes': 'ch', 'phonemes': 'CH', 'desc': '/ch/ as in chip'},
        '4.2': {'name': 'sh digraph', 'type': 'digraph', 'graphemes': 'sh', 'phonemes': 'SH', 'desc': '/sh/ as in ship'},
        '4.3': {'name': 'th digraph', 'type': 'digraph', 'graphemes': 'th', 'phonemes': 'TH or DH', 'desc': 'voiced or unvoiced /th/'},
        '4.4': {'name': 'wh digraph', 'type': 'digraph', 'graphemes': 'wh', 'phonemes': 'W or HH W', 'desc': '/w/ or /hw/'},
        '4.5': {'name': 'ck digraph', 'type': 'digraph', 'graphemes': 'ck', 'phonemes': 'K', 'desc': '/k/ as in back'},
        
        # Initial Blends (5.1-5.4)
        '5.1': {'name': 'l-blends', 'type': 'initial blend', 'graphemes': 'bl, cl, fl, gl, pl, sl', 'phonemes': 'B L, K L, F L, G L, P L, S L', 'desc': 'consonant + /l/'},
        '5.2': {'name': 'r-blends', 'type': 'initial blend', 'graphemes': 'br, cr, dr, fr, gr, pr, tr', 'phonemes': 'B R, K R, D R, F R, G R, P R, T R', 'desc': 'consonant + /r/'},
        '5.3': {'name': 's-blends', 'type': 'initial blend', 'graphemes': 'sc, sk, sm, sn, sp, st, sw', 'phonemes': 'S K, S K, S M, S N, S P, S T, S W', 'desc': '/s/ + consonant'},
        '5.4': {'name': '3-letter blends', 'type': 'initial blend', 'graphemes': 'scr, spr, str, spl, squ, thr', 'phonemes': 'S K R, S P R, S T R, S P L, S K W, TH R', 'desc': 'three consonant sounds'},
        
        # ALL Final Blends (6.1-6.15)
        '6.1': {'name': 'nt ending', 'type': 'final blend', 'graphemes': 'nt', 'phonemes': 'N T', 'desc': '/n/ + /t/'},
        '6.2': {'name': 'nd ending', 'type': 'final blend', 'graphemes': 'nd', 'phonemes': 'N D', 'desc': '/n/ + /d/'},
        '6.3': {'name': 'nk ending', 'type': 'final blend', 'graphemes': 'nk', 'phonemes': 'NG K', 'desc': '/ng/ + /k/'},
        '6.4': {'name': 'lt ending', 'type': 'final blend', 'graphemes': 'lt', 'phonemes': 'L T', 'desc': '/l/ + /t/'},
        '6.5': {'name': 'ld ending', 'type': 'final blend', 'graphemes': 'ld', 'phonemes': 'L D', 'desc': '/l/ + /d/'},
        '6.6': {'name': 'lf ending', 'type': 'final blend', 'graphemes': 'lf', 'phonemes': 'L F', 'desc': '/l/ + /f/'},
        '6.7': {'name': 'lk ending', 'type': 'final blend', 'graphemes': 'lk', 'phonemes': 'L K', 'desc': '/l/ + /k/'},
        '6.8': {'name': 'lm ending', 'type': 'final blend', 'graphemes': 'lm', 'phonemes': 'L M', 'desc': '/l/ + /m/'},
        '6.9': {'name': 'lp ending', 'type': 'final blend', 'graphemes': 'lp', 'phonemes': 'L P', 'desc': '/l/ + /p/'},
        '6.10': {'name': 'mp ending', 'type': 'final blend', 'graphemes': 'mp', 'phonemes': 'M P', 'desc': '/m/ + /p/'},
        '6.11': {'name': 'nd ending', 'type': 'final blend', 'graphemes': 'nd', 'phonemes': 'N D', 'desc': '/n/ + /d/'}, # duplicate of 6.2
        '6.12': {'name': 'ng ending', 'type': 'final blend', 'graphemes': 'ng', 'phonemes': 'NG', 'desc': '/ng/ as in sing'},
        '6.13': {'name': 'sk ending', 'type': 'final blend', 'graphemes': 'sk', 'phonemes': 'S K', 'desc': '/s/ + /k/'},
        '6.14': {'name': 'st ending', 'type': 'final blend', 'graphemes': 'st', 'phonemes': 'S T', 'desc': '/s/ + /t/'},
        '6.15': {'name': 'ft ending', 'type': 'final blend', 'graphemes': 'ft', 'phonemes': 'F T', 'desc': '/f/ + /t/'},
        
        # Special Rules (7.0-11.0)
        '7.0': {'name': 'FLOSS rule', 'type': 'doubling rule', 'graphemes': 'ff, ll, ss, zz', 'phonemes': 'F, L, S, Z', 'desc': 'double after short vowel'},
        '8.0': {'name': 'Backpack rule', 'type': 'compound words', 'graphemes': 'compound words', 'phonemes': 'various', 'desc': 'two words together'},
        '9.0': {'name': 'Catch rule', 'type': 'tch pattern', 'graphemes': 'tch', 'phonemes': 'CH', 'desc': '/ch/ after short vowel'},
        '10.0': {'name': 'Bridge rule', 'type': 'dge pattern', 'graphemes': 'dge', 'phonemes': 'JH', 'desc': '/j/ after short vowel'},
        '11.0': {'name': 'Magic E', 'type': 'vowel pattern', 'graphemes': 'VCe', 'phonemes': 'long vowel', 'desc': 'makes vowel long'},
        
        # Additional patterns
        '12.1': {'name': 'Closed syllable exceptions', 'type': 'exception pattern', 'graphemes': 'all, ald, alk, old, ild, ind', 'phonemes': 'various', 'desc': 'exceptions to closed syllable'},
        '13.1': {'name': 'Soft g', 'type': 'soft sound', 'graphemes': 'ge, gi, gy', 'phonemes': 'JH', 'desc': '/j/ before e, i, y'},
        '13.2': {'name': 'Soft c', 'type': 'soft sound', 'graphemes': 'ce, ci, cy', 'phonemes': 'S', 'desc': '/s/ before e, i, y'},
        '14.1': {'name': 'Y as vowel', 'type': 'vowel substitute', 'graphemes': 'y', 'phonemes': 'IY or AY or IH', 'desc': 'y acts as vowel'},
        
        # Schwa sounds
        '15.1': {'name': 'o says uh', 'type': 'schwa', 'graphemes': 'o', 'phonemes': 'AH', 'desc': 'unstressed /uh/ sound'},
        '15.2': {'name': 'a says uh', 'type': 'schwa', 'graphemes': 'a', 'phonemes': 'AH', 'desc': 'unstressed /uh/ sound'},
        '15.3': {'name': 'e says uh', 'type': 'schwa', 'graphemes': 'e', 'phonemes': 'AH', 'desc': 'unstressed /uh/ sound'},
        
        '16.0': {'name': 'Silent letters', 'type': 'silent letters', 'graphemes': 'kn, wr, mb, gh, gn', 'phonemes': 'silent', 'desc': 'letters not pronounced'},
        '17.0': {'name': '3 sounds of ed', 'type': 'suffix pattern', 'graphemes': 'ed', 'phonemes': '/ed/, /d/, /t/', 'desc': 'three pronunciations'},
        
        # Vowel teams (18.1-18.12)
        '18.1': {'name': 'Long a teams', 'type': 'vowel team', 'graphemes': 'ai, ay, ei, eigh', 'phonemes': 'EY', 'desc': 'long /a/ sound'},
        '18.2': {'name': 'Long e teams', 'type': 'vowel team', 'graphemes': 'ea, ee, ie, ey', 'phonemes': 'IY', 'desc': 'long /e/ sound'},
        '18.3': {'name': 'Long o teams', 'type': 'vowel team', 'graphemes': 'oa, oe, ow', 'phonemes': 'OW', 'desc': 'long /o/ sound'},
        '18.4': {'name': 'ow sound', 'type': 'vowel team', 'graphemes': 'ou, ow', 'phonemes': 'AW', 'desc': '/ow/ as in cow'},
        '18.5': {'name': 'Long i teams', 'type': 'vowel team', 'graphemes': 'ie, igh', 'phonemes': 'AY', 'desc': 'long /i/ sound'},
        '18.6': {'name': 'Long u teams', 'type': 'vowel team', 'graphemes': 'ue, ew, eu', 'phonemes': 'UW or YUW', 'desc': 'long /u/ sound'},
        '18.7': {'name': 'Long oo', 'type': 'vowel team', 'graphemes': 'oo', 'phonemes': 'UW', 'desc': 'long /oo/ as in moon'},
        '18.8': {'name': 'Short oo', 'type': 'vowel team', 'graphemes': 'oo', 'phonemes': 'UH', 'desc': 'short /oo/ as in book'},
        '18.9': {'name': 'ou says u', 'type': 'vowel team', 'graphemes': 'ou', 'phonemes': 'AH', 'desc': 'short /u/ sound'},
        '18.10': {'name': 'aw sound', 'type': 'vowel team', 'graphemes': 'au, aw', 'phonemes': 'AO', 'desc': '/aw/ as in saw'},
        '18.11': {'name': 'oy sound', 'type': 'vowel team', 'graphemes': 'oi, oy', 'phonemes': 'OY', 'desc': '/oy/ as in boy'},
        '18.12': {'name': 'ea says e', 'type': 'vowel team', 'graphemes': 'ea', 'phonemes': 'EH', 'desc': 'short /e/ sound'},
        
        '19.0': {'name': 'Homophones', 'type': 'vocabulary', 'graphemes': 'various', 'phonemes': 'various', 'desc': 'same sound, different meaning'},
        
        # R-controlled (20.1-20.5)
        '20.1': {'name': 'er sound', 'type': 'r-controlled', 'graphemes': 'er, ir, ur', 'phonemes': 'ER', 'desc': '/er/ as in her'},
        '20.2': {'name': 'or sound', 'type': 'r-controlled', 'graphemes': 'or', 'phonemes': 'AO R or AO', 'desc': '/or/ as in for'},
        '20.3': {'name': 'ar sound', 'type': 'r-controlled', 'graphemes': 'ar', 'phonemes': 'AA R', 'desc': '/ar/ as in car'},
        '20.4': {'name': 'war sound', 'type': 'r-controlled', 'graphemes': 'war', 'phonemes': 'AO R', 'desc': '/or/ as in war'},
        '20.5': {'name': 'wor sound', 'type': 'r-controlled', 'graphemes': 'wor', 'phonemes': 'ER', 'desc': '/er/ as in work'},
        
        # Syllabication
        '21.1': {'name': 'Compound words', 'type': 'syllabication', 'graphemes': 'compound', 'phonemes': 'various', 'desc': 'divide between words'},
        '21.2': {'name': 'Turtle words', 'type': 'syllabication', 'graphemes': 'VCCV', 'phonemes': 'various', 'desc': 'divide between consonants'},
        '21.5': {'name': 'Rabbit words', 'type': 'syllabication', 'graphemes': 'VCV', 'phonemes': 'various', 'desc': 'divide after first vowel'},
        
        # Morphology
        '22.0': {'name': 'Common prefixes', 'type': 'morphology', 'graphemes': 'un-, re-, pre-, dis-, mis-', 'phonemes': 'various', 'desc': 'word beginnings'},
        '23.1': {'name': 'Common suffixes', 'type': 'morphology', 'graphemes': '-ing, -ed, -er, -est, -ly', 'phonemes': 'various', 'desc': 'word endings'},
        '23.2': {'name': 'Irregular suffixes', 'type': 'morphology', 'graphemes': '-tion, -sion, -ture', 'phonemes': 'various', 'desc': 'special suffixes'},
        
        # Advanced rules
        '24.0': {'name': '1-1-1 doubling', 'type': 'spelling rule', 'graphemes': 'double final consonant', 'phonemes': 'various', 'desc': 'double before suffix'},
        '25.0': {'name': 'E-dropping rule', 'type': 'spelling rule', 'graphemes': 'drop e before vowel suffix', 'phonemes': 'various', 'desc': 'drop silent e'},
        '26.0': {'name': 'Change y to i', 'type': 'spelling rule', 'graphemes': 'y to i', 'phonemes': 'various', 'desc': 'y changes to i'},
        '27.1': {'name': 'Plural -s', 'type': 'plural rule', 'graphemes': '-s', 'phonemes': 'S or Z', 'desc': 'regular plural'},
        '27.2': {'name': 'Plural -es', 'type': 'plural rule', 'graphemes': '-es', 'phonemes': 'IH Z', 'desc': 'plural after s,x,z,ch,sh'},
        '27.3': {'name': 'f to v plural', 'type': 'plural rule', 'graphemes': 'f/fe to ves', 'phonemes': 'V Z', 'desc': 'knife → knives'},
        '27.5': {'name': 'Irregular plurals', 'type': 'plural rule', 'graphemes': 'various', 'phonemes': 'various', 'desc': 'man → men'},
        '28.0': {'name': '2-1-1 doubling', 'type': 'spelling rule', 'graphemes': 'double in 2-syllable words', 'phonemes': 'various', 'desc': 'begin → beginning'},
        '29.0': {'name': 'Silent e not plural', 'type': 'spelling rule', 'graphemes': 'VCe', 'phonemes': 'various', 'desc': 'distinguish from plural'},
        '30.0': {'name': 'Contractions', 'type': 'punctuation', 'graphemes': "apostrophe", 'phonemes': 'various', 'desc': "can't, won't, it's"},
    }
    
    # Convert to list format with additional details
    for skill_id, info in defined_skills.items():
        rules.append({
            'skill_id': skill_id,
            'skill_name': info['name'],
            'pattern_type': info['type'],
            'graphemes': info['graphemes'],
            'required_phonemes': info['phonemes'],
            'phoneme_description': info['desc'],
            'position_constraints': get_position_constraints(skill_id, info),
            'word_structure': get_word_structure(skill_id, info),
            'special_rules': get_special_rules(skill_id, info),
            'examples': get_examples(skill_id, info),
            'non_examples': get_non_examples(skill_id, info),
            'notes': get_additional_notes(skill_id, info)
        })
    
    # Create DataFrame and save
    df = pd.DataFrame(rules)
    # Sort by skill_id numerically
    df['sort_key'] = df['skill_id'].apply(lambda x: tuple(map(int, x.split('.'))))
    df = df.sort_values('sort_key').drop('sort_key', axis=1)
    
    df.to_csv('/Users/<USER>/word-generator/phonics_master_rules_complete.csv', index=False)
    print(f"Created phonics_master_rules_complete.csv with {len(rules)} rules")
    
    # Verify we have all 82 skills
    skill_sequence = [
        '1.1', '1.2', '1.3', '1.4', '1.5',
        '2.1', '2.2', '2.3',
        '3.1', '3.2',
        '4.1', '4.2', '4.3', '4.4', '4.5',
        '5.1', '5.2', '5.3', '5.4',
        '6.1', '6.2', '6.3', '6.4', '6.5',
        '6.6', '6.7', '6.8', '6.9', '6.10',
        '6.11', '6.12', '6.13', '6.14', '6.15',
        '7.0', '8.0', '9.0', '10.0',
        '11.0',
        '12.1',
        '13.1', '13.2',
        '14.1',
        '15.1', '15.2', '15.3',
        '16.0',
        '17.0',
        '18.1', '18.2', '18.3', '18.4',
        '18.5', '18.6', '18.7', '18.8',
        '18.9', '18.10', '18.11', '18.12',
        '19.0',
        '20.1', '20.2', '20.3', '20.4', '20.5',
        '21.1', '21.2', '21.5',
        '22.0', '23.1', '23.2',
        '24.0', '25.0', '26.0',
        '27.1', '27.2', '27.3', '27.5',
        '28.0', '29.0', '30.0'
    ]
    
    included_skills = set(df['skill_id'].values)
    all_skills = set(skill_sequence)
    missing = all_skills - included_skills
    
    if missing:
        print(f"WARNING: Missing skills: {sorted(missing)}")
    else:
        print(f"✓ All {len(skill_sequence)} skills included!")
    
    return df

def get_position_constraints(skill_id, info):
    """Get position constraints for patterns"""
    constraints = {
        '1.1': 'medial (vowel position)',
        '1.2': 'medial (vowel position)',
        '1.3': 'medial (vowel position)',
        '1.4': 'medial (vowel position)',
        '1.5': 'medial (vowel position)',
        '2.1': 'final',
        '2.2': 'final',
        '2.3': 'final or standalone',
        '3.1': 'before a, o, u',
        '3.2': 'before e, i, y',
        '4.4': 'initial only',
        '4.5': 'final only',
        '5.1': 'initial only',
        '5.2': 'initial only',
        '5.3': 'initial only',
        '5.4': 'initial only',
        '6.1': 'final only',
        '6.2': 'final only',
        '6.3': 'final only',
        '6.4': 'final only',
        '6.5': 'final only',
        '6.6': 'final only',
        '6.7': 'final only',
        '6.8': 'final only',
        '6.9': 'final only',
        '6.10': 'final only',
        '6.11': 'final only',
        '6.12': 'final only',
        '6.13': 'final only',
        '6.14': 'final only',
        '6.15': 'final only',
        '7.0': 'final after short vowel',
        '9.0': 'final after short vowel',
        '10.0': 'final after short vowel',
        '11.0': 'final e with consonant between',
        '18.1': 'ai/ei medial, ay final, eigh any',
        '18.2': 'ea/ee/ie medial, ey final',
        '18.3': 'oa/oe medial, ow final or before n',
        '18.4': 'any',
        '18.5': 'any',
        '18.6': 'ue/eu medial, ew often final',
        '18.7': 'medial',
        '18.8': 'medial',
        '18.9': 'medial',
        '18.10': 'au medial, aw often final',
        '18.11': 'oi medial, oy final',
        '18.12': 'medial',
    }
    return constraints.get(skill_id, 'any position')

def get_word_structure(skill_id, info):
    """Get word structure requirements"""
    structures = {
        '1.1': '3-letter CVC only',
        '1.2': '3-letter CVC only',
        '1.3': '3-letter CVC only',
        '1.4': '3-letter CVC only',
        '1.5': '3-letter CVC only',
        '2.1': 'CV structure only',
        '2.2': 'CV structure only',
        '2.3': 'CV structure only',
        '4.5': 'after short vowel',
        '7.0': 'one-syllable words',
        '9.0': 'one-syllable words',
        '10.0': 'one-syllable words',
        '11.0': 'VCe pattern',
        '21.1': 'two or more words combined',
        '21.2': 'VCCV pattern',
        '21.5': 'VCV pattern',
    }
    return structures.get(skill_id, 'any')

def get_special_rules(skill_id, info):
    """Get special rules for patterns"""
    rules = {
        '1.1': 'No blends, digraphs, or vowel teams allowed',
        '1.2': 'No blends, digraphs, or vowel teams allowed',
        '1.3': 'No blends, digraphs, or vowel teams allowed',
        '1.4': 'No blends, digraphs, or vowel teams allowed',
        '1.5': 'No blends, digraphs, or vowel teams allowed',
        '2.1': 'Limited to specific words: be, he, me, we, she',
        '2.2': 'Limited to specific words: go, no, so',
        '2.3': 'Limited to specific words: I, hi',
        '3.1': 'c makes /k/ sound before a, o, u',
        '3.2': 'Use k (not c) before e, i, y',
        '4.1': 'Two letters make one sound',
        '4.2': 'Two letters make one sound',
        '4.3': 'Can be voiced or unvoiced',
        '4.4': 'Always at beginning of words/syllables',
        '4.5': 'Only after short vowels in one-syllable words',
        '5.1': 'Two consonant sounds blend together',
        '5.2': 'Two consonant sounds blend together',
        '5.3': 'Two consonant sounds blend together',
        '5.4': 'Three consonant sounds blend together',
        '6.1': 'Two consonant sounds at word end',
        '6.2': 'Two consonant sounds at word end',
        '6.3': 'Makes /ngk/ sound',
        '6.12': 'Single sound (not n+g)',
        '7.0': 'Double f, l, s, z after short vowel in one-syllable words',
        '8.0': 'Two complete words joined together',
        '9.0': 'Use tch after short vowel (not ch)',
        '10.0': 'Use dge after short vowel (not ge)',
        '11.0': 'Silent e makes vowel say its name',
        '12.1': 'Vowel sounds long in closed syllables',
        '13.1': 'g makes /j/ sound before e, i, y',
        '13.2': 'c makes /s/ sound before e, i, y',
        '14.1': 'y acts as vowel when not followed by vowel',
        '15.1': 'Unstressed o makes schwa sound',
        '15.2': 'Unstressed a makes schwa sound',
        '15.3': 'Unstressed e makes schwa sound',
        '16.0': 'Letters present but not pronounced',
        '17.0': '/ed/ after t/d, /t/ after voiceless, /d/ after voiced',
        '18.1': 'Must produce long a sound',
        '18.2': 'Must produce long e sound',
        '18.3': 'Must produce long o sound',
        '18.4': 'Must produce /ow/ diphthong',
        '18.5': 'Must produce long i sound',
        '18.6': 'Can be /oo/ or /yoo/ sound',
        '18.7': 'Must produce long oo sound',
        '18.8': 'Must produce short oo sound',
        '18.9': 'ou making short u sound',
        '18.10': 'Consistent /aw/ sound',
        '18.11': 'Consistent /oy/ sound',
        '18.12': 'ea making short e sound',
        '19.0': 'Words that sound the same but have different meanings',
        '20.1': 'All three spellings make same sound',
        '20.2': 'Consistent /or/ sound',
        '20.3': 'Consistent /ar/ sound',
        '20.4': 'w changes ar to or sound',
        '20.5': 'w changes or to er sound',
        '21.1': 'Divide between component words',
        '21.2': 'Divide between two consonants',
        '21.5': 'First vowel usually open (long)',
        '22.0': 'Added to beginning of base word',
        '23.1': 'Added to end of base word',
        '23.2': 'Change pronunciation of base',
        '24.0': 'One syllable, one vowel, one final consonant',
        '25.0': 'Drop e before adding vowel suffix',
        '26.0': 'Change y to i before suffix (except -ing)',
        '27.1': 'Add -s to most nouns',
        '27.2': 'Add -es after sibilant sounds',
        '27.3': 'Change f/fe to v and add -es',
        '27.5': 'Must be memorized',
        '28.0': 'Stress on second syllable',
        '29.0': 'Distinguish rose (flower) vs. rows (plural)',
        '30.0': 'Apostrophe shows missing letters',
    }
    return rules.get(skill_id, '')

def get_examples(skill_id, info):
    """Get example words for each pattern"""
    examples = {
        '1.1': 'cat, hat, mat, sat, bat',
        '1.2': 'bed, get, met, set, wet',
        '1.3': 'sit, hit, bit, fit, pit',
        '1.4': 'hot, pot, dot, got, lot',
        '1.5': 'cup, cut, but, nut, hut',
        '2.1': 'be, he, me, we, she',
        '2.2': 'go, no, so',
        '2.3': 'I, hi',
        '3.1': 'cat, cot, cup, can, come',
        '3.2': 'kit, key, kid, kite, keep',
        '4.1': 'chip, chat, much, lunch, chop',
        '4.2': 'ship, shop, wish, cash, she',
        '4.3': 'this, that, thin, bath, with',
        '4.4': 'when, what, where, why, which',
        '4.5': 'back, pack, sick, lock, duck',
        '5.1': 'black, clap, flag, glad, play, slow',
        '5.2': 'bring, crab, drip, from, grab, print, trip',
        '5.3': 'scan, skip, smell, snap, spot, stop, swim',
        '5.4': 'scrap, spring, string, split, square, three',
        '6.1': 'ant, went, hunt, plant, print',
        '6.2': 'and, end, hand, stand, wind',
        '6.3': 'ink, bank, think, trunk, drink',
        '6.4': 'salt, melt, bolt, felt, wilt',
        '6.5': 'old, cold, held, build, child',
        '6.6': 'self, golf, wolf, shelf, myself',
        '6.7': 'milk, walk, talk, chalk, silk',
        '6.8': 'calm, palm, film, helm, realm',
        '6.9': 'help, kelp, gulp, scalp, yelp',
        '6.10': 'jump, stamp, camp, lamp, damp',
        '6.11': 'and, end, hand, stand, wind',
        '6.12': 'sing, ring, thing, strong, long',
        '6.13': 'ask, mask, desk, risk, task',
        '6.14': 'fast, last, best, just, must',
        '6.15': 'left, soft, gift, craft, shift',
        '7.0': 'stuff, bell, pass, buzz, cliff',
        '8.0': 'backpack, homework, playground, lunchbox',
        '9.0': 'catch, match, pitch, notch, clutch',
        '10.0': 'badge, edge, bridge, lodge, judge',
        '11.0': 'make, pete, bike, hope, cute',
        '12.1': 'all, ball, tall, old, cold, wild',
        '13.1': 'age, giant, gym, gentle, energy',
        '13.2': 'city, cent, face, dance, fancy',
        '14.1': 'my, fly, baby, happy, gym',
        '15.1': 'lemon, button, bacon, prison, melon',
        '15.2': 'about, around, sofa, banana, America',
        '15.3': 'taken, kitten, frozen, broken, children',
        '16.0': 'knee, write, lamb, light, sign',
        '17.0': 'wanted, jumped, played, needed, walked',
        '18.1': 'rain, play, vein, eight, neighbor',
        '18.2': 'eat, see, field, key, turkey',
        '18.3': 'boat, toe, grow, show, own',
        '18.4': 'out, house, cow, now, howl',
        '18.5': 'pie, tie, high, night, bright',
        '18.6': 'blue, new, few, feud, rescue',
        '18.7': 'moon, food, pool, boot, tooth',
        '18.8': 'book, look, good, wood, foot',
        '18.9': 'touch, young, country, double, trouble',
        '18.10': 'saw, paw, haul, cause, autumn',
        '18.11': 'oil, coin, boy, toy, enjoy',
        '18.12': 'bread, head, dead, spread, heavy',
        '19.0': 'to/two/too, there/their, no/know',
        '20.1': 'her, bird, turn, fern, first',
        '20.2': 'for, corn, sport, north, born',
        '20.3': 'car, star, park, hard, start',
        '20.4': 'war, warm, warn, wart, dwarf',
        '20.5': 'work, word, world, worm, worth',
        '21.1': 'cupcake, football, rainbow, butterfly',
        '21.2': 'rabbit, letter, happen, dinner, yellow',
        '21.5': 'tiger, spider, robot, music, paper',
        '22.0': 'undo, replay, preview, disagree, mistake',
        '23.1': 'jumping, walked, faster, happily, teacher',
        '23.2': 'nation, vision, picture, action, mission',
        '24.0': 'hopping, sitting, running, swimming, batting',
        '25.0': 'making, hoping, caring, taking, baking',
        '26.0': 'happier, cried, flies, babies, funnier',
        '27.1': 'cats, dogs, books, cars, toys',
        '27.2': 'boxes, wishes, churches, bushes, glasses',
        '27.3': 'knives, wolves, leaves, lives, shelves',
        '27.5': 'children, men, feet, mice, geese',
        '28.0': 'beginning, forgetting, occurring, referring',
        '29.0': 'rose, hope, care, love, home',
        '30.0': "can't, won't, it's, I'll, they're",
    }
    return examples.get(skill_id, '')

def get_non_examples(skill_id, info):
    """Get non-example words for each pattern"""
    non_examples = {
        '1.1': 'cake (magic e), rain (vowel team), chat (digraph)',
        '1.2': 'keep (vowel team), shed (digraph), best (blend)',
        '1.3': 'kite (magic e), rain (vowel team), ship (digraph)',
        '1.4': 'boat (vowel team), shop (digraph), stop (blend)',
        '1.5': 'cute (magic e), out (vowel team), shut (digraph)',
        '2.1': 'bee (double vowel), sea (vowel team)',
        '2.2': 'toe (CVe), row (vowel team)',
        '2.3': 'pie (vowel team), my (y as vowel)',
        '3.1': 'city (soft c), cent (soft c)',
        '3.2': 'city (uses c), cent (uses c)',
        '4.1': 'character (k sound), chef (sh sound)',
        '4.2': 'mishap (s-h across syllables)',
        '4.3': 'lighthouse (t-h across morphemes)',
        '4.4': 'nowhere (compound word)',
        '4.5': 'bark (r-controlled), make (long vowel)',
        '5.1': 'help (final blend), below (across syllables)',
        '5.2': 'car (not a blend), forth (r-controlled)',
        '5.3': 'fast (final blend), sh (digraph not blend)',
        '5.4': 'first (r-controlled), inst (final blend)',
        '6.1': 'net (not a blend), enter (medial position)',
        '6.2': 'den (not a blend), under (medial position)',
        '6.3': 'knee (initial), ankle (medial)',
        '6.12': 'finger (medial), angel (across syllables)',
        '7.0': 'bus (exception), golf (l-blend)',
        '9.0': 'much (exception), teach (long vowel)',
        '10.0': 'age (long vowel), huge (long vowel)',
        '11.0': 'have (exception), love (exception)',
        '18.1': 'said (exception), aisle (different sound)',
        '18.2': 'bread (short e), break (long a)',
        '18.3': 'cow (ou sound), shoe (oo sound)',
        '18.4': 'soul (long o), low (long o)',
        '18.5': 'field (long e), friend (short e)',
        '18.6': 'sew (long o), build (short i)',
        '18.7': 'book (short oo), blood (short u)',
        '18.8': 'moon (long oo), blood (short u)',
        '18.9': 'out (ow sound), soul (long o)',
        '18.10': 'laugh (short a), aunt (varies)',
        '18.11': 'choir (different sound)',
        '18.12': 'eat (long e), break (long a)',
    }
    return non_examples.get(skill_id, '')

def get_additional_notes(skill_id, info):
    """Get additional notes for patterns"""
    notes = {
        '6.11': 'Note: 6.11 appears to be duplicate of 6.2 in the code',
        '8.0': 'Not explicitly defined in patterns, but used for compound words',
        '12.1': 'Specific words where closed syllable has long vowel',
        '14.1': 'Y acts as vowel at end of word or when only vowel',
        '15.1': 'Schwa sound in unstressed syllables',
        '15.2': 'Schwa sound in unstressed syllables',
        '15.3': 'Schwa sound in unstressed syllables',
        '17.0': 'Not pattern-based, depends on preceding sound',
        '19.0': 'Vocabulary skill, not pattern-based',
        '21.1': 'Same as 8.0 - compound words',
        '21.2': 'Two consonants between vowels',
        '21.5': 'One consonant between vowels',
        '22.0': 'Meaning-changing units at word beginning',
        '23.1': 'Grammatical units at word ending',
        '23.2': 'Special pronunciation patterns',
        '24.0': 'CVC pattern doubles final consonant',
        '25.0': 'Drop silent e before vowel suffix',
        '26.0': 'Spelling change before suffixes',
        '27.1': 'Regular plural formation',
        '27.2': 'Plural after sibilant sounds',
        '27.3': 'Special spelling change for plural',
        '27.5': 'No pattern - must memorize',
        '28.0': 'Like 24.0 but for 2-syllable words',
        '29.0': 'Distinguishing homophones',
        '30.0': 'Two words combined with apostrophe',
    }
    return notes.get(skill_id, '')

def main():
    """Generate complete CSV files"""
    print("Creating COMPLETE Phonics Rules CSV Files...")
    print("=" * 60)
    
    # Create complete master rules
    print("\n1. Creating Complete Master Rules CSV...")
    master_df = create_complete_master_rules_csv()
    
    print("\n" + "=" * 60)
    print("CSV file created successfully!")
    print("\nFile created:")
    print("- phonics_master_rules_complete.csv")
    
    # Show summary
    print(f"\nTotal skills documented: {len(master_df)}")
    print(f"Pattern types included: {master_df['pattern_type'].nunique()}")
    print("\nPattern type distribution:")
    print(master_df['pattern_type'].value_counts())

if __name__ == "__main__":
    main()