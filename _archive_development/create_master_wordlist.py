#!/usr/bin/env python3
"""
Create Master Word List in Book6 Template Format
Combines all corrected word lists into a single CSV following the book6.csv template
"""

import pandas as pd
import os
from collections import OrderedDict

def create_master_wordlist():
    """Create a master word list combining all corrected word lists"""
    
    corrected_dir = '/Users/<USER>/word-generator/word_lists_corrected'
    output_path = '/Users/<USER>/word-generator/master_wordlist_book6_format.csv'
    
    # Define skill categories for organization (following OG sequence)
    skill_categories = OrderedDict([
        ('# CVC WORDS', ['1.1', '1.2', '1.3', '1.4', '1.5']),
        ('# CV WORDS (OPEN SYLLABLES)', ['2.1', '2.2', '2.3']),
        ('# C OR K SPELLING RULE', ['3.1', '3.2']),
        ('# DIGRAPHS', ['4.1', '4.2', '4.3', '4.4', '4.5']),
        ('# INITIAL BLENDS', ['5.1', '5.2', '5.3', '5.4']),
        ('# FINAL BLENDS', ['6.1', '6.2', '6.3', '6.4', '6.5', '6.6', '6.7', '6.8', '6.9', 
                           '6.10', '6.11', '6.12', '6.13', '6.14', '6.15']),
        ('# FLOSS RULE', ['7.0']),
        ('# BACKPACK RULE (COMPOUND WORDS)', ['8.0']),
        ('# CATCH RULE', ['9.0']),
        ('# BRIDGE RULE', ['10.0']),
        ('# MAGIC E', ['11.0']),
        ('# CLOSED SYLLABLE EXCEPTIONS', ['12.1']),
        ('# SOFT SOUNDS', ['13.1', '13.2']),
        ('# Y AS VOWEL', ['14.1']),
        ('# SCHWA SOUNDS', ['15.1', '15.2', '15.3']),
        ('# SILENT LETTERS', ['16.0']),
        ('# 3 SOUNDS OF ED', ['17.0']),
        ('# VOWEL TEAMS - LONG A', ['18.1']),
        ('# VOWEL TEAMS - LONG E', ['18.2']),
        ('# VOWEL TEAMS - LONG O', ['18.3']),
        ('# VOWEL TEAMS - OW SOUND', ['18.4']),
        ('# VOWEL TEAMS - LONG I', ['18.5']),
        ('# VOWEL TEAMS - LONG U', ['18.6']),
        ('# VOWEL TEAMS - LONG OO', ['18.7']),
        ('# VOWEL TEAMS - SHORT OO', ['18.8']),
        ('# VOWEL TEAMS - OU SAYS U', ['18.9']),
        ('# VOWEL TEAMS - AW SOUND', ['18.10']),
        ('# VOWEL TEAMS - OY SOUND', ['18.11']),
        ('# VOWEL TEAMS - EA SAYS SHORT E', ['18.12']),
        ('# HOMOPHONES', ['19.0']),
        ('# R-CONTROLLED VOWELS', ['20.1', '20.2', '20.3', '20.4', '20.5']),
        ('# SYLLABICATION PATTERNS', ['21.1', '21.2', '21.5']),
        ('# MORPHOLOGY - PREFIXES', ['22.0']),
        ('# MORPHOLOGY - SUFFIXES', ['23.1', '23.2']),
        ('# DOUBLING RULES', ['24.0', '28.0']),
        ('# E-DROPPING RULE', ['25.0']),
        ('# Y TO I RULE', ['26.0']),
        ('# PLURALS', ['27.1', '27.2', '27.3', '27.5']),
        ('# SILENT E (NOT PLURAL)', ['29.0']),
        ('# CONTRACTIONS', ['30.0'])
    ])
    
    # Columns to match book6 template
    columns = ['skill_id', 'word', 'primary_pattern', 'pattern_position', 
               'is_HF', 'is_heart', 'irregular_part', 'irregular_sound', 
               'syllable_breaks', 'word_type', 'notes']
    
    # Create master dataframe
    all_rows = []
    
    # Process each category
    for category_name, skill_ids in skill_categories.items():
        # Add category header
        category_row = {col: '' for col in columns}
        category_row['skill_id'] = category_name
        all_rows.append(category_row)
        
        # Process each skill in this category
        for skill_id in skill_ids:
            # Find the corresponding file
            skill_files = [f for f in os.listdir(corrected_dir) 
                          if f.startswith(f'skill_{skill_id}_') and f.endswith('.csv')]
            
            if skill_files:
                skill_file = skill_files[0]
                skill_path = os.path.join(corrected_dir, skill_file)
                
                try:
                    # Read the skill file
                    df = pd.read_csv(skill_path)
                    
                    # Process each word
                    for _, row in df.iterrows():
                        # Create new row matching book6 format
                        new_row = {
                            'skill_id': skill_id,
                            'word': row['word'],
                            'primary_pattern': row.get('primary_pattern', ''),
                            'pattern_position': row.get('pattern_position', ''),
                            'is_HF': 'TRUE' if row.get('is_HF', False) else 'FALSE',
                            'is_heart': 'TRUE' if row.get('is_heart', False) else 'FALSE',
                            'irregular_part': row.get('irregular_part', ''),
                            'irregular_sound': row.get('irregular_sound', ''),
                            'syllable_breaks': row.get('syllable_breaks', row['word']),
                            'word_type': row.get('word_type', 'target'),
                            'notes': row.get('notes', '')
                        }
                        all_rows.append(new_row)
                    
                    print(f"Added {len(df)} words from skill {skill_id}")
                    
                except Exception as e:
                    print(f"Error reading {skill_file}: {e}")
        
        # Add empty row after each category
        empty_row = {col: '' for col in columns}
        all_rows.append(empty_row)
    
    # Create final dataframe
    master_df = pd.DataFrame(all_rows, columns=columns)
    
    # Save to CSV
    master_df.to_csv(output_path, index=False, encoding='utf-8-sig')
    
    print(f"\n{'='*60}")
    print("MASTER WORD LIST CREATED!")
    print(f"{'='*60}")
    print(f"Total rows: {len(master_df)}")
    print(f"Output file: {output_path}")
    
    # Generate summary statistics
    data_rows = master_df[~master_df['skill_id'].str.startswith('#', na=False)]
    data_rows = data_rows[data_rows['word'] != '']
    
    print(f"\nStatistics:")
    print(f"- Total words: {len(data_rows)}")
    print(f"- Heart words: {len(data_rows[data_rows['is_heart'] == 'TRUE'])}")
    print(f"- High frequency words: {len(data_rows[data_rows['is_HF'] == 'TRUE'])}")
    print(f"- Unique skills: {data_rows['skill_id'].nunique()}")

def create_compact_version():
    """Create a more compact version without empty rows and headers"""
    
    corrected_dir = '/Users/<USER>/word-generator/word_lists_corrected'
    output_path = '/Users/<USER>/word-generator/master_wordlist_compact.csv'
    
    # Columns to match book6 template
    columns = ['skill_id', 'word', 'primary_pattern', 'pattern_position', 
               'is_HF', 'is_heart', 'irregular_part', 'irregular_sound', 
               'syllable_breaks', 'word_type', 'notes']
    
    all_rows = []
    
    # Get all CSV files
    csv_files = [f for f in os.listdir(corrected_dir) 
                 if f.startswith('skill_') and f.endswith('.csv') 
                 and 'summary' not in f and 'report' not in f]
    
    for csv_file in sorted(csv_files):
        skill_path = os.path.join(corrected_dir, csv_file)
        
        try:
            df = pd.read_csv(skill_path)
            
            for _, row in df.iterrows():
                new_row = {
                    'skill_id': row.get('skill_id', ''),
                    'word': row['word'],
                    'primary_pattern': row.get('primary_pattern', ''),
                    'pattern_position': row.get('pattern_position', ''),
                    'is_HF': 'TRUE' if row.get('is_HF', False) else 'FALSE',
                    'is_heart': 'TRUE' if row.get('is_heart', False) else 'FALSE',
                    'irregular_part': row.get('irregular_part', ''),
                    'irregular_sound': row.get('irregular_sound', ''),
                    'syllable_breaks': row.get('syllable_breaks', row['word']),
                    'word_type': row.get('word_type', 'target'),
                    'notes': row.get('notes', '')
                }
                all_rows.append(new_row)
        
        except Exception as e:
            print(f"Error reading {csv_file}: {e}")
    
    # Create and save compact dataframe
    compact_df = pd.DataFrame(all_rows, columns=columns)
    compact_df.to_csv(output_path, index=False, encoding='utf-8-sig')
    
    print(f"\nCompact version created: {output_path}")
    print(f"Total words: {len(compact_df)}")

if __name__ == "__main__":
    print("Creating master word list in book6 format...")
    create_master_wordlist()
    
    print("\n" + "="*60)
    print("Creating compact version...")
    create_compact_version()
