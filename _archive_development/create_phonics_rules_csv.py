#!/usr/bin/env python3
"""
Create CSV files documenting all phonics rules and patterns
from the phoneme-aware decodable generator.
"""

import pandas as pd
import csv

def create_master_rules_csv():
    """Create comprehensive rules CSV for all skills"""
    
    rules = []
    
    # CVC Rules (1.1-1.5)
    cvc_rules = [
        {
            'skill_id': '1.1',
            'skill_name': 'CVC short a',
            'pattern_type': 'CVC',
            'graphemes': 'a',
            'required_phonemes': 'AE',
            'phoneme_description': 'short /a/ as in cat',
            'position_constraints': 'medial (vowel position)',
            'word_structure': '3-letter CVC only',
            'special_rules': 'No blends, digraphs, or vowel teams allowed',
            'examples': 'cat, hat, mat, sat, bat',
            'non_examples': 'cake (magic e), rain (vowel team), chat (digraph)'
        },
        {
            'skill_id': '1.2',
            'skill_name': 'CVC short e',
            'pattern_type': 'CVC',
            'graphemes': 'e',
            'required_phonemes': 'EH',
            'phoneme_description': 'short /e/ as in bed',
            'position_constraints': 'medial (vowel position)',
            'word_structure': '3-letter CVC only',
            'special_rules': 'No blends, digraphs, or vowel teams allowed',
            'examples': 'bed, get, met, set, wet',
            'non_examples': 'keep (vowel team), shed (digraph), best (blend)'
        },
        {
            'skill_id': '1.3',
            'skill_name': 'CVC short i',
            'pattern_type': 'CVC',
            'graphemes': 'i',
            'required_phonemes': 'IH',
            'phoneme_description': 'short /i/ as in sit',
            'position_constraints': 'medial (vowel position)',
            'word_structure': '3-letter CVC only',
            'special_rules': 'No blends, digraphs, or vowel teams allowed',
            'examples': 'sit, hit, bit, fit, pit',
            'non_examples': 'kite (magic e), rain (vowel team), ship (digraph)'
        },
        {
            'skill_id': '1.4',
            'skill_name': 'CVC short o',
            'pattern_type': 'CVC',
            'graphemes': 'o',
            'required_phonemes': 'AA',
            'phoneme_description': 'short /o/ as in hot',
            'position_constraints': 'medial (vowel position)',
            'word_structure': '3-letter CVC only',
            'special_rules': 'No blends, digraphs, or vowel teams allowed',
            'examples': 'hot, pot, dot, got, lot',
            'non_examples': 'boat (vowel team), shop (digraph), stop (blend)'
        },
        {
            'skill_id': '1.5',
            'skill_name': 'CVC short u',
            'pattern_type': 'CVC',
            'graphemes': 'u',
            'required_phonemes': 'AH',
            'phoneme_description': 'short /u/ as in cup',
            'position_constraints': 'medial (vowel position)',
            'word_structure': '3-letter CVC only',
            'special_rules': 'No blends, digraphs, or vowel teams allowed',
            'examples': 'cup, cut, but, nut, hut',
            'non_examples': 'cute (magic e), out (vowel team), shut (digraph)'
        }
    ]
    
    # CV Rules (2.1-2.3)
    cv_rules = [
        {
            'skill_id': '2.1',
            'skill_name': 'CV long e',
            'pattern_type': 'CV open syllable',
            'graphemes': 'e',
            'required_phonemes': 'IY',
            'phoneme_description': 'long /e/ as in be',
            'position_constraints': 'final',
            'word_structure': 'CV structure only',
            'special_rules': 'Limited to specific words: be, he, me, we, she',
            'examples': 'be, he, me, we, she',
            'non_examples': 'bee (double vowel), sea (vowel team)'
        },
        {
            'skill_id': '2.2',
            'skill_name': 'CV long o',
            'pattern_type': 'CV open syllable',
            'graphemes': 'o',
            'required_phonemes': 'OW',
            'phoneme_description': 'long /o/ as in go',
            'position_constraints': 'final',
            'word_structure': 'CV structure only',
            'special_rules': 'Limited to specific words: go, no, so',
            'examples': 'go, no, so',
            'non_examples': 'toe (CVe), row (vowel team)'
        },
        {
            'skill_id': '2.3',
            'skill_name': 'CV long i',
            'pattern_type': 'CV open syllable',
            'graphemes': 'i',
            'required_phonemes': 'AY',
            'phoneme_description': 'long /i/ as in I',
            'position_constraints': 'final or standalone',
            'word_structure': 'CV structure only',
            'special_rules': 'Limited to specific words: I, hi',
            'examples': 'I, hi',
            'non_examples': 'pie (vowel team), my (y as vowel)'
        }
    ]
    
    # C/K Rules (3.1-3.2)
    ck_rules = [
        {
            'skill_id': '3.1',
            'skill_name': 'c before a,o,u',
            'pattern_type': 'hard c rule',
            'graphemes': 'c',
            'required_phonemes': 'K',
            'phoneme_description': 'hard /k/ sound',
            'position_constraints': 'before a, o, u',
            'word_structure': 'any',
            'special_rules': 'c makes /k/ sound before a, o, u',
            'examples': 'cat, cot, cup, can, come',
            'non_examples': 'city (soft c), cent (soft c)'
        },
        {
            'skill_id': '3.2',
            'skill_name': 'k before e,i,y',
            'pattern_type': 'k spelling rule',
            'graphemes': 'k',
            'required_phonemes': 'K',
            'phoneme_description': 'hard /k/ sound',
            'position_constraints': 'before e, i, y',
            'word_structure': 'any',
            'special_rules': 'Use k (not c) before e, i, y',
            'examples': 'kit, key, kid, kite, keep',
            'non_examples': 'city (uses c), cent (uses c)'
        }
    ]
    
    # Digraph Rules (4.1-4.5)
    digraph_rules = [
        {
            'skill_id': '4.1',
            'skill_name': 'ch digraph',
            'pattern_type': 'digraph',
            'graphemes': 'ch',
            'required_phonemes': 'CH',
            'phoneme_description': '/ch/ as in chip',
            'position_constraints': 'any position',
            'word_structure': 'any',
            'special_rules': 'Two letters make one sound',
            'examples': 'chip, chat, much, lunch, chop',
            'non_examples': 'character (k sound), chef (sh sound)'
        },
        {
            'skill_id': '4.2',
            'skill_name': 'sh digraph',
            'pattern_type': 'digraph',
            'graphemes': 'sh',
            'required_phonemes': 'SH',
            'phoneme_description': '/sh/ as in ship',
            'position_constraints': 'any position',
            'word_structure': 'any',
            'special_rules': 'Two letters make one sound',
            'examples': 'ship, shop, wish, cash, she',
            'non_examples': 'mishap (s-h across syllables)'
        },
        {
            'skill_id': '4.3',
            'skill_name': 'th digraph',
            'pattern_type': 'digraph',
            'graphemes': 'th',
            'required_phonemes': 'TH or DH',
            'phoneme_description': 'voiced /th/ (this) or unvoiced /th/ (thin)',
            'position_constraints': 'any position',
            'word_structure': 'any',
            'special_rules': 'Can be voiced or unvoiced',
            'examples': 'this, that, thin, bath, with',
            'non_examples': 'lighthouse (t-h across morphemes)'
        },
        {
            'skill_id': '4.4',
            'skill_name': 'wh digraph',
            'pattern_type': 'digraph',
            'graphemes': 'wh',
            'required_phonemes': 'W or HH W',
            'phoneme_description': '/w/ or /hw/ (regional)',
            'position_constraints': 'initial only',
            'word_structure': 'any',
            'special_rules': 'Always at beginning of words/syllables',
            'examples': 'when, what, where, why, which',
            'non_examples': 'nowhere (compound word)'
        },
        {
            'skill_id': '4.5',
            'skill_name': 'ck digraph',
            'pattern_type': 'digraph',
            'graphemes': 'ck',
            'required_phonemes': 'K',
            'phoneme_description': '/k/ as in back',
            'position_constraints': 'final only',
            'word_structure': 'after short vowel',
            'special_rules': 'Only after short vowels in one-syllable words',
            'examples': 'back, pack, sick, lock, duck',
            'non_examples': 'bark (r-controlled), make (long vowel)'
        }
    ]
    
    # Initial Blend Rules (5.1-5.4)
    initial_blend_rules = [
        {
            'skill_id': '5.1',
            'skill_name': 'l-blends',
            'pattern_type': 'initial blend',
            'graphemes': 'bl, cl, fl, gl, pl, sl',
            'required_phonemes': 'B L, K L, F L, G L, P L, S L',
            'phoneme_description': 'consonant + /l/',
            'position_constraints': 'initial only',
            'word_structure': 'any',
            'special_rules': 'Two consonant sounds blend together',
            'examples': 'black, clap, flag, glad, play, slow',
            'non_examples': 'help (final blend), below (across syllables)'
        },
        {
            'skill_id': '5.2',
            'skill_name': 'r-blends',
            'pattern_type': 'initial blend',
            'graphemes': 'br, cr, dr, fr, gr, pr, tr',
            'required_phonemes': 'B R, K R, D R, F R, G R, P R, T R',
            'phoneme_description': 'consonant + /r/',
            'position_constraints': 'initial only',
            'word_structure': 'any',
            'special_rules': 'Two consonant sounds blend together',
            'examples': 'bring, crab, drip, from, grab, print, trip',
            'non_examples': 'car (not a blend), forth (r-controlled)'
        },
        {
            'skill_id': '5.3',
            'skill_name': 's-blends',
            'pattern_type': 'initial blend',
            'graphemes': 'sc, sk, sm, sn, sp, st, sw',
            'required_phonemes': 'S K, S K, S M, S N, S P, S T, S W',
            'phoneme_description': '/s/ + consonant',
            'position_constraints': 'initial only',
            'word_structure': 'any',
            'special_rules': 'Two consonant sounds blend together',
            'examples': 'scan, skip, smell, snap, spot, stop, swim',
            'non_examples': 'fast (final blend), sh (digraph not blend)'
        },
        {
            'skill_id': '5.4',
            'skill_name': '3-letter blends',
            'pattern_type': 'initial blend',
            'graphemes': 'scr, spr, str, spl, squ, thr',
            'required_phonemes': 'S K R, S P R, S T R, S P L, S K W, TH R',
            'phoneme_description': 'three consonant sounds',
            'position_constraints': 'initial only',
            'word_structure': 'any',
            'special_rules': 'Three consonant sounds blend together',
            'examples': 'scrap, spring, string, split, square, three',
            'non_examples': 'first (r-controlled), inst (final blend)'
        }
    ]
    
    # Final Blend Rules (6.1-6.15)
    final_blend_rules = [
        {
            'skill_id': '6.1',
            'skill_name': 'nt ending',
            'pattern_type': 'final blend',
            'graphemes': 'nt',
            'required_phonemes': 'N T',
            'phoneme_description': '/n/ + /t/',
            'position_constraints': 'final only',
            'word_structure': 'any',
            'special_rules': 'Two consonant sounds at word end',
            'examples': 'ant, went, hunt, plant, print',
            'non_examples': 'net (not a blend), enter (medial position)'
        },
        {
            'skill_id': '6.2',
            'skill_name': 'nd ending',
            'pattern_type': 'final blend',
            'graphemes': 'nd',
            'required_phonemes': 'N D',
            'phoneme_description': '/n/ + /d/',
            'position_constraints': 'final only',
            'word_structure': 'any',
            'special_rules': 'Two consonant sounds at word end',
            'examples': 'and, end, hand, stand, wind',
            'non_examples': 'den (not a blend), under (medial position)'
        },
        {
            'skill_id': '6.3',
            'skill_name': 'nk ending',
            'pattern_type': 'final blend',
            'graphemes': 'nk',
            'required_phonemes': 'NG K',
            'phoneme_description': '/ng/ + /k/',
            'position_constraints': 'final only',
            'word_structure': 'any',
            'special_rules': 'Makes /ngk/ sound',
            'examples': 'ink, bank, think, trunk, drink',
            'non_examples': 'knee (initial), ankle (medial)'
        },
        {
            'skill_id': '6.12',
            'skill_name': 'ng ending',
            'pattern_type': 'final blend',
            'graphemes': 'ng',
            'required_phonemes': 'NG',
            'phoneme_description': '/ng/ as in sing',
            'position_constraints': 'final only',
            'word_structure': 'any',
            'special_rules': 'Single sound (not n+g)',
            'examples': 'sing, ring, thing, strong, long',
            'non_examples': 'finger (medial), angel (across syllables)'
        }
    ]
    
    # Special Rules (7.0-10.0)
    special_rules_list = [
        {
            'skill_id': '7.0',
            'skill_name': 'FLOSS rule',
            'pattern_type': 'doubling rule',
            'graphemes': 'ff, ll, ss, zz',
            'required_phonemes': 'F, L, S, Z',
            'phoneme_description': 'same as single letter',
            'position_constraints': 'final after short vowel',
            'word_structure': 'one-syllable words',
            'special_rules': 'Double f, l, s, z after short vowel in one-syllable words',
            'examples': 'stuff, bell, pass, buzz, cliff',
            'non_examples': 'bus (exception), golf (l-blend)'
        },
        {
            'skill_id': '9.0',
            'skill_name': 'Catch rule',
            'pattern_type': 'tch pattern',
            'graphemes': 'tch',
            'required_phonemes': 'CH',
            'phoneme_description': '/ch/ as in catch',
            'position_constraints': 'final after short vowel',
            'word_structure': 'one-syllable words',
            'special_rules': 'Use tch after short vowel (not ch)',
            'examples': 'catch, match, pitch, notch, clutch',
            'non_examples': 'much (exception), teach (long vowel)'
        },
        {
            'skill_id': '10.0',
            'skill_name': 'Bridge rule',
            'pattern_type': 'dge pattern',
            'graphemes': 'dge',
            'required_phonemes': 'JH',
            'phoneme_description': '/j/ as in judge',
            'position_constraints': 'final after short vowel',
            'word_structure': 'one-syllable words',
            'special_rules': 'Use dge after short vowel (not ge)',
            'examples': 'badge, edge, bridge, lodge, judge',
            'non_examples': 'age (long vowel), huge (long vowel)'
        },
        {
            'skill_id': '11.0',
            'skill_name': 'Magic E',
            'pattern_type': 'vowel pattern',
            'graphemes': 'VCe',
            'required_phonemes': 'long vowel sounds',
            'phoneme_description': 'makes preceding vowel long',
            'position_constraints': 'final e with consonant between',
            'word_structure': 'VCe pattern',
            'special_rules': 'Silent e makes vowel say its name',
            'examples': 'make, pete, bike, hope, cute',
            'non_examples': 'have (exception), love (exception)'
        }
    ]
    
    # Vowel Team Rules (18.1-18.12)
    vowel_team_rules = [
        {
            'skill_id': '18.1',
            'skill_name': 'Long a teams',
            'pattern_type': 'vowel team',
            'graphemes': 'ai, ay, ei, eigh',
            'required_phonemes': 'EY',
            'phoneme_description': 'long /a/ as in rain',
            'position_constraints': 'ai/ei medial, ay final, eigh any',
            'word_structure': 'any',
            'special_rules': 'Must produce long a sound',
            'examples': 'rain, play, vein, eight, neighbor',
            'non_examples': 'said (exception), aisle (different sound)'
        },
        {
            'skill_id': '18.2',
            'skill_name': 'Long e teams',
            'pattern_type': 'vowel team',
            'graphemes': 'ea, ee, ie, ey',
            'required_phonemes': 'IY',
            'phoneme_description': 'long /e/ as in see',
            'position_constraints': 'ea/ee/ie medial, ey final',
            'word_structure': 'any',
            'special_rules': 'Must produce long e sound',
            'examples': 'eat, see, field, key, turkey',
            'non_examples': 'bread (short e), break (long a)'
        },
        {
            'skill_id': '18.3',
            'skill_name': 'Long o teams',
            'pattern_type': 'vowel team',
            'graphemes': 'oa, oe, ow',
            'required_phonemes': 'OW',
            'phoneme_description': 'long /o/ as in boat',
            'position_constraints': 'oa/oe medial, ow final or before n',
            'word_structure': 'any',
            'special_rules': 'Must produce long o sound',
            'examples': 'boat, toe, grow, show, own',
            'non_examples': 'cow (ou sound), shoe (oo sound)'
        },
        {
            'skill_id': '18.4',
            'skill_name': 'ow sound',
            'pattern_type': 'vowel team',
            'graphemes': 'ou, ow',
            'required_phonemes': 'AW',
            'phoneme_description': '/ow/ as in cow',
            'position_constraints': 'any',
            'word_structure': 'any',
            'special_rules': 'Must produce /ow/ diphthong',
            'examples': 'out, house, cow, now, howl',
            'non_examples': 'soul (long o), low (long o)'
        },
        {
            'skill_id': '18.5',
            'skill_name': 'Long i teams',
            'pattern_type': 'vowel team',
            'graphemes': 'ie, igh',
            'required_phonemes': 'AY',
            'phoneme_description': 'long /i/ as in night',
            'position_constraints': 'any',
            'word_structure': 'any',
            'special_rules': 'Must produce long i sound',
            'examples': 'pie, tie, high, night, bright',
            'non_examples': 'field (long e), friend (short e)'
        },
        {
            'skill_id': '18.6',
            'skill_name': 'Long u teams',
            'pattern_type': 'vowel team',
            'graphemes': 'ue, ew, eu',
            'required_phonemes': 'UW or YUW',
            'phoneme_description': 'long /u/ as in blue or cute',
            'position_constraints': 'ue/eu medial, ew often final',
            'word_structure': 'any',
            'special_rules': 'Can be /oo/ or /yoo/ sound',
            'examples': 'blue, new, few, feud, rescue',
            'non_examples': 'sew (long o), build (short i)'
        },
        {
            'skill_id': '18.7',
            'skill_name': 'Long oo',
            'pattern_type': 'vowel team',
            'graphemes': 'oo',
            'required_phonemes': 'UW',
            'phoneme_description': 'long /oo/ as in moon',
            'position_constraints': 'medial',
            'word_structure': 'any',
            'special_rules': 'Must produce long oo sound',
            'examples': 'moon, food, pool, boot, tooth',
            'non_examples': 'book (short oo), blood (short u)'
        },
        {
            'skill_id': '18.8',
            'skill_name': 'Short oo',
            'pattern_type': 'vowel team',
            'graphemes': 'oo',
            'required_phonemes': 'UH',
            'phoneme_description': 'short /oo/ as in book',
            'position_constraints': 'medial',
            'word_structure': 'any',
            'special_rules': 'Must produce short oo sound',
            'examples': 'book, look, good, wood, foot',
            'non_examples': 'moon (long oo), blood (short u)'
        },
        {
            'skill_id': '18.9',
            'skill_name': 'ou says u',
            'pattern_type': 'vowel team',
            'graphemes': 'ou',
            'required_phonemes': 'AH',
            'phoneme_description': 'short /u/ as in touch',
            'position_constraints': 'medial',
            'word_structure': 'any',
            'special_rules': 'ou making short u sound',
            'examples': 'touch, young, country, double, trouble',
            'non_examples': 'out (ow sound), soul (long o)'
        },
        {
            'skill_id': '18.10',
            'skill_name': 'aw sound',
            'pattern_type': 'vowel team',
            'graphemes': 'au, aw',
            'required_phonemes': 'AO',
            'phoneme_description': '/aw/ as in saw',
            'position_constraints': 'au medial, aw often final',
            'word_structure': 'any',
            'special_rules': 'Consistent /aw/ sound',
            'examples': 'saw, paw, haul, cause, autumn',
            'non_examples': 'laugh (short a), aunt (varies)'
        },
        {
            'skill_id': '18.11',
            'skill_name': 'oy sound',
            'pattern_type': 'vowel team',
            'graphemes': 'oi, oy',
            'required_phonemes': 'OY',
            'phoneme_description': '/oy/ as in boy',
            'position_constraints': 'oi medial, oy final',
            'word_structure': 'any',
            'special_rules': 'Consistent /oy/ sound',
            'examples': 'oil, coin, boy, toy, enjoy',
            'non_examples': 'choir (different sound)'
        },
        {
            'skill_id': '18.12',
            'skill_name': 'ea says e',
            'pattern_type': 'vowel team',
            'graphemes': 'ea',
            'required_phonemes': 'EH',
            'phoneme_description': 'short /e/ as in bread',
            'position_constraints': 'medial',
            'word_structure': 'any',
            'special_rules': 'ea making short e sound',
            'examples': 'bread, head, dead, spread, heavy',
            'non_examples': 'eat (long e), break (long a)'
        }
    ]
    
    # R-controlled Rules (20.1-20.5)
    r_controlled_rules = [
        {
            'skill_id': '20.1',
            'skill_name': 'er sound',
            'pattern_type': 'r-controlled',
            'graphemes': 'er, ir, ur',
            'required_phonemes': 'ER',
            'phoneme_description': '/er/ as in her',
            'position_constraints': 'any',
            'word_structure': 'any',
            'special_rules': 'All three spellings make same sound',
            'examples': 'her, bird, turn, fern, first',
            'non_examples': 'here (long e+r), fire (long i+r)'
        },
        {
            'skill_id': '20.2',
            'skill_name': 'or sound',
            'pattern_type': 'r-controlled',
            'graphemes': 'or',
            'required_phonemes': 'AO R or AO',
            'phoneme_description': '/or/ as in for',
            'position_constraints': 'any',
            'word_structure': 'any',
            'special_rules': 'Consistent /or/ sound',
            'examples': 'for, corn, sport, north, born',
            'non_examples': 'word (er sound), work (er sound)'
        },
        {
            'skill_id': '20.3',
            'skill_name': 'ar sound',
            'pattern_type': 'r-controlled',
            'graphemes': 'ar',
            'required_phonemes': 'AA R',
            'phoneme_description': '/ar/ as in car',
            'position_constraints': 'any',
            'word_structure': 'any',
            'special_rules': 'Consistent /ar/ sound',
            'examples': 'car, star, park, hard, start',
            'non_examples': 'war (or sound), dollar (er sound)'
        },
        {
            'skill_id': '20.4',
            'skill_name': 'war sound',
            'pattern_type': 'r-controlled',
            'graphemes': 'war',
            'required_phonemes': 'AO R',
            'phoneme_description': '/or/ as in war',
            'position_constraints': 'any',
            'word_structure': 'any',
            'special_rules': 'w changes ar to or sound',
            'examples': 'war, warm, warn, wart, dwarf',
            'non_examples': 'ward (varies), beware (ar sound)'
        },
        {
            'skill_id': '20.5',
            'skill_name': 'wor sound',
            'pattern_type': 'r-controlled',
            'graphemes': 'wor',
            'required_phonemes': 'ER',
            'phoneme_description': '/er/ as in work',
            'position_constraints': 'any',
            'word_structure': 'any',
            'special_rules': 'w changes or to er sound',
            'examples': 'work, word, world, worm, worth',
            'non_examples': 'worn (or sound), worry (ur sound)'
        }
    ]
    
    # Combine all rules
    rules.extend(cvc_rules)
    rules.extend(cv_rules)
    rules.extend(ck_rules)
    rules.extend(digraph_rules)
    rules.extend(initial_blend_rules)
    rules.extend(final_blend_rules)
    rules.extend(special_rules_list)
    rules.extend(vowel_team_rules)
    rules.extend(r_controlled_rules)
    
    # Create DataFrame and save
    df = pd.DataFrame(rules)
    df.to_csv('/Users/<USER>/word-generator/phonics_master_rules.csv', index=False)
    print(f"Created phonics_master_rules.csv with {len(rules)} rules")
    
    return df

def create_decodability_constraints_csv():
    """Create CSV documenting all decodability blocking rules"""
    
    constraints = []
    
    # Define all patterns that block decodability when not yet taught
    blocking_patterns = [
        # Vowel Teams
        {'pattern': 'eigh', 'pattern_type': 'vowel_team', 'introduced_at': '18.1', 'blocks_before': True},
        {'pattern': 'augh', 'pattern_type': 'vowel_team', 'introduced_at': 'not_taught', 'blocks_before': True},
        {'pattern': 'ough', 'pattern_type': 'vowel_team', 'introduced_at': 'not_taught', 'blocks_before': True},
        {'pattern': 'ai', 'pattern_type': 'vowel_team', 'introduced_at': '18.1', 'blocks_before': True},
        {'pattern': 'ay', 'pattern_type': 'vowel_team', 'introduced_at': '18.1', 'blocks_before': True},
        {'pattern': 'ea', 'pattern_type': 'vowel_team', 'introduced_at': '18.2', 'blocks_before': True},
        {'pattern': 'ee', 'pattern_type': 'vowel_team', 'introduced_at': '18.2', 'blocks_before': True},
        {'pattern': 'ei', 'pattern_type': 'vowel_team', 'introduced_at': '18.1', 'blocks_before': True},
        {'pattern': 'ey', 'pattern_type': 'vowel_team', 'introduced_at': '18.2', 'blocks_before': True},
        {'pattern': 'ie', 'pattern_type': 'vowel_team', 'introduced_at': '18.2', 'blocks_before': True},
        {'pattern': 'oa', 'pattern_type': 'vowel_team', 'introduced_at': '18.3', 'blocks_before': True},
        {'pattern': 'oe', 'pattern_type': 'vowel_team', 'introduced_at': '18.3', 'blocks_before': True},
        {'pattern': 'oi', 'pattern_type': 'vowel_team', 'introduced_at': '18.11', 'blocks_before': True},
        {'pattern': 'oo', 'pattern_type': 'vowel_team', 'introduced_at': '18.7', 'blocks_before': True},
        {'pattern': 'ou', 'pattern_type': 'vowel_team', 'introduced_at': '18.4', 'blocks_before': True},
        {'pattern': 'ow', 'pattern_type': 'vowel_team', 'introduced_at': '18.3', 'blocks_before': True},
        {'pattern': 'oy', 'pattern_type': 'vowel_team', 'introduced_at': '18.11', 'blocks_before': True},
        {'pattern': 'ue', 'pattern_type': 'vowel_team', 'introduced_at': '18.6', 'blocks_before': True},
        {'pattern': 'ui', 'pattern_type': 'vowel_team', 'introduced_at': 'not_taught', 'blocks_before': True},
        {'pattern': 'au', 'pattern_type': 'vowel_team', 'introduced_at': '18.10', 'blocks_before': True},
        {'pattern': 'aw', 'pattern_type': 'vowel_team', 'introduced_at': '18.10', 'blocks_before': True},
        {'pattern': 'ew', 'pattern_type': 'vowel_team', 'introduced_at': '18.6', 'blocks_before': True},
        {'pattern': 'eu', 'pattern_type': 'vowel_team', 'introduced_at': '18.6', 'blocks_before': True},
        {'pattern': 'igh', 'pattern_type': 'vowel_team', 'introduced_at': '18.5', 'blocks_before': True},
        
        # R-controlled
        {'pattern': 'war', 'pattern_type': 'r_controlled', 'introduced_at': '20.4', 'blocks_before': True},
        {'pattern': 'wor', 'pattern_type': 'r_controlled', 'introduced_at': '20.5', 'blocks_before': True},
        {'pattern': 'ar', 'pattern_type': 'r_controlled', 'introduced_at': '20.3', 'blocks_before': True},
        {'pattern': 'er', 'pattern_type': 'r_controlled', 'introduced_at': '20.1', 'blocks_before': True},
        {'pattern': 'ir', 'pattern_type': 'r_controlled', 'introduced_at': '20.1', 'blocks_before': True},
        {'pattern': 'or', 'pattern_type': 'r_controlled', 'introduced_at': '20.2', 'blocks_before': True},
        {'pattern': 'ur', 'pattern_type': 'r_controlled', 'introduced_at': '20.1', 'blocks_before': True},
        
        # Magic E
        {'pattern': 'VCe', 'pattern_type': 'magic_e', 'introduced_at': '11.0', 'blocks_before': True},
        
        # Initial Blends
        {'pattern': 'bl', 'pattern_type': 'initial_blend', 'introduced_at': '5.1', 'blocks_before': True},
        {'pattern': 'cl', 'pattern_type': 'initial_blend', 'introduced_at': '5.1', 'blocks_before': True},
        {'pattern': 'fl', 'pattern_type': 'initial_blend', 'introduced_at': '5.1', 'blocks_before': True},
        {'pattern': 'gl', 'pattern_type': 'initial_blend', 'introduced_at': '5.1', 'blocks_before': True},
        {'pattern': 'pl', 'pattern_type': 'initial_blend', 'introduced_at': '5.1', 'blocks_before': True},
        {'pattern': 'sl', 'pattern_type': 'initial_blend', 'introduced_at': '5.1', 'blocks_before': True},
        {'pattern': 'br', 'pattern_type': 'initial_blend', 'introduced_at': '5.2', 'blocks_before': True},
        {'pattern': 'cr', 'pattern_type': 'initial_blend', 'introduced_at': '5.2', 'blocks_before': True},
        {'pattern': 'dr', 'pattern_type': 'initial_blend', 'introduced_at': '5.2', 'blocks_before': True},
        {'pattern': 'fr', 'pattern_type': 'initial_blend', 'introduced_at': '5.2', 'blocks_before': True},
        {'pattern': 'gr', 'pattern_type': 'initial_blend', 'introduced_at': '5.2', 'blocks_before': True},
        {'pattern': 'pr', 'pattern_type': 'initial_blend', 'introduced_at': '5.2', 'blocks_before': True},
        {'pattern': 'tr', 'pattern_type': 'initial_blend', 'introduced_at': '5.2', 'blocks_before': True},
        {'pattern': 'sc', 'pattern_type': 'initial_blend', 'introduced_at': '5.3', 'blocks_before': True},
        {'pattern': 'sk', 'pattern_type': 'initial_blend', 'introduced_at': '5.3', 'blocks_before': True},
        {'pattern': 'sm', 'pattern_type': 'initial_blend', 'introduced_at': '5.3', 'blocks_before': True},
        {'pattern': 'sn', 'pattern_type': 'initial_blend', 'introduced_at': '5.3', 'blocks_before': True},
        {'pattern': 'sp', 'pattern_type': 'initial_blend', 'introduced_at': '5.3', 'blocks_before': True},
        {'pattern': 'st', 'pattern_type': 'initial_blend', 'introduced_at': '5.3', 'blocks_before': True},
        {'pattern': 'sw', 'pattern_type': 'initial_blend', 'introduced_at': '5.3', 'blocks_before': True},
        {'pattern': 'scr', 'pattern_type': 'initial_blend', 'introduced_at': '5.4', 'blocks_before': True},
        {'pattern': 'spr', 'pattern_type': 'initial_blend', 'introduced_at': '5.4', 'blocks_before': True},
        {'pattern': 'str', 'pattern_type': 'initial_blend', 'introduced_at': '5.4', 'blocks_before': True},
        {'pattern': 'spl', 'pattern_type': 'initial_blend', 'introduced_at': '5.4', 'blocks_before': True},
        {'pattern': 'squ', 'pattern_type': 'initial_blend', 'introduced_at': '5.4', 'blocks_before': True},
        {'pattern': 'thr', 'pattern_type': 'initial_blend', 'introduced_at': '5.4', 'blocks_before': True},
        
        # Final Blends
        {'pattern': 'nt', 'pattern_type': 'final_blend', 'introduced_at': '6.1', 'blocks_before': True},
        {'pattern': 'nd', 'pattern_type': 'final_blend', 'introduced_at': '6.2', 'blocks_before': True},
        {'pattern': 'nk', 'pattern_type': 'final_blend', 'introduced_at': '6.3', 'blocks_before': True},
        {'pattern': 'lt', 'pattern_type': 'final_blend', 'introduced_at': '6.4', 'blocks_before': True},
        {'pattern': 'ld', 'pattern_type': 'final_blend', 'introduced_at': '6.5', 'blocks_before': True},
        {'pattern': 'lf', 'pattern_type': 'final_blend', 'introduced_at': '6.6', 'blocks_before': True},
        {'pattern': 'lk', 'pattern_type': 'final_blend', 'introduced_at': '6.7', 'blocks_before': True},
        {'pattern': 'lm', 'pattern_type': 'final_blend', 'introduced_at': '6.8', 'blocks_before': True},
        {'pattern': 'lp', 'pattern_type': 'final_blend', 'introduced_at': '6.9', 'blocks_before': True},
        {'pattern': 'mp', 'pattern_type': 'final_blend', 'introduced_at': '6.10', 'blocks_before': True},
        {'pattern': 'ng', 'pattern_type': 'final_blend', 'introduced_at': '6.12', 'blocks_before': True},
        {'pattern': 'sk', 'pattern_type': 'final_blend', 'introduced_at': '6.13', 'blocks_before': True},
        {'pattern': 'st', 'pattern_type': 'final_blend', 'introduced_at': '6.14', 'blocks_before': True},
        {'pattern': 'ft', 'pattern_type': 'final_blend', 'introduced_at': '6.15', 'blocks_before': True},
        
        # Digraphs
        {'pattern': 'ch', 'pattern_type': 'digraph', 'introduced_at': '4.1', 'blocks_before': True},
        {'pattern': 'sh', 'pattern_type': 'digraph', 'introduced_at': '4.2', 'blocks_before': True},
        {'pattern': 'th', 'pattern_type': 'digraph', 'introduced_at': '4.3', 'blocks_before': True},
        {'pattern': 'wh', 'pattern_type': 'digraph', 'introduced_at': '4.4', 'blocks_before': True},
        {'pattern': 'ck', 'pattern_type': 'digraph', 'introduced_at': '4.5', 'blocks_before': True},
        
        # Special Patterns
        {'pattern': 'tch', 'pattern_type': 'special', 'introduced_at': '9.0', 'blocks_before': True},
        {'pattern': 'dge', 'pattern_type': 'special', 'introduced_at': '10.0', 'blocks_before': True},
        
        # Y as vowel
        {'pattern': 'y_as_vowel', 'pattern_type': 'special', 'introduced_at': '14.1', 'blocks_before': True},
        
        # Silent letters
        {'pattern': 'kn', 'pattern_type': 'silent', 'introduced_at': '16.0', 'blocks_before': True},
        {'pattern': 'wr', 'pattern_type': 'silent', 'introduced_at': '16.0', 'blocks_before': True},
        {'pattern': 'mb', 'pattern_type': 'silent', 'introduced_at': '16.0', 'blocks_before': True},
        {'pattern': 'gh', 'pattern_type': 'silent', 'introduced_at': '16.0', 'blocks_before': True},
        {'pattern': 'gn', 'pattern_type': 'silent', 'introduced_at': '16.0', 'blocks_before': True},
        
        # Soft sounds
        {'pattern': 'ge', 'pattern_type': 'soft_sound', 'introduced_at': '13.1', 'blocks_before': True},
        {'pattern': 'gi', 'pattern_type': 'soft_sound', 'introduced_at': '13.1', 'blocks_before': True},
        {'pattern': 'gy', 'pattern_type': 'soft_sound', 'introduced_at': '13.1', 'blocks_before': True},
        {'pattern': 'ce', 'pattern_type': 'soft_sound', 'introduced_at': '13.2', 'blocks_before': True},
        {'pattern': 'ci', 'pattern_type': 'soft_sound', 'introduced_at': '13.2', 'blocks_before': True},
        {'pattern': 'cy', 'pattern_type': 'soft_sound', 'introduced_at': '13.2', 'blocks_before': True},
    ]
    
    # Additional constraints for specific skills
    special_constraints = [
        {
            'constraint_type': 'CVC_structure',
            'applies_to': '1.1, 1.2, 1.3, 1.4, 1.5',
            'rule': 'Must be exactly 3 letters: consonant-vowel-consonant',
            'blocks': 'Words with blends, digraphs, vowel teams, or not 3 letters'
        },
        {
            'constraint_type': 'CV_structure',
            'applies_to': '2.1, 2.2, 2.3',
            'rule': 'Limited to specific words only',
            'blocks': 'Any words not in the allowed list'
        },
        {
            'constraint_type': 'heart_words',
            'applies_to': 'all',
            'rule': 'Heart words (irregular) allowed if taught before current skill',
            'blocks': 'Never blocks - provides exception'
        },
        {
            'constraint_type': 'frequency_threshold',
            'applies_to': 'all',
            'rule': 'Word frequency must be > 2 in Brown corpus',
            'blocks': 'Very rare words'
        }
    ]
    
    # Convert to list of dictionaries with additional info
    for pattern in blocking_patterns:
        constraints.append({
            'pattern': pattern['pattern'],
            'pattern_type': pattern['pattern_type'],
            'introduced_at_skill': pattern['introduced_at'],
            'blocks_words_before': 'Yes' if pattern['blocks_before'] else 'No',
            'detection_method': f"'{pattern['pattern']}' in word" if pattern['pattern'] != 'VCe' else 'VCe pattern detection',
            'example_blocked_words': get_example_blocked_words(pattern['pattern']),
            'notes': get_pattern_notes(pattern['pattern'])
        })
    
    # Add special constraints
    for constraint in special_constraints:
        constraints.append({
            'pattern': constraint['constraint_type'],
            'pattern_type': 'structural_rule',
            'introduced_at_skill': constraint['applies_to'],
            'blocks_words_before': 'Varies',
            'detection_method': constraint['rule'],
            'example_blocked_words': '',
            'notes': constraint['blocks']
        })
    
    # Create DataFrame and save
    df = pd.DataFrame(constraints)
    df.to_csv('/Users/<USER>/word-generator/decodability_constraints.csv', index=False)
    print(f"Created decodability_constraints.csv with {len(constraints)} constraints")
    
    return df

def get_example_blocked_words(pattern):
    """Get example words blocked by this pattern"""
    examples = {
        'ai': 'rain, train, mail',
        'ay': 'play, stay, day',
        'ea': 'eat, read, beach',
        'ee': 'see, tree, feet',
        'oa': 'boat, coat, road',
        'oo': 'moon, book, foot',
        'ou': 'out, house, cloud',
        'ow': 'cow, show, know',
        'ar': 'car, star, park',
        'er': 'her, water, sister',
        'or': 'for, horse, sport',
        'ch': 'chip, much, chair',
        'sh': 'ship, fish, wish',
        'th': 'this, with, three',
        'bl': 'black, blue, block',
        'st': 'stop, fast, best',
        'VCe': 'make, bike, hope',
        'tch': 'catch, watch, pitch',
        'dge': 'bridge, edge, judge'
    }
    return examples.get(pattern, '')

def get_pattern_notes(pattern):
    """Get notes about the pattern"""
    notes = {
        'ai': 'Typically medial position',
        'ay': 'Typically final position',
        'ea': 'Can make multiple sounds',
        'oo': 'Two different sounds (18.7 vs 18.8)',
        'ou': 'Multiple sounds possible',
        'ow': 'Two sounds: /ow/ or long o',
        'VCe': 'Silent e makes vowel long',
        'y_as_vowel': 'Y at end or as only vowel',
        'ge': 'Soft g before e, i, y',
        'ce': 'Soft c before e, i, y'
    }
    return notes.get(pattern, '')

def create_phoneme_mapping_csv():
    """Create CSV mapping CMU phonemes to human-readable descriptions"""
    
    phoneme_mappings = [
        # Vowels
        {'cmu_code': 'AA', 'ipa': '/ɑ/', 'description': 'short o as in hot', 'example_words': 'hot, pot, top, stop, doll'},
        {'cmu_code': 'AE', 'ipa': '/æ/', 'description': 'short a as in cat', 'example_words': 'cat, hat, bat, sat, apple'},
        {'cmu_code': 'AH', 'ipa': '/ʌ/', 'description': 'short u as in cup', 'example_words': 'cup, but, sun, come, love'},
        {'cmu_code': 'AO', 'ipa': '/ɔ/', 'description': 'aw as in law', 'example_words': 'law, saw, call, tall, water'},
        {'cmu_code': 'AW', 'ipa': '/aʊ/', 'description': 'ow as in cow', 'example_words': 'cow, how, now, out, house'},
        {'cmu_code': 'AY', 'ipa': '/aɪ/', 'description': 'long i as in my', 'example_words': 'my, sky, high, pie, light'},
        {'cmu_code': 'EH', 'ipa': '/ɛ/', 'description': 'short e as in bed', 'example_words': 'bed, get, said, bread, friend'},
        {'cmu_code': 'ER', 'ipa': '/ɝ/', 'description': 'er as in her', 'example_words': 'her, bird, turn, work, earth'},
        {'cmu_code': 'EY', 'ipa': '/eɪ/', 'description': 'long a as in day', 'example_words': 'day, make, rain, great, they'},
        {'cmu_code': 'IH', 'ipa': '/ɪ/', 'description': 'short i as in sit', 'example_words': 'sit, big, fish, gym, busy'},
        {'cmu_code': 'IY', 'ipa': '/i/', 'description': 'long e as in see', 'example_words': 'see, me, eat, key, field'},
        {'cmu_code': 'OW', 'ipa': '/oʊ/', 'description': 'long o as in go', 'example_words': 'go, home, boat, toe, know'},
        {'cmu_code': 'OY', 'ipa': '/ɔɪ/', 'description': 'oy as in boy', 'example_words': 'boy, toy, oil, coin, voice'},
        {'cmu_code': 'UH', 'ipa': '/ʊ/', 'description': 'short oo as in book', 'example_words': 'book, put, could, wolf, foot'},
        {'cmu_code': 'UW', 'ipa': '/u/', 'description': 'long oo as in moon', 'example_words': 'moon, blue, flew, soup, true'},
        
        # Consonants
        {'cmu_code': 'B', 'ipa': '/b/', 'description': 'b as in ball', 'example_words': 'ball, baby, tub, rabbit, number'},
        {'cmu_code': 'CH', 'ipa': '/tʃ/', 'description': 'ch as in chip', 'example_words': 'chip, much, chair, catch, kitchen'},
        {'cmu_code': 'D', 'ipa': '/d/', 'description': 'd as in dog', 'example_words': 'dog, did, add, ladder, middle'},
        {'cmu_code': 'DH', 'ipa': '/ð/', 'description': 'th as in this', 'example_words': 'this, that, mother, smooth, breathe'},
        {'cmu_code': 'F', 'ipa': '/f/', 'description': 'f as in fish', 'example_words': 'fish, off, phone, laugh, stuff'},
        {'cmu_code': 'G', 'ipa': '/g/', 'description': 'g as in go', 'example_words': 'go, big, dog, egg, ghost'},
        {'cmu_code': 'HH', 'ipa': '/h/', 'description': 'h as in hat', 'example_words': 'hat, home, who, behind, ahead'},
        {'cmu_code': 'JH', 'ipa': '/dʒ/', 'description': 'j as in jump', 'example_words': 'jump, judge, age, edge, gym'},
        {'cmu_code': 'K', 'ipa': '/k/', 'description': 'k as in kite', 'example_words': 'kite, cat, back, school, stomach'},
        {'cmu_code': 'L', 'ipa': '/l/', 'description': 'l as in love', 'example_words': 'love, bell, table, animal, apple'},
        {'cmu_code': 'M', 'ipa': '/m/', 'description': 'm as in mom', 'example_words': 'mom, come, ham, summer, thumb'},
        {'cmu_code': 'N', 'ipa': '/n/', 'description': 'n as in no', 'example_words': 'no, sun, funny, knee, pneumonia'},
        {'cmu_code': 'NG', 'ipa': '/ŋ/', 'description': 'ng as in sing', 'example_words': 'sing, thing, bank, finger, ankle'},
        {'cmu_code': 'P', 'ipa': '/p/', 'description': 'p as in pet', 'example_words': 'pet, up, happy, apple, stop'},
        {'cmu_code': 'R', 'ipa': '/r/', 'description': 'r as in run', 'example_words': 'run, car, berry, write, rhyme'},
        {'cmu_code': 'S', 'ipa': '/s/', 'description': 's as in sun', 'example_words': 'sun, miss, city, science, psychology'},
        {'cmu_code': 'SH', 'ipa': '/ʃ/', 'description': 'sh as in ship', 'example_words': 'ship, wish, nation, sugar, ocean'},
        {'cmu_code': 'T', 'ipa': '/t/', 'description': 't as in top', 'example_words': 'top, cat, better, walked, pterodactyl'},
        {'cmu_code': 'TH', 'ipa': '/θ/', 'description': 'th as in thin', 'example_words': 'thin, bath, think, teeth, both'},
        {'cmu_code': 'V', 'ipa': '/v/', 'description': 'v as in van', 'example_words': 'van, love, give, of, five'},
        {'cmu_code': 'W', 'ipa': '/w/', 'description': 'w as in win', 'example_words': 'win, away, quick, one, choir'},
        {'cmu_code': 'Y', 'ipa': '/j/', 'description': 'y as in yes', 'example_words': 'yes, use, new, onion, hallelujah'},
        {'cmu_code': 'Z', 'ipa': '/z/', 'description': 'z as in zoo', 'example_words': 'zoo, buzz, is, cheese, xylophone'},
        {'cmu_code': 'ZH', 'ipa': '/ʒ/', 'description': 'zh as in vision', 'example_words': 'vision, measure, garage, beige, azure'},
        
        # Additional notations
        {'cmu_code': '0', 'ipa': '', 'description': 'no stress', 'example_words': 'unstressed syllable'},
        {'cmu_code': '1', 'ipa': 'ˈ', 'description': 'primary stress', 'example_words': 'stressed syllable'},
        {'cmu_code': '2', 'ipa': 'ˌ', 'description': 'secondary stress', 'example_words': 'secondary stressed syllable'}
    ]
    
    # Create DataFrame and save
    df = pd.DataFrame(phoneme_mappings)
    df.to_csv('/Users/<USER>/word-generator/cmu_phoneme_reference.csv', index=False)
    print(f"Created cmu_phoneme_reference.csv with {len(phoneme_mappings)} phoneme mappings")
    
    return df

def main():
    """Generate all CSV files"""
    print("Creating Phonics Rules CSV Files...")
    print("=" * 60)
    
    # Create master rules
    print("\n1. Creating Master Rules CSV...")
    master_df = create_master_rules_csv()
    
    # Create decodability constraints
    print("\n2. Creating Decodability Constraints CSV...")
    constraints_df = create_decodability_constraints_csv()
    
    # Create phoneme mapping
    print("\n3. Creating Phoneme Mapping Reference CSV...")
    phoneme_df = create_phoneme_mapping_csv()
    
    print("\n" + "=" * 60)
    print("All CSV files created successfully!")
    print("\nFiles created:")
    print("- phonics_master_rules.csv")
    print("- decodability_constraints.csv")
    print("- cmu_phoneme_reference.csv")

if __name__ == "__main__":
    main()