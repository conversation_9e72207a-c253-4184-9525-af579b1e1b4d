#!/usr/bin/env python3
"""
CSV Configuration Translator for Orton-Gillingham Word Generator
Translates the position-aware CSV format into generator-compatible data
"""

import pandas as pd
import re
from typing import Dict, List, Tuple, Any

class SkillConfigTranslator:
    def __init__(self, csv_path='og_skill_pattern_configurations_with_position.csv'):
        """Initialize with the path to your CSV configuration"""
        self.csv_path = csv_path
        self.df = pd.read_csv(csv_path)
        
    def parse_grapheme_patterns(self, pattern_str: str) -> Dict[str, List[str]]:
        """Parse the grapheme_patterns column into structured data"""
        patterns = {}
        
        # Handle different pattern formats
        if ':' in pattern_str:
            # Format: "single vowel: a" or "specific words: be, he, me"
            prefix, content = pattern_str.split(':', 1)
            prefix = prefix.strip()
            content = content.strip()
            
            if prefix == 'single vowel':
                patterns['vowel'] = content
            elif prefix == 'specific words':
                patterns['specific_words'] = [w.strip() for w in content.split(',')]
            elif prefix == 'consonant+le':
                patterns['c_le'] = True
            else:
                # Generic handling
                patterns['patterns'] = [p.strip() for p in content.split(',')]
        
        elif '→' in pattern_str:
            # Format: "f/fe → ves"
            patterns['transformation'] = pattern_str
        
        elif '+' in pattern_str and 'word+word' not in pattern_str:
            # Format: "c+a, c+o, c+u"
            patterns['combinations'] = [p.strip() for p in pattern_str.split(',')]
        
        elif pattern_str == 'word+word':
            patterns['compound'] = True
        
        elif pattern_str == 'VCe':
            patterns['magic_e'] = True
        
        elif pattern_str == 'apostrophe replaces letters':
            patterns['contractions'] = True
            
        elif pattern_str == 'various':
            patterns['various'] = True
            
        else:
            # Standard comma-separated patterns
            pattern_list = [p.strip() for p in pattern_str.split(',')]
            
            # Categorize patterns
            initial_blends = ['bl', 'cl', 'fl', 'gl', 'pl', 'sl', 
                            'br', 'cr', 'dr', 'fr', 'gr', 'pr', 'tr',
                            'sc', 'sk', 'sm', 'sn', 'sp', 'st', 'sw',
                            'scr', 'spr', 'str', 'spl', 'squ', 'thr']
            
            final_blends = ['nt', 'nd', 'nk', 'lt', 'ld', 'lf', 'lk', 
                          'lm', 'lp', 'mp', 'ng', 'sk', 'st', 'ft']
            
            digraphs = ['ch', 'sh', 'th', 'wh', 'ck', 'tch', 'dge']
            
            vowel_teams = ['ai', 'ay', 'ea', 'ee', 'ei', 'ey', 'ie', 
                         'oa', 'oe', 'oi', 'oo', 'ou', 'ow', 'oy',
                         'ue', 'ew', 'eu', 'au', 'aw', 'eigh', 'igh', 'augh']
            
            r_controlled = ['ar', 'er', 'ir', 'or', 'ur', 'ore', 'war', 'wor']
            
            for pattern in pattern_list:
                if pattern in initial_blends:
                    patterns.setdefault('initial_blends', []).append(pattern)
                elif pattern in final_blends:
                    patterns.setdefault('final_blends', []).append(pattern)
                elif pattern in digraphs:
                    patterns.setdefault('digraphs', []).append(pattern)
                elif pattern in vowel_teams:
                    patterns.setdefault('vowel_teams', []).append(pattern)
                elif pattern in r_controlled:
                    patterns.setdefault('r_controlled', []).append(pattern)
                elif pattern in ['ff', 'll', 'ss', 'zz']:
                    patterns.setdefault('floss', []).append(pattern)
                elif pattern.startswith('-') or pattern.endswith('-'):
                    patterns.setdefault('affixes', []).append(pattern)
                else:
                    patterns.setdefault('patterns', []).append(pattern)
        
        return patterns
    
    def parse_word_length(self, length_str: str) -> Dict[str, int]:
        """Parse word length into min/max dictionary"""
        if '-' in str(length_str):
            parts = str(length_str).split('-')
            return {'min': int(parts[0]), 'max': int(parts[1])}
        else:
            length = int(length_str)
            return {'min': length, 'max': length}
    
    def parse_phoneme_requirement(self, phoneme_str: str) -> Tuple[str, str]:
        """Parse phoneme requirement into code and description"""
        if pd.isna(phoneme_str):
            return None, None
            
        # Handle "CODE (description)" format
        if '(' in phoneme_str and ')' in phoneme_str:
            match = re.match(r'([A-Z\s]+)\s*\((.*?)\)', phoneme_str)
            if match:
                return match.group(1).strip(), match.group(2).strip()
        
        # Handle "CODE or CODE" format
        if ' or ' in phoneme_str:
            codes = phoneme_str.split(' or ')
            return codes, "multiple options"
        
        return phoneme_str.strip(), ""
    
    def parse_position_constraints(self, additional_constraints: str) -> Dict[str, str]:
        """Parse position information from additional_constraints"""
        position_map = {}
        
        if pd.isna(additional_constraints):
            return position_map
        
        # Parse patterns like "ai/ei=medial, ay=final"
        if '=' in additional_constraints:
            parts = additional_constraints.split(',')
            for part in parts:
                if '=' in part:
                    patterns, position = part.strip().split('=')
                    for pattern in patterns.split('/'):
                        position_map[pattern.strip()] = position.strip()
        
        return position_map
    
    def get_skill_config(self, skill_id: str) -> Dict[str, Any]:
        """Get complete configuration for a skill"""
        row = self.df[self.df['skill_id'] == skill_id]
        
        if row.empty:
            return None
        
        row = row.iloc[0]
        
        # Parse all components
        patterns = self.parse_grapheme_patterns(row['grapheme_patterns'])
        word_length = self.parse_word_length(row['word_length'])
        phoneme_code, phoneme_desc = self.parse_phoneme_requirement(row['phoneme_requirement'])
        position_by_pattern = self.parse_position_constraints(row['additional_constraints'])
        
        config = {
            'skill_id': row['skill_id'],
            'name': row['skill_name'],
            'patterns': patterns,
            'position': row['position'],
            'position_by_pattern': position_by_pattern,
            'word_length': word_length,
            'pattern_type': row['pattern_type'],
            'phoneme_code': phoneme_code,
            'phoneme_description': phoneme_desc,
            'additional_constraints': row['additional_constraints']
        }
        
        return config
    
    def get_pattern_position(self, skill_id: str, pattern: str) -> str:
        """Get the position requirement for a specific pattern in a skill"""
        config = self.get_skill_config(skill_id)
        
        if not config:
            return 'any'
        
        # Check pattern-specific positions first
        if config['position_by_pattern'] and pattern in config['position_by_pattern']:
            return config['position_by_pattern'][pattern]
        
        # Return general position for skill
        return config['position']
    
    def generate_skill_mappings(self) -> Dict[str, Dict]:
        """Generate skill mappings compatible with the word generator"""
        mappings = {}
        
        for _, row in self.df.iterrows():
            skill_id = row['skill_id']
            patterns = self.parse_grapheme_patterns(row['grapheme_patterns'])
            phoneme_code, _ = self.parse_phoneme_requirement(row['phoneme_requirement'])
            
            mapping = {
                'type': row['pattern_type'],
                'name': row['skill_name'],
                'position': row['position']
            }
            
            # Add pattern-specific fields based on type
            if row['pattern_type'] == 'CVC':
                mapping['vowel'] = phoneme_code
            elif row['pattern_type'] in ['initial_blend', 'vowel_team', 'r_controlled']:
                if any(key in patterns for key in ['initial_blends', 'vowel_teams', 'r_controlled', 'patterns']):
                    pattern_key = next(key for key in ['initial_blends', 'vowel_teams', 'r_controlled', 'patterns'] if key in patterns)
                    mapping['patterns'] = patterns[pattern_key]
            elif row['pattern_type'] in ['digraph', 'final_blend']:
                if 'digraphs' in patterns:
                    mapping['pattern'] = patterns['digraphs'][0] if len(patterns['digraphs']) == 1 else patterns['digraphs']
                elif 'final_blends' in patterns:
                    mapping['pattern'] = patterns['final_blends'][0]
                elif 'patterns' in patterns:
                    mapping['pattern'] = patterns['patterns'][0]
            
            mappings[skill_id] = mapping
        
        return mappings
    
    def validate_word_with_position(self, word: str, skill_id: str) -> Tuple[bool, str]:
        """Validate if a word meets position requirements for a skill"""
        config = self.get_skill_config(skill_id)
        
        if not config:
            return False, "Skill not found"
        
        # Get all patterns for this skill
        all_patterns = []
        for pattern_list in config['patterns'].values():
            if isinstance(pattern_list, list):
                all_patterns.extend(pattern_list)
            elif isinstance(pattern_list, str):
                all_patterns.append(pattern_list)
        
        # Check each pattern
        pattern_found = False
        for pattern in all_patterns:
            if pattern in word.lower():
                # Get position requirement for this pattern
                required_position = self.get_pattern_position(skill_id, pattern)
                
                # Check if pattern is in correct position
                from position_aware_skill_config import check_pattern_position
                if check_pattern_position(word, pattern, required_position):
                    pattern_found = True
                else:
                    return False, f"Pattern '{pattern}' not in required position '{required_position}'"
        
        if not pattern_found and all_patterns:
            return False, "No required patterns found in word"
        
        return True, "Valid"

# Demo usage
if __name__ == "__main__":
    print("CSV CONFIGURATION TRANSLATOR")
    print("=" * 60)
    
    translator = SkillConfigTranslator()
    
    # Test with a few skills
    test_skills = ['5.3', '18.1', '4.5', '18.5']
    
    for skill_id in test_skills:
        print(f"\nSkill {skill_id}:")
        config = translator.get_skill_config(skill_id)
        
        if config:
            print(f"  Name: {config['name']}")
            print(f"  Patterns: {config['patterns']}")
            print(f"  Position: {config['position']}")
            if config['position_by_pattern']:
                print(f"  Pattern-specific positions: {config['position_by_pattern']}")
            print(f"  Word length: {config['word_length']['min']}-{config['word_length']['max']}")
    
    # Test word validation
    print("\n\nWORD VALIDATION TESTS:")
    print("-" * 40)
    
    test_cases = [
        ("stop", "5.3"),     # st at beginning
        ("rain", "18.1"),    # ai in middle
        ("day", "18.1"),     # ay at end
        ("pie", "18.5"),     # ie at end
        ("light", "18.5"),   # igh in middle
    ]
    
    for word, skill_id in test_cases:
        valid, reason = translator.validate_word_with_position(word, skill_id)
        status = "✓" if valid else "✗"
        print(f"{status} '{word}' for skill {skill_id}: {reason}")
    
    # Generate skill mappings
    print("\n\nGENERATING SKILL MAPPINGS...")
    mappings = translator.generate_skill_mappings()
    print(f"Generated mappings for {len(mappings)} skills")
