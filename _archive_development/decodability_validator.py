#!/usr/bin/env python3
"""
Decodability Validator for Orton-Gillingham Word Lists
Ensures words are truly decodable at each skill level by checking ALL patterns
"""

import pandas as pd
import os
import re
from collections import defaultdict

class DecodabilityValidator:
    def __init__(self):
        self.word_lists_dir = '/Users/<USER>/word-generator/word_lists'
        self.heart_words_path = '/Users/<USER>/word-generator/heart_words/heart_words_reference_by_skill.csv'
        
        # Load heart words by skill level
        self.heart_words_by_skill = self._load_heart_words()
        
        # Define what patterns are taught at each skill level
        self.patterns_by_skill = self._define_skill_patterns()
        
        # Define prerequisite skills
        self.skill_prerequisites = self._define_prerequisites()
        
    def _load_heart_words(self):
        """Load heart words organized by when they're introduced"""
        heart_words_by_skill = defaultdict(set)
        
        df = pd.read_csv(self.heart_words_path)
        for _, row in df.iterrows():
            word = row['word'].lower()
            skill = row['skill_introduction']
            heart_words_by_skill[skill].add(word)
        
        return heart_words_by_skill
    
    def _define_skill_patterns(self):
        """Define what phonics patterns are taught at each skill level"""
        patterns = {
            # CVC Skills - single vowels only
            '1.1': {'vowels': ['a'], 'patterns': ['CVC with short a']},
            '1.2': {'vowels': ['e'], 'patterns': ['CVC with short e']},
            '1.3': {'vowels': ['i'], 'patterns': ['CVC with short i']},
            '1.4': {'vowels': ['o'], 'patterns': ['CVC with short o']},
            '1.5': {'vowels': ['u'], 'patterns': ['CVC with short u']},
            
            # CV Open syllables
            '2.1': {'patterns': ['CV with long e (be, he, me, we)']},
            '2.2': {'patterns': ['CV with long o (go, no, so)']},
            '2.3': {'patterns': ['CV with long i (I, hi)']},
            
            # C or K rule
            '3.1': {'patterns': ['c before a, o, u']},
            '3.2': {'patterns': ['k before e, i, y']},
            
            # Digraphs
            '4.1': {'patterns': ['ch'], 'sounds': ['/ch/']},
            '4.2': {'patterns': ['sh'], 'sounds': ['/sh/']},
            '4.3': {'patterns': ['th'], 'sounds': ['/th/', '/ð/']},
            '4.4': {'patterns': ['wh'], 'sounds': ['/w/', '/hw/']},
            '4.5': {'patterns': ['ck'], 'sounds': ['/k/']},
            
            # Initial blends
            '5.1': {'patterns': ['bl', 'cl', 'fl', 'gl', 'pl', 'sl']},
            '5.2': {'patterns': ['br', 'cr', 'dr', 'fr', 'gr', 'pr', 'tr']},
            '5.3': {'patterns': ['sc', 'sk', 'sm', 'sn', 'sp', 'st', 'sw']},
            '5.4': {'patterns': ['scr', 'spr', 'str', 'spl', 'squ', 'thr']},
            
            # Final blends
            '6.1': {'patterns': ['nt']}, '6.2': {'patterns': ['nd']},
            '6.3': {'patterns': ['nk']}, '6.4': {'patterns': ['lt']},
            '6.5': {'patterns': ['ld']}, '6.6': {'patterns': ['lf']},
            '6.7': {'patterns': ['lk']}, '6.8': {'patterns': ['lm']},
            '6.9': {'patterns': ['lp']}, '6.10': {'patterns': ['mp']},
            '6.11': {'patterns': ['nd']}, '6.12': {'patterns': ['ng']},
            '6.13': {'patterns': ['sk']}, '6.14': {'patterns': ['st']},
            '6.15': {'patterns': ['ft']},
            
            # Special rules
            '7.0': {'patterns': ['ff', 'll', 'ss', 'zz'], 'rule': 'after short vowel'},
            '8.0': {'patterns': ['compound words']},
            '9.0': {'patterns': ['tch'], 'rule': 'after short vowel'},
            '10.0': {'patterns': ['dge'], 'rule': 'after short vowel'},
            
            # Magic E
            '11.0': {'patterns': ['VCe'], 'rule': 'makes vowel long'},
            
            # Closed syllable exceptions
            '12.1': {'patterns': ['ind', 'ild', 'old', 'ost', 'olt']},
            
            # Soft sounds
            '13.1': {'patterns': ['ge', 'gi', 'gy', 'dge']},
            '13.2': {'patterns': ['ce', 'ci', 'cy']},
            
            # Y as vowel
            '14.1': {'patterns': ['y as /i/', 'y as /ī/', 'y as /ē/']},
            
            # Schwa
            '15.1': {'patterns': ['o as schwa']},
            '15.2': {'patterns': ['a as schwa']},
            '15.3': {'patterns': ['e as schwa']},
            
            # Silent letters
            '16.0': {'patterns': ['kn', 'wr', 'mb', 'gh', 'gn', 'ps', 'rh']},
            
            # 3 sounds of ED
            '17.0': {'patterns': ['ed as /t/', 'ed as /d/', 'ed as /id/']},
            
            # Vowel teams
            '18.1': {'patterns': ['ai', 'ay', 'ei', 'eigh'], 'sound': 'long a'},
            '18.2': {'patterns': ['ea', 'ee', 'ie', 'ey'], 'sound': 'long e'},
            '18.3': {'patterns': ['oa', 'oe', 'ow'], 'sound': 'long o'},
            '18.4': {'patterns': ['ou', 'ow'], 'sound': '/ow/'},
            '18.5': {'patterns': ['ie', 'igh', 'y', 'i_e'], 'sound': 'long i'},
            '18.6': {'patterns': ['ue', 'ew', 'eu'], 'sound': 'long u'},
            '18.7': {'patterns': ['oo'], 'sound': 'long /oo/'},
            '18.8': {'patterns': ['oo'], 'sound': 'short /oo/'},
            '18.9': {'patterns': ['ou'], 'sound': 'short u'},
            '18.10': {'patterns': ['au', 'aw', 'augh'], 'sound': '/aw/'},
            '18.11': {'patterns': ['oi', 'oy'], 'sound': '/oy/'},
            '18.12': {'patterns': ['ea'], 'sound': 'short e'},
            
            # R-controlled
            '20.1': {'patterns': ['er', 'ir', 'ur'], 'sound': '/er/'},
            '20.2': {'patterns': ['or', 'ore'], 'sound': '/or/'},
            '20.3': {'patterns': ['ar'], 'sound': '/ar/'},
            '20.4': {'patterns': ['war'], 'sound': '/wor/'},
            '20.5': {'patterns': ['wor'], 'sound': '/wer/'},
        }
        
        return patterns
    
    def _define_prerequisites(self):
        """Define prerequisite skills for each skill"""
        prereqs = {
            '1.1': [], '1.2': [], '1.3': [], '1.4': [], '1.5': [],
            '2.1': ['1.1', '1.2', '1.3', '1.4', '1.5'],
            '2.2': ['1.1', '1.2', '1.3', '1.4', '1.5'],
            '2.3': ['1.1', '1.2', '1.3', '1.4', '1.5'],
            '3.1': ['1.1', '1.2', '1.3', '1.4', '1.5'],
            '3.2': ['1.1', '1.2', '1.3', '1.4', '1.5'],
            '4.1': ['1.1', '1.2', '1.3', '1.4', '1.5'],
            '4.2': ['1.1', '1.2', '1.3', '1.4', '1.5'],
            '4.3': ['1.1', '1.2', '1.3', '1.4', '1.5'],
            '4.4': ['1.1', '1.2', '1.3', '1.4', '1.5'],
            '4.5': ['1.1', '1.2', '1.3', '1.4', '1.5'],
            '5.1': ['1.1', '1.2', '1.3', '1.4', '1.5', '4.1', '4.2', '4.3', '4.4', '4.5'],
            '5.2': ['1.1', '1.2', '1.3', '1.4', '1.5', '4.1', '4.2', '4.3', '4.4', '4.5'],
            '5.3': ['1.1', '1.2', '1.3', '1.4', '1.5', '4.1', '4.2', '4.3', '4.4', '4.5'],
            '5.4': ['1.1', '1.2', '1.3', '1.4', '1.5', '4.1', '4.2', '4.3', '4.4', '4.5'],
            # Continue for all skills...
        }
        
        # For now, simplified - each skill requires all previous skills
        all_skills = sorted([f for f in os.listdir(self.word_lists_dir) 
                           if f.startswith('skill_') and f.endswith('.csv')])
        
        skill_order = []
        for f in all_skills:
            skill_id = f.split('_')[1].replace('.csv', '')
            skill_order.append(skill_id)
        
        # Build prerequisites based on order
        for i, skill in enumerate(skill_order):
            if skill not in prereqs:
                prereqs[skill] = skill_order[:i]
        
        return prereqs
    
    def _identify_patterns_in_word(self, word):
        """Identify all phonics patterns in a word"""
        patterns = []
        
        # Check for vowel teams (must check before single vowels)
        vowel_teams = ['ai', 'ay', 'ea', 'ee', 'ei', 'ey', 'ie', 'oa', 'oe', 
                      'oi', 'oo', 'ou', 'ow', 'oy', 'ue', 'ui', 'au', 'aw',
                      'ew', 'eigh', 'igh', 'augh', 'ough']
        
        for vt in vowel_teams:
            if vt in word:
                patterns.append(('vowel_team', vt))
        
        # Check for digraphs
        digraphs = ['ch', 'sh', 'th', 'wh', 'ck', 'ph', 'gh', 'ng']
        for dg in digraphs:
            if dg in word:
                patterns.append(('digraph', dg))
        
        # Check for blends
        initial_blends = ['bl', 'cl', 'fl', 'gl', 'pl', 'sl', 'br', 'cr', 
                         'dr', 'fr', 'gr', 'pr', 'tr', 'sc', 'sk', 'sm', 
                         'sn', 'sp', 'st', 'sw', 'scr', 'spr', 'str', 'spl']
        
        for blend in initial_blends:
            if word.startswith(blend):
                patterns.append(('initial_blend', blend))
        
        # Check for r-controlled
        r_controlled = ['ar', 'er', 'ir', 'or', 'ur', 'war', 'wor']
        for rc in r_controlled:
            if rc in word:
                patterns.append(('r_controlled', rc))
        
        # Check for VCe pattern
        if len(word) >= 4 and word[-1] == 'e' and word[-3] in 'aeiou':
            if word[-2] not in 'aeiou':
                patterns.append(('magic_e', 'VCe'))
        
        # Check for special patterns
        if 'll' in word or 'ff' in word or 'ss' in word or 'zz' in word:
            patterns.append(('floss', word))
        
        if 'tch' in word:
            patterns.append(('catch', 'tch'))
            
        if 'dge' in word:
            patterns.append(('bridge', 'dge'))
        
        return patterns
    
    def is_word_decodable(self, word, current_skill, mastered_skills, heart_words):
        """Check if a word is fully decodable at the current skill level"""
        word_lower = word.lower()
        
        # If it's a heart word at this level or earlier, it's "decodable"
        if word_lower in heart_words:
            return True, "heart word"
        
        # Identify all patterns in the word
        patterns = self._identify_patterns_in_word(word_lower)
        
        # Check each pattern
        undecodable_patterns = []
        
        for pattern_type, pattern in patterns:
            pattern_found = False
            
            # Check if pattern is in current skill
            if current_skill in self.patterns_by_skill:
                skill_patterns = self.patterns_by_skill[current_skill].get('patterns', [])
                if any(pattern in p for p in skill_patterns):
                    pattern_found = True
            
            # Check if pattern is in mastered skills
            if not pattern_found:
                for skill in mastered_skills:
                    if skill in self.patterns_by_skill:
                        skill_patterns = self.patterns_by_skill[skill].get('patterns', [])
                        if any(pattern in p for p in skill_patterns):
                            pattern_found = True
                            break
            
            if not pattern_found:
                undecodable_patterns.append(f"{pattern_type}:{pattern}")
        
        # Additional check for vowel teams in supposedly simple words
        if any(vt in word_lower for vt in ['oo', 'ea', 'ay', 'ow', 'igh', 'ew']):
            # These require vowel team skills (18.x)
            if not any(skill.startswith('18') for skill in mastered_skills + [current_skill]):
                undecodable_patterns.append("vowel_team:not_yet_taught")
        
        if undecodable_patterns:
            return False, f"Undecodable patterns: {', '.join(undecodable_patterns)}"
        
        return True, "decodable"
    
    def validate_all_lists(self):
        """Validate all word lists for decodability"""
        results = []
        
        # Get all CSV files
        csv_files = [f for f in os.listdir(self.word_lists_dir) 
                    if f.startswith('skill_') and f.endswith('.csv')]
        
        for csv_file in sorted(csv_files):
            skill_id = csv_file.split('_')[1]
            
            # Get mastered skills (prerequisites)
            mastered_skills = self.skill_prerequisites.get(skill_id, [])
            
            # Get heart words available at this level
            available_heart_words = set()
            for skill in mastered_skills + [skill_id]:
                available_heart_words.update(self.heart_words_by_skill.get(skill, set()))
            
            # Read word list
            df = pd.read_csv(os.path.join(self.word_lists_dir, csv_file))
            
            violations = []
            
            for _, row in df.iterrows():
                word = row['word']
                is_decodable, reason = self.is_word_decodable(
                    word, skill_id, mastered_skills, available_heart_words
                )
                
                if not is_decodable and not row.get('is_heart', False):
                    violations.append({
                        'word': word,
                        'reason': reason,
                        'is_HF': row.get('is_HF', False)
                    })
            
            if violations:
                results.append({
                    'skill_id': skill_id,
                    'skill_file': csv_file,
                    'total_words': len(df),
                    'violations': violations,
                    'violation_count': len(violations),
                    'violation_rate': len(violations) / len(df) * 100
                })
        
        return results
    
    def generate_report(self, results):
        """Generate a detailed decodability report"""
        report_path = '/Users/<USER>/word-generator/decodability_report.txt'
        
        with open(report_path, 'w') as f:
            f.write("DECODABILITY VALIDATION REPORT\n")
            f.write("=" * 80 + "\n")
            f.write("Words that contain patterns not yet taught at their skill level\n\n")
            
            total_violations = sum(r['violation_count'] for r in results)
            f.write(f"Total violations found: {total_violations}\n")
            f.write(f"Skills with violations: {len(results)}\n\n")
            
            # Most problematic skills
            results_sorted = sorted(results, key=lambda x: x['violation_count'], reverse=True)
            
            f.write("TOP 10 MOST PROBLEMATIC SKILLS:\n")
            f.write("-" * 80 + "\n")
            
            for result in results_sorted[:10]:
                f.write(f"\nSkill {result['skill_id']} ({result['skill_file']}):\n")
                f.write(f"  Violations: {result['violation_count']} / {result['total_words']} ")
                f.write(f"({result['violation_rate']:.1f}%)\n")
                
                # Show first 5 violations
                f.write("  Examples:\n")
                for v in result['violations'][:5]:
                    f.write(f"    - {v['word']}: {v['reason']}\n")
                
                if len(result['violations']) > 5:
                    f.write(f"    ... and {len(result['violations']) - 5} more\n")
            
            # Detailed section
            f.write("\n\nDETAILED VIOLATIONS BY SKILL:\n")
            f.write("=" * 80 + "\n")
            
            for result in results_sorted:
                if result['violations']:
                    f.write(f"\n{result['skill_file']}:\n")
                    for v in result['violations']:
                        hf_marker = " [HF]" if v['is_HF'] else ""
                        f.write(f"  {v['word']}{hf_marker}: {v['reason']}\n")
        
        # Also create a CSV for easier analysis
        csv_path = '/Users/<USER>/word-generator/decodability_violations.csv'
        
        all_violations = []
        for result in results:
            for v in result['violations']:
                all_violations.append({
                    'skill_id': result['skill_id'],
                    'word': v['word'],
                    'reason': v['reason'],
                    'is_HF': v['is_HF']
                })
        
        violations_df = pd.DataFrame(all_violations)
        violations_df.to_csv(csv_path, index=False)
        
        print(f"\nReports generated:")
        print(f"  - {report_path}")
        print(f"  - {csv_path}")
        
        return total_violations

def main():
    print("DECODABILITY VALIDATOR")
    print("=" * 80)
    print("Checking if words contain only patterns that have been taught...\n")
    
    validator = DecodabilityValidator()
    results = validator.validate_all_lists()
    
    total_violations = validator.generate_report(results)
    
    print(f"\nValidation complete!")
    print(f"Found {total_violations} words that contain patterns not yet taught.")
    print("\nCheck decodability_report.txt for details.")

if __name__ == "__main__":
    main()
