#!/usr/bin/env python3
"""
Enhanced <PERSON>ton-<PERSON>ingham Word List Generator with Decodability Checking

This generator ensures that EVERY phonics element in a word is decodable
at the skill level where it's introduced. No more "stood" at skill 5.3!
"""

import nltk
import pandas as pd
import re
import os
import json
from collections import defaultdict
from datetime import datetime

class DecodableWordGenerator:
    def __init__(self):
        print("Initializing Decodable Word Generator...")
        
        # Load CMU Pronouncing Dictionary
        self.cmu_dict = nltk.corpus.cmudict.dict()
        
        # Load word frequency data
        self.freq_dist = self._load_frequency_data()
        
        # Define high-frequency words
        self.high_freq_words = self._get_high_frequency_words()
        
        # Load inappropriate word filters
        self.inappropriate_words = self._load_inappropriate_words()
        
        # Define skill sequence and prerequisites
        self.skill_sequence = self._define_skill_sequence()
        self.patterns_taught_by_skill = self._build_patterns_by_skill()
        
        # Load heart words
        self.heart_words_by_skill = self._load_heart_words()
        
        # Define word length constraints
        self.word_length_limits = self._define_length_limits()
        
        # Define skill mappings
        self.skill_mappings = self._load_skill_mappings()
        
        print("Initialization complete!")
    
    def _load_frequency_data(self):
        """Load word frequency from Brown corpus"""
        brown_words = nltk.corpus.brown.words()
        return nltk.FreqDist(word.lower() for word in brown_words)
    
    def _get_high_frequency_words(self, top_n=500):
        """Get the most frequent words"""
        return set([word for word, _ in self.freq_dist.most_common(top_n)])
    
    def _load_inappropriate_words(self):
        """Load list of inappropriate words to exclude"""
        return {
            'damn', 'hell', 'crap', 'piss', 'bastard', 'ass',
            'butt', 'boob', 'poop', 'fart', 'pee',
            'beer', 'wine', 'drug', 'weed', 'drunk', 'booze',
            'kill', 'stab', 'gun', 'bomb', 'dead', 'die', 'murder',
            'sexy', 'stupid', 'dumb', 'idiot', 'hate', 'suck'
        }
    
    def _load_heart_words(self):
        """Load heart words by skill introduction"""
        heart_words = defaultdict(set)
        heart_words_path = '/Users/<USER>/word-generator/heart_words/heart_words_reference_by_skill.csv'
        
        if os.path.exists(heart_words_path):
            df = pd.read_csv(heart_words_path)
            for _, row in df.iterrows():
                word = row['word'].lower()
                skill = str(row['skill_introduction'])
                heart_words[skill].add(word)
        
        return heart_words
    
    def _define_skill_sequence(self):
        """Define the exact order skills are taught"""
        return [
            # Level 1: CVC
            '1.1', '1.2', '1.3', '1.4', '1.5',
            # Level 2: CV Open syllables  
            '2.1', '2.2', '2.3',
            # Level 3: C/K rule
            '3.1', '3.2',
            # Level 4: Digraphs
            '4.1', '4.2', '4.3', '4.4', '4.5',
            # Level 5: Initial blends
            '5.1', '5.2', '5.3', '5.4',
            # Level 6: Final blends
            '6.1', '6.2', '6.3', '6.4', '6.5', '6.6', '6.7', '6.8', 
            '6.9', '6.10', '6.11', '6.12', '6.13', '6.14', '6.15',
            # Level 7-10: Special rules
            '7.0', '8.0', '9.0', '10.0',
            # Level 11: Magic E
            '11.0',
            # Level 12: Closed syllable exceptions
            '12.1',
            # Level 13: Soft sounds
            '13.1', '13.2',
            # Level 14: Y as vowel
            '14.1',
            # Level 15: Schwa
            '15.1', '15.2', '15.3',
            # Level 16: Silent letters
            '16.0',
            # Level 17: 3 sounds of ED
            '17.0',
            # Level 18: Vowel teams
            '18.1', '18.2', '18.3', '18.4', '18.5', '18.6', 
            '18.7', '18.8', '18.9', '18.10', '18.11', '18.12',
            # Level 19: Homophones
            '19.0',
            # Level 20: R-controlled
            '20.1', '20.2', '20.3', '20.4', '20.5',
            # Level 21+: Advanced patterns
            '21.1', '21.2', '21.5',
            '22.0', '23.1', '23.2', '24.0', '25.0', '26.0',
            '27.1', '27.2', '27.3', '27.5', '28.0', '29.0', '30.0'
        ]
    
    def _build_patterns_by_skill(self):
        """Build a comprehensive map of what patterns are decodable at each skill level"""
        patterns = {}
        
        # Skills 1.x: Only CVC with short vowels
        patterns['1.1'] = {
            'short_vowels': ['a'],
            'consonants': ['b', 'c', 'd', 'f', 'g', 'h', 'j', 'k', 'l', 'm', 'n', 'p', 'r', 's', 't', 'v', 'w', 'x', 'z'],
            'pattern': 'CVC only'
        }
        patterns['1.2'] = {
            'short_vowels': ['a', 'e'],
            'consonants': ['b', 'c', 'd', 'f', 'g', 'h', 'j', 'k', 'l', 'm', 'n', 'p', 'r', 's', 't', 'v', 'w', 'x', 'z'],
            'pattern': 'CVC only'
        }
        patterns['1.3'] = {
            'short_vowels': ['a', 'e', 'i'],
            'consonants': ['b', 'c', 'd', 'f', 'g', 'h', 'j', 'k', 'l', 'm', 'n', 'p', 'r', 's', 't', 'v', 'w', 'x', 'z'],
            'pattern': 'CVC only'
        }
        patterns['1.4'] = {
            'short_vowels': ['a', 'e', 'i', 'o'],
            'consonants': ['b', 'c', 'd', 'f', 'g', 'h', 'j', 'k', 'l', 'm', 'n', 'p', 'r', 's', 't', 'v', 'w', 'x', 'z'],
            'pattern': 'CVC only'
        }
        patterns['1.5'] = {
            'short_vowels': ['a', 'e', 'i', 'o', 'u'],
            'consonants': ['b', 'c', 'd', 'f', 'g', 'h', 'j', 'k', 'l', 'm', 'n', 'p', 'r', 's', 't', 'v', 'w', 'x', 'z'],
            'pattern': 'CVC only'
        }
        
        # Skills 2.x: Add CV open syllables (specific words only)
        patterns['2.1'] = {'specific_words': ['be', 'he', 'me', 'we', 'she']}
        patterns['2.2'] = {'specific_words': ['go', 'no', 'so']}
        patterns['2.3'] = {'specific_words': ['I', 'hi']}
        
        # Skills 4.x: Add digraphs
        patterns['4.1'] = {'digraphs': ['ch']}
        patterns['4.2'] = {'digraphs': ['sh']}
        patterns['4.3'] = {'digraphs': ['th']}
        patterns['4.4'] = {'digraphs': ['wh']}
        patterns['4.5'] = {'digraphs': ['ck']}
        
        # Skills 5.x: Add initial blends
        patterns['5.1'] = {'initial_blends': ['bl', 'cl', 'fl', 'gl', 'pl', 'sl']}
        patterns['5.2'] = {'initial_blends': ['br', 'cr', 'dr', 'fr', 'gr', 'pr', 'tr']}
        patterns['5.3'] = {'initial_blends': ['sc', 'sk', 'sm', 'sn', 'sp', 'st', 'sw']}
        patterns['5.4'] = {'initial_blends': ['scr', 'spr', 'str', 'spl', 'squ', 'thr']}
        
        # Skills 6.x: Add final blends
        patterns['6.1'] = {'final_blends': ['nt']}
        patterns['6.2'] = {'final_blends': ['nd']}
        patterns['6.3'] = {'final_blends': ['nk']}
        patterns['6.4'] = {'final_blends': ['lt']}
        patterns['6.5'] = {'final_blends': ['ld']}
        patterns['6.6'] = {'final_blends': ['lf']}
        patterns['6.7'] = {'final_blends': ['lk']}
        patterns['6.8'] = {'final_blends': ['lm']}
        patterns['6.9'] = {'final_blends': ['lp']}
        patterns['6.10'] = {'final_blends': ['mp']}
        patterns['6.12'] = {'final_blends': ['ng']}
        patterns['6.13'] = {'final_blends': ['sk']}
        patterns['6.14'] = {'final_blends': ['st']}
        patterns['6.15'] = {'final_blends': ['ft']}
        
        # Skills 7.0: FLOSS rule
        patterns['7.0'] = {'floss': ['ff', 'll', 'ss', 'zz']}
        
        # Skills 9.0 & 10.0: tch and dge
        patterns['9.0'] = {'patterns': ['tch']}
        patterns['10.0'] = {'patterns': ['dge']}
        
        # Skill 11.0: Magic E
        patterns['11.0'] = {'magic_e': True}
        
        # Skill 12.1: Closed syllable exceptions
        patterns['12.1'] = {'exceptions': ['all', 'alk', 'old', 'ild', 'ind', 'ost', 'olt']}
        
        # Skills 13.x: Soft sounds
        patterns['13.1'] = {'soft_g': ['ge', 'gi', 'gy']}
        patterns['13.2'] = {'soft_c': ['ce', 'ci', 'cy']}
        
        # Skill 14.1: Y as vowel
        patterns['14.1'] = {'y_as_vowel': True}
        
        # Skill 16.0: Silent letters
        patterns['16.0'] = {'silent': ['kn', 'wr', 'mb', 'gh', 'gn']}
        
        # Skills 18.x: Vowel teams (CRITICAL FOR DECODABILITY)
        patterns['18.1'] = {'vowel_teams': ['ai', 'ay', 'ei', 'eigh']}
        patterns['18.2'] = {'vowel_teams': ['ea', 'ee', 'ie', 'ey']}
        patterns['18.3'] = {'vowel_teams': ['oa', 'oe', 'ow']}
        patterns['18.4'] = {'vowel_teams': ['ou', 'ow']}  # as in "how"
        patterns['18.5'] = {'vowel_teams': ['ie', 'igh']}
        patterns['18.6'] = {'vowel_teams': ['ue', 'ew', 'eu']}
        patterns['18.7'] = {'vowel_teams': ['oo']}  # long oo as in "moon"
        patterns['18.8'] = {'vowel_teams': ['oo']}  # short oo as in "book"
        patterns['18.9'] = {'vowel_teams': ['ou']}  # as in "could"
        patterns['18.10'] = {'vowel_teams': ['au', 'aw', 'augh']}
        patterns['18.11'] = {'vowel_teams': ['oi', 'oy']}
        patterns['18.12'] = {'vowel_teams': ['ea']}  # as in "bread"
        
        # Skills 20.x: R-controlled
        patterns['20.1'] = {'r_controlled': ['er', 'ir', 'ur']}
        patterns['20.2'] = {'r_controlled': ['or', 'ore']}
        patterns['20.3'] = {'r_controlled': ['ar']}
        patterns['20.4'] = {'r_controlled': ['war']}
        patterns['20.5'] = {'r_controlled': ['wor']}
        
        return patterns
    
    def get_available_patterns(self, skill_id):
        """Get all patterns available up to and including a skill level"""
        available = {
            'short_vowels': [],
            'long_vowels': [],
            'consonants': [],
            'digraphs': [],
            'initial_blends': [],
            'final_blends': [],
            'vowel_teams': [],
            'r_controlled': [],
            'special_patterns': [],
            'silent_letters': [],
            'magic_e': False,
            'y_as_vowel': False,
            'soft_sounds': []
        }
        
        # Get skill index
        if skill_id not in self.skill_sequence:
            return available
            
        skill_index = self.skill_sequence.index(skill_id)
        
        # Accumulate patterns from all previous skills
        for i in range(skill_index + 1):
            current_skill = self.skill_sequence[i]
            if current_skill in self.patterns_taught_by_skill:
                skill_patterns = self.patterns_taught_by_skill[current_skill]
                
                # Add patterns based on type
                if 'short_vowels' in skill_patterns:
                    available['short_vowels'].extend(skill_patterns['short_vowels'])
                if 'consonants' in skill_patterns:
                    available['consonants'] = skill_patterns['consonants']
                if 'digraphs' in skill_patterns:
                    available['digraphs'].extend(skill_patterns['digraphs'])
                if 'initial_blends' in skill_patterns:
                    available['initial_blends'].extend(skill_patterns['initial_blends'])
                if 'final_blends' in skill_patterns:
                    available['final_blends'].extend(skill_patterns['final_blends'])
                if 'vowel_teams' in skill_patterns:
                    available['vowel_teams'].extend(skill_patterns['vowel_teams'])
                if 'r_controlled' in skill_patterns:
                    available['r_controlled'].extend(skill_patterns['r_controlled'])
                if 'floss' in skill_patterns:
                    available['special_patterns'].extend(skill_patterns['floss'])
                if 'patterns' in skill_patterns:
                    available['special_patterns'].extend(skill_patterns['patterns'])
                if 'exceptions' in skill_patterns:
                    available['special_patterns'].extend(skill_patterns['exceptions'])
                if 'silent' in skill_patterns:
                    available['silent_letters'].extend(skill_patterns['silent'])
                if 'magic_e' in skill_patterns:
                    available['magic_e'] = True
                if 'y_as_vowel' in skill_patterns:
                    available['y_as_vowel'] = True
                if 'soft_g' in skill_patterns:
                    available['soft_sounds'].extend(skill_patterns['soft_g'])
                if 'soft_c' in skill_patterns:
                    available['soft_sounds'].extend(skill_patterns['soft_c'])
        
        return available
    
    def is_word_decodable(self, word, skill_id):
        """Check if a word is fully decodable at the given skill level"""
        word_lower = word.lower()
        
        # Get available patterns at this skill level
        available = self.get_available_patterns(skill_id)
        
        # Get available heart words
        available_heart_words = set()
        if skill_id in self.skill_sequence:
            skill_index = self.skill_sequence.index(skill_id)
            for i in range(skill_index + 1):
                current_skill = self.skill_sequence[i]
                available_heart_words.update(self.heart_words_by_skill.get(current_skill, set()))
        
        # If it's a heart word, it's allowed
        if word_lower in available_heart_words:
            return True, "heart word"
        
        # Check for vowel teams (must check BEFORE single vowels)
        all_vowel_teams = [
            'eigh', 'augh', 'ough',  # 4-letter teams
            'ai', 'ay', 'ea', 'ee', 'ei', 'ey', 'ie', 'oa', 'oe', 
            'oi', 'oo', 'ou', 'ow', 'oy', 'ue', 'ui', 'au', 'aw',
            'ew', 'eu', 'igh'
        ]
        
        for vt in all_vowel_teams:
            if vt in word_lower and vt not in available['vowel_teams']:
                return False, f"contains '{vt}' vowel team not yet taught"
        
        # Check for r-controlled patterns
        r_patterns = ['war', 'wor', 'ar', 'er', 'ir', 'or', 'ur', 'ore']
        for rp in r_patterns:
            if rp in word_lower and rp not in available['r_controlled']:
                return False, f"contains '{rp}' r-controlled pattern not yet taught"
        
        # Check for Magic E pattern
        if (len(word_lower) >= 4 and 
            word_lower[-1] == 'e' and 
            word_lower[-2] not in 'aeiou' and
            word_lower[-3] in 'aeiou' and
            not available['magic_e']):
            return False, "contains Magic E pattern not yet taught"
        
        # Check for initial blends
        for blend in ['scr', 'spr', 'str', 'spl', 'squ', 'thr',  # 3-letter first
                      'bl', 'cl', 'fl', 'gl', 'pl', 'sl', 
                      'br', 'cr', 'dr', 'fr', 'gr', 'pr', 'tr',
                      'sc', 'sk', 'sm', 'sn', 'sp', 'st', 'sw']:
            if word_lower.startswith(blend) and blend not in available['initial_blends']:
                return False, f"contains '{blend}' initial blend not yet taught"
        
        # Check for final blends
        for blend in ['nt', 'nd', 'nk', 'lt', 'ld', 'lf', 'lk', 'lm', 'lp', 
                      'mp', 'ng', 'sk', 'st', 'ft']:
            if word_lower.endswith(blend) and blend not in available['final_blends']:
                return False, f"contains '{blend}' final blend not yet taught"
        
        # Check for digraphs
        for digraph in ['ch', 'sh', 'th', 'wh', 'ck', 'tch', 'dge']:
            if digraph in word_lower:
                if digraph == 'tch' and 'tch' not in available['special_patterns']:
                    return False, "contains 'tch' pattern not yet taught"
                elif digraph == 'dge' and 'dge' not in available['special_patterns']:
                    return False, "contains 'dge' pattern not yet taught"
                elif digraph in ['ch', 'sh', 'th', 'wh', 'ck'] and digraph not in available['digraphs']:
                    return False, f"contains '{digraph}' digraph not yet taught"
        
        # Check for Y as vowel
        if 'y' in word_lower and not available['y_as_vowel']:
            # Y is acting as a vowel if it's at the end or not followed by a vowel
            for i, char in enumerate(word_lower):
                if char == 'y':
                    if i == len(word_lower) - 1:  # Y at end
                        return False, "contains Y as vowel not yet taught"
                    elif i < len(word_lower) - 1 and word_lower[i + 1] not in 'aeiou':
                        # Y not followed by vowel, so it's acting as vowel
                        return False, "contains Y as vowel not yet taught"
        
        # Check for soft C and G
        for i in range(len(word_lower) - 1):
            if word_lower[i] == 'c' and word_lower[i+1] in 'eiy':
                if f'c{word_lower[i+1]}' not in available['soft_sounds']:
                    return False, "contains soft C not yet taught"
            if word_lower[i] == 'g' and word_lower[i+1] in 'eiy':
                if f'g{word_lower[i+1]}' not in available['soft_sounds']:
                    return False, "contains soft G not yet taught"
        
        # Check for silent letters
        silent_patterns = ['kn', 'wr', 'mb', 'gh', 'gn']
        for sp in silent_patterns:
            if sp in word_lower and sp not in available['silent_letters']:
                return False, f"contains '{sp}' silent letter pattern not yet taught"
        
        # For early CVC skills, apply stricter constraints
        if skill_id.startswith('1.'):
            # Should be exactly CVC pattern
            if len(word) != 3:
                return False, "not a 3-letter CVC word"
            
            # Check structure: consonant-vowel-consonant
            if not (word[0] not in 'aeiou' and 
                   word[1] in available['short_vowels'] and 
                   word[2] not in 'aeiou'):
                return False, "not CVC structure"
            
            # No blends or digraphs allowed
            if any(blend in word_lower for blend in ['bl', 'cl', 'fl', 'gl', 'pl', 'sl', 
                                                      'br', 'cr', 'dr', 'fr', 'gr', 'pr', 
                                                      'tr', 'sc', 'sk', 'sm', 'sn', 'sp', 
                                                      'st', 'sw', 'ch', 'sh', 'th', 'wh', 'ck']):
                return False, "contains blends or digraphs not allowed in CVC"
        
        return True, "decodable"
    
    def _define_length_limits(self):
        """Define appropriate word length limits for each skill"""
        return {
            # CVC words - exactly 3 letters
            '1.1': {'min': 3, 'max': 3}, '1.2': {'min': 3, 'max': 3},
            '1.3': {'min': 3, 'max': 3}, '1.4': {'min': 3, 'max': 3},
            '1.5': {'min': 3, 'max': 3},
            
            # CV words - exactly 2 letters
            '2.1': {'min': 2, 'max': 3}, '2.2': {'min': 2, 'max': 2},
            '2.3': {'min': 1, 'max': 2},  # for "I"
            
            # C/K rule - 3-4 letters
            '3.1': {'min': 3, 'max': 4}, '3.2': {'min': 3, 'max': 4},
            
            # Digraphs - 3-4 letters
            '4.1': {'min': 3, 'max': 4}, '4.2': {'min': 3, 'max': 4},
            '4.3': {'min': 3, 'max': 4}, '4.4': {'min': 3, 'max': 4},
            '4.5': {'min': 3, 'max': 4},
            
            # Initial blends - 4-5 letters
            '5.1': {'min': 4, 'max': 5}, '5.2': {'min': 4, 'max': 5},
            '5.3': {'min': 4, 'max': 5}, '5.4': {'min': 5, 'max': 6},
            
            # Final blends - 4-5 letters
            '6.1': {'min': 4, 'max': 5}, '6.2': {'min': 4, 'max': 5},
            '6.3': {'min': 4, 'max': 5}, '6.4': {'min': 4, 'max': 5},
            '6.5': {'min': 4, 'max': 5}, '6.6': {'min': 4, 'max': 5},
            '6.7': {'min': 4, 'max': 5}, '6.8': {'min': 4, 'max': 5},
            '6.9': {'min': 4, 'max': 5}, '6.10': {'min': 4, 'max': 5},
            '6.11': {'min': 4, 'max': 5}, '6.12': {'min': 4, 'max': 5},
            '6.13': {'min': 4, 'max': 5}, '6.14': {'min': 4, 'max': 5},
            '6.15': {'min': 4, 'max': 5},
            
            # Special rules
            '7.0': {'min': 4, 'max': 6},  # FLOSS
            '8.0': {'min': 6, 'max': 10}, # Compound
            '9.0': {'min': 5, 'max': 6},  # Catch
            '10.0': {'min': 5, 'max': 6}, # Bridge
            
            # Magic E
            '11.0': {'min': 4, 'max': 5},
            
            # More advanced patterns - gradually increase
            '12.1': {'min': 3, 'max': 6},
            '13.1': {'min': 4, 'max': 7}, '13.2': {'min': 4, 'max': 7},
            '14.1': {'min': 3, 'max': 6},
            '15.1': {'min': 4, 'max': 8}, '15.2': {'min': 4, 'max': 8},
            '15.3': {'min': 4, 'max': 8},
            '16.0': {'min': 4, 'max': 7},
            '17.0': {'min': 4, 'max': 8},
            
            # Vowel teams
            '18.1': {'min': 4, 'max': 8}, '18.2': {'min': 4, 'max': 8},
            '18.3': {'min': 4, 'max': 8}, '18.4': {'min': 4, 'max': 8},
            '18.5': {'min': 4, 'max': 8}, '18.6': {'min': 4, 'max': 8},
            '18.7': {'min': 4, 'max': 8}, '18.8': {'min': 4, 'max': 8},
            '18.9': {'min': 4, 'max': 8}, '18.10': {'min': 4, 'max': 8},
            '18.11': {'min': 4, 'max': 8}, '18.12': {'min': 4, 'max': 8},
            
            # R-controlled and beyond
            '19.0': {'min': 2, 'max': 10},
            '20.1': {'min': 3, 'max': 10}, '20.2': {'min': 3, 'max': 10},
            '20.3': {'min': 3, 'max': 10}, '20.4': {'min': 3, 'max': 10},
            '20.5': {'min': 3, 'max': 10},
            
            # Advanced patterns
            '21.1': {'min': 6, 'max': 12}, '21.2': {'min': 5, 'max': 10},
            '21.5': {'min': 5, 'max': 10},
            '22.0': {'min': 5, 'max': 15},
            '23.1': {'min': 5, 'max': 15}, '23.2': {'min': 5, 'max': 15},
            '24.0': {'min': 5, 'max': 12}, '25.0': {'min': 5, 'max': 12},
            '26.0': {'min': 5, 'max': 12},
            '27.1': {'min': 3, 'max': 12}, '27.2': {'min': 4, 'max': 12},
            '27.3': {'min': 4, 'max': 12}, '27.5': {'min': 3, 'max': 12},
            '28.0': {'min': 6, 'max': 15}, '29.0': {'min': 4, 'max': 10},
            '30.0': {'min': 3, 'max': 10}
        }
    
    def _load_skill_mappings(self):
        """Load skill type mappings"""
        return {
            '1.1': {'type': 'cvc', 'vowel': 'AE', 'name': 'CVC short a'},
            '1.2': {'type': 'cvc', 'vowel': 'EH', 'name': 'CVC short e'},
            '1.3': {'type': 'cvc', 'vowel': 'IH', 'name': 'CVC short i'},
            '1.4': {'type': 'cvc', 'vowel': 'AA', 'name': 'CVC short o'},
            '1.5': {'type': 'cvc', 'vowel': 'AH', 'name': 'CVC short u'},
            
            '2.1': {'type': 'cv', 'vowel': 'IY', 'name': 'CV long e'},
            '2.2': {'type': 'cv', 'vowel': 'OW', 'name': 'CV long o'},
            '2.3': {'type': 'cv', 'vowel': 'AY', 'name': 'CV long i'},
            
            '3.1': {'type': 'c_or_k', 'rule': 'c_before_aou', 'name': 'c before a,o,u'},
            '3.2': {'type': 'c_or_k', 'rule': 'k_before_eiy', 'name': 'k before e,i,y'},
            
            '4.1': {'type': 'digraph', 'pattern': 'ch', 'name': 'ch digraph'},
            '4.2': {'type': 'digraph', 'pattern': 'sh', 'name': 'sh digraph'},
            '4.3': {'type': 'digraph', 'pattern': 'th', 'name': 'th digraph'},
            '4.4': {'type': 'digraph', 'pattern': 'wh', 'name': 'wh digraph'},
            '4.5': {'type': 'digraph', 'pattern': 'ck', 'name': 'ck digraph'},
            
            '5.1': {'type': 'initial_blend', 'patterns': ['bl', 'cl', 'fl', 'gl', 'pl', 'sl'], 'name': 'l-blends'},
            '5.2': {'type': 'initial_blend', 'patterns': ['br', 'cr', 'dr', 'fr', 'gr', 'pr', 'tr'], 'name': 'r-blends'},
            '5.3': {'type': 'initial_blend', 'patterns': ['sc', 'sk', 'sm', 'sn', 'sp', 'st', 'sw'], 'name': 's-blends'},
            '5.4': {'type': 'initial_blend', 'patterns': ['scr', 'spr', 'str', 'spl', 'squ', 'thr'], 'name': '3-letter blends'},
            
            '6.1': {'type': 'final_blend', 'pattern': 'nt', 'name': 'nt ending'},
            '6.2': {'type': 'final_blend', 'pattern': 'nd', 'name': 'nd ending'},
            '6.3': {'type': 'final_blend', 'pattern': 'nk', 'name': 'nk ending'},
            '6.4': {'type': 'final_blend', 'pattern': 'lt', 'name': 'lt ending'},
            '6.5': {'type': 'final_blend', 'pattern': 'ld', 'name': 'ld ending'},
            '6.6': {'type': 'final_blend', 'pattern': 'lf', 'name': 'lf ending'},
            '6.7': {'type': 'final_blend', 'pattern': 'lk', 'name': 'lk ending'},
            '6.8': {'type': 'final_blend', 'pattern': 'lm', 'name': 'lm ending'},
            '6.9': {'type': 'final_blend', 'pattern': 'lp', 'name': 'lp ending'},
            '6.10': {'type': 'final_blend', 'pattern': 'mp', 'name': 'mp ending'},
            '6.11': {'type': 'final_blend', 'pattern': 'nd', 'name': 'nd ending'},
            '6.12': {'type': 'final_blend', 'pattern': 'ng', 'name': 'ng ending'},
            '6.13': {'type': 'final_blend', 'pattern': 'sk', 'name': 'sk ending'},
            '6.14': {'type': 'final_blend', 'pattern': 'st', 'name': 'st ending'},
            '6.15': {'type': 'final_blend', 'pattern': 'ft', 'name': 'ft ending'},
            
            '7.0': {'type': 'floss', 'name': 'FLOSS rule'},
            '8.0': {'type': 'compound', 'name': 'Compound words'},
            '9.0': {'type': 'catch', 'name': 'Catch rule'},
            '10.0': {'type': 'bridge', 'name': 'Bridge rule'},
            
            '11.0': {'type': 'magic_e', 'name': 'Magic E'},
            
            '12.1': {'type': 'rule_breaker', 'name': 'Closed syllable exceptions'},
            
            '13.1': {'type': 'soft_sound', 'letter': 'g', 'name': 'Soft g'},
            '13.2': {'type': 'soft_sound', 'letter': 'c', 'name': 'Soft c'},
            
            '14.1': {'type': 'y_vowel', 'name': 'Y as vowel'},
            
            '15.1': {'type': 'schwa', 'vowel': 'o', 'name': 'o says uh'},
            '15.2': {'type': 'schwa', 'vowel': 'a', 'name': 'a says uh'},
            '15.3': {'type': 'schwa', 'vowel': 'e', 'name': 'e says uh'},
            
            '16.0': {'type': 'silent', 'name': 'Silent letters'},
            
            '17.0': {'type': 'ed_sounds', 'name': '3 sounds of ed'},
            
            '18.1': {'type': 'vowel_team', 'patterns': ['ai', 'ay', 'ei', 'eigh'], 'name': 'Long a teams'},
            '18.2': {'type': 'vowel_team', 'patterns': ['ea', 'ee', 'ie', 'ey'], 'name': 'Long e teams'},
            '18.3': {'type': 'vowel_team', 'patterns': ['oa', 'oe', 'ow'], 'name': 'Long o teams'},
            '18.4': {'type': 'vowel_team', 'patterns': ['ou', 'ow'], 'name': 'ow sound'},
            '18.5': {'type': 'vowel_team', 'patterns': ['ie', 'igh'], 'name': 'Long i teams'},
            '18.6': {'type': 'vowel_team', 'patterns': ['ue', 'ew', 'eu'], 'name': 'Long u teams'},
            '18.7': {'type': 'vowel_team', 'patterns': ['oo'], 'name': 'Long oo'},
            '18.8': {'type': 'vowel_team', 'patterns': ['oo'], 'name': 'Short oo'},
            '18.9': {'type': 'vowel_team', 'patterns': ['ou'], 'name': 'ou says u'},
            '18.10': {'type': 'vowel_team', 'patterns': ['au', 'aw'], 'name': 'aw sound'},
            '18.11': {'type': 'vowel_team', 'patterns': ['oi', 'oy'], 'name': 'oy sound'},
            '18.12': {'type': 'vowel_team', 'patterns': ['ea'], 'name': 'ea says e'},
            
            '19.0': {'type': 'homophones', 'name': 'Homophones'},
            
            '20.1': {'type': 'r_controlled', 'patterns': ['er', 'ir', 'ur'], 'name': 'er sound'},
            '20.2': {'type': 'r_controlled', 'pattern': 'or', 'name': 'or sound'},
            '20.3': {'type': 'r_controlled', 'pattern': 'ar', 'name': 'ar sound'},
            '20.4': {'type': 'r_controlled', 'pattern': 'war', 'name': 'war sound'},
            '20.5': {'type': 'r_controlled', 'pattern': 'wor', 'name': 'wor sound'},
            
            '21.1': {'type': 'compound', 'name': 'Compound words'},
            '21.2': {'type': 'c_le', 'name': 'Turtle words'},
            '21.5': {'type': 'vccv_double', 'name': 'Rabbit words'},
            
            '22.0': {'type': 'prefix', 'name': 'Common prefixes'},
            '23.1': {'type': 'suffix', 'name': 'Common suffixes'},
            '23.2': {'type': 'suffix_irregular', 'name': 'Irregular suffixes'},
            '24.0': {'type': '1_1_1_doubling', 'name': '1-1-1 doubling'},
            '25.0': {'type': 'e_drop', 'name': 'E-dropping rule'},
            '26.0': {'type': 'y_to_i', 'name': 'Change y to i'},
            '27.1': {'type': 'plural_s', 'name': 'Plural -s'},
            '27.2': {'type': 'plural_es', 'name': 'Plural -es'},
            '27.3': {'type': 'plural_f_to_v', 'name': 'f to v plural'},
            '27.5': {'type': 'plural_irregular', 'name': 'Irregular plurals'},
            '28.0': {'type': '2_1_1_doubling', 'name': '2-1-1 doubling'},
            '29.0': {'type': 'silent_e_not_plural', 'name': 'Silent e not plural'},
            '30.0': {'type': 'contractions', 'name': 'Contractions'}
        }
    
    def generate_for_skill(self, skill_id):
        """Generate words for a specific skill with decodability checking"""
        skill_info = self.skill_mappings.get(skill_id, {})
        skill_type = skill_info.get('type')
        
        # Get raw word candidates based on skill type
        candidates = self._get_word_candidates(skill_id, skill_type, skill_info)
        
        # Filter for decodability
        decodable_words = []
        decodability_report = {'total_candidates': len(candidates), 'rejected': []}
        
        for word_entry in candidates:
            word = word_entry['word']
            is_decodable, reason = self.is_word_decodable(word, skill_id)
            
            if is_decodable:
                decodable_words.append(word_entry)
            else:
                decodability_report['rejected'].append({
                    'word': word,
                    'reason': reason
                })
        
        # Print decodability summary
        print(f"  Decodability check: {len(decodable_words)}/{len(candidates)} words passed")
        if decodability_report['rejected']:
            print(f"  Sample rejections:")
            for reject in decodability_report['rejected'][:5]:
                print(f"    - '{reject['word']}': {reject['reason']}")
        
        return decodable_words
    
    def _get_word_candidates(self, skill_id, skill_type, skill_info):
        """Get initial word candidates for a skill (before decodability check)"""
        candidates = []
        
        if skill_type == 'cvc':
            candidates = self._get_cvc_candidates(skill_id, skill_info)
        elif skill_type == 'cv':
            candidates = self._get_cv_candidates(skill_id, skill_info)
        elif skill_type == 'c_or_k':
            candidates = self._get_c_or_k_candidates(skill_id, skill_info)
        elif skill_type == 'digraph':
            candidates = self._get_digraph_candidates(skill_id, skill_info)
        elif skill_type == 'initial_blend':
            candidates = self._get_initial_blend_candidates(skill_id, skill_info)
        elif skill_type == 'final_blend':
            candidates = self._get_final_blend_candidates(skill_id, skill_info)
        elif skill_type == 'floss':
            candidates = self._get_floss_candidates(skill_id, skill_info)
        elif skill_type == 'compound':
            candidates = self._get_compound_candidates(skill_id, skill_info)
        elif skill_type == 'catch':
            candidates = self._get_catch_candidates(skill_id, skill_info)
        elif skill_type == 'bridge':
            candidates = self._get_bridge_candidates(skill_id, skill_info)
        elif skill_type == 'magic_e':
            candidates = self._get_magic_e_candidates(skill_id, skill_info)
        elif skill_type == 'vowel_team':
            candidates = self._get_vowel_team_candidates(skill_id, skill_info)
        elif skill_type == 'r_controlled':
            candidates = self._get_r_controlled_candidates(skill_id, skill_info)
        else:
            print(f"  Generator not implemented for type: {skill_type}")
        
        return candidates
    
    def _get_cvc_candidates(self, skill_id, skill_info):
        """Get CVC word candidates"""
        target_vowel = skill_info['vowel']
        candidates = []
        
        for word, pronunciations in self.cmu_dict.items():
            if len(word) != 3 or not word.isalpha():
                continue
            
            phonemes = pronunciations[0]
            
            if (len(phonemes) == 3 and 
                self._is_consonant_sound(phonemes[0]) and
                target_vowel in phonemes[1] and
                self._is_consonant_sound(phonemes[2])):
                
                candidates.append(self._create_word_entry(
                    skill_id, word, word[1], 'medial', 'target'
                ))
        
        return candidates
    
    def _get_cv_candidates(self, skill_id, skill_info):
        """Get CV word candidates"""
        # For CV words, we use specific allowed words
        allowed_cv_words = {
            '2.1': ['be', 'he', 'me', 'we', 'she'],
            '2.2': ['go', 'no', 'so'],
            '2.3': ['I', 'hi']
        }
        
        candidates = []
        for word in allowed_cv_words.get(skill_id, []):
            if word.lower() in self.cmu_dict:
                candidates.append(self._create_word_entry(
                    skill_id, word, word[-1], 'final', 'target'
                ))
        
        return candidates
    
    def _get_c_or_k_candidates(self, skill_id, skill_info):
        """Get C or K rule word candidates"""
        rule = skill_info['rule']
        candidates = []
        
        for word in self.cmu_dict.keys():
            if rule == 'c_before_aou':
                if any(word.startswith(f'c{v}') for v in ['a', 'o', 'u']):
                    candidates.append(self._create_word_entry(
                        skill_id, word, 'c', 'initial', 'target'
                    ))
            elif rule == 'k_before_eiy':
                if any(word.startswith(f'k{v}') for v in ['e', 'i', 'y']):
                    candidates.append(self._create_word_entry(
                        skill_id, word, 'k', 'initial', 'target'
                    ))
        
        return candidates
    
    def _get_digraph_candidates(self, skill_id, skill_info):
        """Get digraph word candidates"""
        digraph = skill_info['pattern']
        candidates = []
        
        for word in self.cmu_dict.keys():
            if digraph in word:
                position = self._get_pattern_position(word, digraph)
                candidates.append(self._create_word_entry(
                    skill_id, word, digraph, position, 'target'
                ))
        
        return candidates
    
    def _get_initial_blend_candidates(self, skill_id, skill_info):
        """Get initial blend word candidates"""
        patterns = skill_info['patterns']
        candidates = []
        
        for pattern in patterns:
            for word in self.cmu_dict.keys():
                if word.startswith(pattern) and len(word) > len(pattern):
                    candidates.append(self._create_word_entry(
                        skill_id, word, pattern, 'initial', 'target'
                    ))
        
        return candidates
    
    def _get_final_blend_candidates(self, skill_id, skill_info):
        """Get final blend word candidates"""
        pattern = skill_info['pattern']
        candidates = []
        
        for word in self.cmu_dict.keys():
            if word.endswith(pattern) and len(word) > len(pattern):
                candidates.append(self._create_word_entry(
                    skill_id, word, pattern, 'final', 'target'
                ))
        
        return candidates
    
    def _get_floss_candidates(self, skill_id, skill_info):
        """Get FLOSS rule word candidates"""
        candidates = []
        floss_patterns = ['ff', 'll', 'ss', 'zz']
        
        for word in self.cmu_dict.keys():
            for pattern in floss_patterns:
                if pattern in word:
                    idx = word.find(pattern)
                    if idx > 0 and word[idx-1] in 'aeiou':
                        candidates.append(self._create_word_entry(
                            skill_id, word, pattern, 'medial/final', 'target'
                        ))
        
        return candidates
    
    def _get_compound_candidates(self, skill_id, skill_info):
        """Get compound word candidates"""
        candidates = []
        word_set = set(self.cmu_dict.keys())
        
        # Common compound words that should be decodable by skill 8.0
        common_compounds = [
            'backpack', 'bedtime', 'cannot', 'inside', 'himself',
            'sunset', 'upon', 'within', 'without', 'into'
        ]
        
        for word in common_compounds:
            if word in self.cmu_dict:
                candidates.append(self._create_word_entry(
                    skill_id, word, 'compound', 'multiple', 'target'
                ))
        
        return candidates
    
    def _get_catch_candidates(self, skill_id, skill_info):
        """Get catch rule (tch) word candidates"""
        candidates = []
        
        for word in self.cmu_dict.keys():
            if 'tch' in word:
                candidates.append(self._create_word_entry(
                    skill_id, word, 'tch', self._get_pattern_position(word, 'tch'), 'target'
                ))
        
        return candidates
    
    def _get_bridge_candidates(self, skill_id, skill_info):
        """Get bridge rule (dge) word candidates"""
        candidates = []
        
        for word in self.cmu_dict.keys():
            if 'dge' in word:
                candidates.append(self._create_word_entry(
                    skill_id, word, 'dge', self._get_pattern_position(word, 'dge'), 'target'
                ))
        
        return candidates
    
    def _get_magic_e_candidates(self, skill_id, skill_info):
        """Get Magic E word candidates"""
        candidates = []
        
        for word in self.cmu_dict.keys():
            if (len(word) >= 4 and 
                word[-1] == 'e' and 
                word[-2] not in 'aeiou' and
                word[-3] in 'aeiou' and
                word[-4] not in 'aeiou'):
                
                candidates.append(self._create_word_entry(
                    skill_id, word, 'VCe', 'final', 'target'
                ))
        
        return candidates
    
    def _get_vowel_team_candidates(self, skill_id, skill_info):
        """Get vowel team word candidates"""
        patterns = skill_info['patterns']
        candidates = []
        
        for pattern in patterns:
            for word in self.cmu_dict.keys():
                if pattern in word:
                    candidates.append(self._create_word_entry(
                        skill_id, word, pattern, 
                        self._get_pattern_position(word, pattern), 'target'
                    ))
        
        return candidates
    
    def _get_r_controlled_candidates(self, skill_id, skill_info):
        """Get r-controlled word candidates"""
        if 'patterns' in skill_info:
            patterns = skill_info['patterns']
        else:
            patterns = [skill_info['pattern']]
            
        candidates = []
        
        for pattern in patterns:
            for word in self.cmu_dict.keys():
                if pattern in word:
                    candidates.append(self._create_word_entry(
                        skill_id, word, pattern,
                        self._get_pattern_position(word, pattern), 'target'
                    ))
        
        return candidates
    
    def filter_words(self, words, skill_id, max_words=50):
        """Filter words by length, appropriateness, and frequency"""
        # Apply length filter
        if skill_id in self.word_length_limits:
            limits = self.word_length_limits[skill_id]
            words = [w for w in words if limits['min'] <= len(w['word']) <= limits['max']]
        
        # Apply appropriateness filter
        words = [w for w in words if w['word'].lower() not in self.inappropriate_words]
        
        # Remove very rare words
        words = [w for w in words if self.freq_dist[w['word']] > 2]
        
        # Remove duplicates
        seen = set()
        unique_words = []
        for w in words:
            if w['word'] not in seen:
                seen.add(w['word'])
                unique_words.append(w)
        
        # Sort by frequency
        unique_words.sort(key=lambda x: self.freq_dist[x['word']], reverse=True)
        
        return unique_words[:max_words]
    
    def generate_all_skills(self, output_dir='word_lists_decodable'):
        """Generate word lists for all skills with decodability enforcement"""
        os.makedirs(output_dir, exist_ok=True)
        
        summary = []
        decodability_stats = []
        
        for skill_id in self.skill_sequence:
            if skill_id not in self.skill_mappings:
                continue
                
            skill_info = self.skill_mappings[skill_id]
            print(f"\nGenerating words for skill {skill_id}: {skill_info['name']}")
            
            # Show what patterns are available
            available = self.get_available_patterns(skill_id)
            print(f"  Available patterns at this level:")
            if available['short_vowels']:
                print(f"    Short vowels: {available['short_vowels']}")
            if available['digraphs']:
                print(f"    Digraphs: {available['digraphs']}")
            if available['initial_blends']:
                print(f"    Initial blends: {available['initial_blends']}")
            if available['vowel_teams']:
                print(f"    Vowel teams: {available['vowel_teams']}")
            
            try:
                # Generate words WITH decodability checking
                words = self.generate_for_skill(skill_id)
                
                if words:
                    # Apply additional filters
                    words = self.filter_words(words, skill_id, max_words=50)
                    
                    print(f"  Final count: {len(words)} decodable words")
                    
                    # Export to CSV
                    filename = f"skill_{skill_id}_{skill_info['name'].replace(' ', '_').replace('/', '_')}.csv"
                    filepath = os.path.join(output_dir, filename)
                    self.export_to_csv(words, filepath)
                    
                    summary.append({
                        'skill_id': skill_id,
                        'skill_name': skill_info['name'],
                        'word_count': len(words),
                        'filename': filename
                    })
                else:
                    print(f"  No decodable words found for {skill_id}")
                    
            except Exception as e:
                print(f"  Error generating {skill_id}: {str(e)}")
        
        # Export summary
        summary_df = pd.DataFrame(summary)
        summary_df.to_csv(os.path.join(output_dir, 'generation_summary.csv'), index=False)
        
        print(f"\nGeneration complete!")
        print(f"Decodable word lists saved to: {output_dir}/")
        
        return summary
    
    def export_to_csv(self, words, filename):
        """Export word list to CSV"""
        df = pd.DataFrame(words)
        df.to_csv(filename, index=False)
    
    # Helper methods
    def _create_word_entry(self, skill_id, word, pattern, position, word_type, 
                          syllable_breaks=None, notes=''):
        """Create a standardized word entry"""
        return {
            'skill_id': skill_id,
            'word': word,
            'primary_pattern': pattern,
            'pattern_position': position,
            'is_HF': word in self.high_freq_words,
            'is_heart': False,
            'irregular_part': '',
            'irregular_sound': '',
            'syllable_breaks': syllable_breaks or word,
            'word_type': word_type,
            'notes': notes
        }
    
    def _is_consonant_sound(self, phoneme):
        """Check if phoneme is a consonant"""
        vowel_sounds = ['AA', 'AE', 'AH', 'AO', 'AW', 'AY', 'EH', 'ER', 
                       'EY', 'IH', 'IY', 'OW', 'OY', 'UH', 'UW']
        return not any(v in phoneme for v in vowel_sounds)
    
    def _get_pattern_position(self, word, pattern):
        """Determine where pattern appears in word"""
        if word.startswith(pattern):
            return 'initial'
        elif word.endswith(pattern):
            return 'final'
        else:
            return 'medial'

def main():
    """Main execution function"""
    print("=" * 60)
    print("DECODABLE WORD GENERATOR")
    print("Ensuring all words are 100% decodable at each skill level")
    print("=" * 60)
    
    # Initialize generator
    generator = DecodableWordGenerator()
    
    # Generate all word lists
    print("\nGenerating decodable word lists for all skills...")
    summary = generator.generate_all_skills()
    
    # Print final summary
    print("\n" + "=" * 60)
    print("GENERATION COMPLETE!")
    print("=" * 60)
    print(f"Total skills processed: {len(summary)}")
    print(f"Total words generated: {sum(s['word_count'] for s in summary)}")
    
    print("\nKey improvements:")
    print("✓ Every word is checked for complete decodability")
    print("✓ No words contain patterns not yet taught")
    print("✓ 'stood' won't appear at skill 5.3 anymore!")
    print("✓ Heart words are properly tracked and allowed")

if __name__ == "__main__":
    main()
