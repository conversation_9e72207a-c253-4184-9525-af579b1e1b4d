#!/usr/bin/env python3
"""
Demo: Show how the decodable generator correctly handles skill 5.3 (s-blends)
"""

from decodable_word_generator import DecodableWordGenerator
import pandas as pd

def demo_skill_5_3():
    """Demonstrate decodability checking for skill 5.3"""
    print("DECODABLE WORD GENERATOR DEMO: Skill 5.3 (s-blends)")
    print("=" * 70)
    
    # Initialize generator
    generator = DecodableWordGenerator()
    
    # Show what patterns are available at skill 5.3
    print("\nPatterns available at skill 5.3:")
    available = generator.get_available_patterns('5.3')
    print(f"  Short vowels: {available['short_vowels']}")
    print(f"  Digraphs: {available['digraphs']}")
    print(f"  Initial blends: {available['initial_blends']}")
    print(f"  Final blends: {available['final_blends']}")
    print(f"  Vowel teams: {available['vowel_teams']} (none yet!)")
    print(f"  Magic E: {available['magic_e']}")
    
    # Test problematic words from original list
    print("\n\nTesting words from original skill 5.3 list:")
    print("-" * 70)
    
    test_words = [
        # Words that should FAIL decodability check
        ('stood', "Contains 'oo' vowel team"),
        ('state', "Contains Magic E pattern"),
        ('stage', "Contains Magic E pattern"),
        ('space', "Contains Magic E pattern"),
        ('sweet', "Contains 'ee' vowel team"),
        ('spoke', "Contains Magic E pattern"),
        ('speed', "Contains 'ee' vowel team"),
        ('store', "Contains Magic E pattern"),
        ('scale', "Contains Magic E pattern"),
        ('snow', "Contains 'ow' vowel team"),
        ('smile', "Contains Magic E pattern"),
        ('stone', "Contains Magic E pattern"),
        ('spite', "Contains Magic E pattern"),
        ('steel', "Contains 'ee' vowel team"),
        ('snake', "Contains Magic E pattern"),
        ('smoke', "Contains Magic E pattern"),
        
        # Words that should PASS decodability check
        ('stop', "All patterns decodable"),
        ('step', "All patterns decodable"),
        ('stick', "All patterns decodable"),
        ('stand', "All patterns decodable"),
        ('spend', "All patterns decodable"),
        ('spot', "All patterns decodable"),
        ('spin', "All patterns decodable"),
        ('skip', "All patterns decodable"),
        ('skin', "All patterns decodable"),
        ('scan', "All patterns decodable"),
        ('snap', "All patterns decodable"),
        ('swim', "All patterns decodable"),
        ('swam', "All patterns decodable"),
        ('stem', "All patterns decodable"),
        ('spit', "All patterns decodable")
    ]
    
    passed = []
    failed = []
    
    for word, expected in test_words:
        is_decodable, reason = generator.is_word_decodable(word, '5.3')
        
        if is_decodable:
            passed.append(word)
            status = "✓ PASS"
        else:
            failed.append((word, reason))
            status = "❌ FAIL"
        
        print(f"{status} '{word}' - {expected}")
        if not is_decodable:
            print(f"      Reason: {reason}")
    
    # Summary
    print(f"\n\nSUMMARY:")
    print(f"  Passed: {len(passed)} words")
    print(f"  Failed: {len(failed)} words")
    
    print(f"\nDecodable words that WOULD be included:")
    for word in passed[:10]:
        print(f"  - {word}")
    
    print(f"\nNon-decodable words that are CORRECTLY REJECTED:")
    for word, reason in failed[:10]:
        print(f"  - {word}: {reason}")
    
    # Generate a small sample of actual words
    print("\n\nGenerating actual word list for skill 5.3...")
    print("-" * 70)
    
    # Get word candidates
    skill_info = generator.skill_mappings['5.3']
    candidates = generator._get_initial_blend_candidates('5.3', skill_info)
    
    # Show decodability filtering in action
    decodable = []
    rejected = []
    
    for candidate in candidates[:50]:  # Check first 50 candidates
        word = candidate['word']
        is_decodable, reason = generator.is_word_decodable(word, '5.3')
        
        if is_decodable:
            decodable.append(candidate)
        else:
            rejected.append((word, reason))
    
    print(f"From {len(candidates[:50])} candidates:")
    print(f"  - {len(decodable)} passed decodability check")
    print(f"  - {len(rejected)} rejected")
    
    if decodable:
        print("\nSample DECODABLE s-blend words:")
        # Filter and sort by frequency
        decodable = generator.filter_words(decodable, '5.3', max_words=15)
        for entry in decodable:
            print(f"  - {entry['word']}")
    
    if rejected:
        print("\nSample REJECTED words:")
        for word, reason in rejected[:10]:
            print(f"  - {word}: {reason}")

def main():
    """Run the demo"""
    demo_skill_5_3()
    
    print("\n\n" + "=" * 70)
    print("KEY TAKEAWAY:")
    print("The decodable generator ensures students can decode EVERY part of EVERY word")
    print("using only the phonics patterns they've learned so far.")
    print("\nNo more 'stood' at skill 5.3! 🎉")

if __name__ == "__main__":
    main()
