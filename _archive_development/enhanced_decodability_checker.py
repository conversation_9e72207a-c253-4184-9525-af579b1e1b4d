#!/usr/bin/env python3
"""
Enhanced Decodability Checker with Specific Pattern Rules
Focuses on common violations like vowel teams appearing too early
"""

import pandas as pd
import os
import re

class EnhancedDecodabilityChecker:
    def __init__(self):
        self.word_lists_dir = '/Users/<USER>/word-generator/word_lists'
        
        # Define when specific patterns are introduced
        self.pattern_introduction = {
            # Vowel Teams (not decodable until skill 18.x)
            'ai': '18.1', 'ay': '18.1', 'eigh': '18.1', 'ei': '18.1',
            'ea_long': '18.2', 'ee': '18.2', 'ie_long': '18.2', 'ey': '18.2',
            'oa': '18.3', 'oe': '18.3', 'ow_long': '18.3',
            'ou_ow': '18.4', 'ow_ow': '18.4',
            'igh': '18.5', 'ie_long_i': '18.5', 
            'ue': '18.6', 'ew': '18.6', 'eu': '18.6',
            'oo_long': '18.7', 'oo_short': '18.8',
            'ou_u': '18.9',
            'au': '18.10', 'aw': '18.10', 'augh': '18.10',
            'oi': '18.11', 'oy': '18.11',
            'ea_short': '18.12',
            
            # R-controlled (not decodable until skill 20.x)
            'ar': '20.3', 'er': '20.1', 'ir': '20.1', 'ur': '20.1', 'or': '20.2',
            'war': '20.4', 'wor': '20.5',
            
            # Special patterns
            'all': '12.1',  # Closed syllable exception
            'alk': '12.1',  # walk, talk pattern
            'old': '12.1', 'ild': '12.1', 'ind': '12.1',
            
            # Silent letters
            'kn': '16.0', 'wr': '16.0', 'mb': '16.0', 'gh': '16.0',
            
            # Magic E
            'VCe': '11.0',
            
            # Y as vowel
            'y_end': '14.1',  # y at end saying /i/ or /ī/
            
            # Soft sounds
            'ce': '13.2', 'ci': '13.2', 'cy': '13.2',
            'ge': '13.1', 'gi': '13.1', 'gy': '13.1',
        }
    
    def check_word_patterns(self, word):
        """Identify all advanced patterns in a word"""
        found_patterns = []
        word_lower = word.lower()
        
        # Check for vowel teams
        vowel_teams = {
            'ai': '18.1', 'ay': '18.1', 'eigh': '18.1',
            'ea': '18.2', 'ee': '18.2', 'ie': '18.2', 'ey': '18.2',
            'oa': '18.3', 'oe': '18.3', 'ow': '18.3',
            'ou': '18.4', 'oi': '18.11', 'oy': '18.11',
            'oo': '18.7', 'ue': '18.6', 'ew': '18.6',
            'au': '18.10', 'aw': '18.10', 'igh': '18.5'
        }
        
        for vt, skill in vowel_teams.items():
            if vt in word_lower:
                found_patterns.append((vt, skill, 'vowel_team'))
        
        # Check for r-controlled
        r_patterns = {
            'ar': '20.3', 'er': '20.1', 'ir': '20.1', 
            'or': '20.2', 'ur': '20.1'
        }
        
        for rp, skill in r_patterns.items():
            if rp in word_lower:
                # Special cases
                if rp == 'ar' and 'war' in word_lower:
                    found_patterns.append(('war', '20.4', 'r_controlled'))
                elif rp == 'or' and 'wor' in word_lower:
                    found_patterns.append(('wor', '20.5', 'r_controlled'))
                else:
                    found_patterns.append((rp, skill, 'r_controlled'))
        
        # Check for silent letters
        silent_patterns = {
            'kn': '16.0', 'wr': '16.0', 'mb': '16.0', 
            'lk': '12.1', 'll': '7.0'
        }
        
        for sp, skill in silent_patterns.items():
            if sp in word_lower:
                if sp == 'lk' and any(word_lower.endswith(end) for end in ['alk', 'olk', 'ulk']):
                    found_patterns.append((sp, skill, 'silent_l'))
                else:
                    found_patterns.append((sp, skill, 'special'))
        
        # Check for Magic E
        if (len(word_lower) >= 4 and 
            word_lower[-1] == 'e' and 
            word_lower[-2] not in 'aeiou' and
            word_lower[-3] in 'aeiou'):
            found_patterns.append(('VCe', '11.0', 'magic_e'))
        
        # Check for Y as vowel at end
        if word_lower.endswith('y') and len(word_lower) > 1:
            found_patterns.append(('y_end', '14.1', 'y_vowel'))
        
        # Check for soft C and G
        for i in range(len(word_lower)-1):
            if word_lower[i] == 'c' and word_lower[i+1] in 'eiy':
                found_patterns.append(('c+e/i/y', '13.2', 'soft_c'))
            if word_lower[i] == 'g' and word_lower[i+1] in 'eiy':
                found_patterns.append(('g+e/i/y', '13.1', 'soft_g'))
        
        return found_patterns
    
    def skill_to_number(self, skill_id):
        """Convert skill ID to number for comparison"""
        try:
            parts = skill_id.split('.')
            return float(parts[0]) + float(parts[1])/10 if len(parts) > 1 else float(parts[0])
        except:
            return 999  # Put unknown skills at end
    
    def validate_skill_words(self, skill_id, df):
        """Validate words for a specific skill"""
        violations = []
        current_skill_num = self.skill_to_number(skill_id)
        
        for _, row in df.iterrows():
            word = row['word']
            
            # Skip if marked as heart word
            if row.get('is_heart', False):
                continue
            
            # Check patterns in word
            patterns = self.check_word_patterns(word)
            
            # Find violations
            word_violations = []
            for pattern, required_skill, pattern_type in patterns:
                required_skill_num = self.skill_to_number(required_skill)
                
                if required_skill_num > current_skill_num:
                    word_violations.append({
                        'pattern': pattern,
                        'pattern_type': pattern_type,
                        'requires_skill': required_skill,
                        'current_skill': skill_id
                    })
            
            if word_violations:
                violations.append({
                    'word': word,
                    'violations': word_violations,
                    'is_HF': row.get('is_HF', False)
                })
        
        return violations
    
    def generate_clean_lists(self):
        """Generate cleaned word lists with only truly decodable words"""
        clean_dir = '/Users/<USER>/word-generator/word_lists_clean'
        os.makedirs(clean_dir, exist_ok=True)
        
        summary = []
        
        csv_files = [f for f in os.listdir(self.word_lists_dir) 
                    if f.startswith('skill_') and f.endswith('.csv')]
        
        for csv_file in sorted(csv_files):
            skill_id = csv_file.split('_')[1]
            filepath = os.path.join(self.word_lists_dir, csv_file)
            
            # Read original list
            df = pd.read_csv(filepath)
            original_count = len(df)
            
            # Get violations
            violations = self.validate_skill_words(skill_id, df)
            violation_words = {v['word'] for v in violations}
            
            # Create clean dataframe
            clean_df = df[~df['word'].isin(violation_words)].copy()
            clean_count = len(clean_df)
            
            # Save clean list
            clean_path = os.path.join(clean_dir, csv_file)
            clean_df.to_csv(clean_path, index=False)
            
            # Track summary
            summary.append({
                'skill_id': skill_id,
                'file': csv_file,
                'original_count': original_count,
                'violations_removed': len(violations),
                'clean_count': clean_count,
                'retention_rate': clean_count / original_count * 100 if original_count > 0 else 0
            })
            
            # Print violations for this skill
            if violations:
                print(f"\nSkill {skill_id} violations ({len(violations)} words):")
                for v in violations[:5]:  # Show first 5
                    print(f"  - {v['word']}: ", end='')
                    for viol in v['violations']:
                        print(f"{viol['pattern']} (needs skill {viol['requires_skill']})", end=' ')
                    print()
                if len(violations) > 5:
                    print(f"  ... and {len(violations)-5} more")
        
        # Save summary
        summary_df = pd.DataFrame(summary)
        summary_df.to_csv(os.path.join(clean_dir, 'cleaning_summary.csv'), index=False)
        
        return summary

def main():
    print("ENHANCED DECODABILITY CHECKER")
    print("=" * 80)
    print("Finding words with patterns that haven't been taught yet...\n")
    
    checker = EnhancedDecodabilityChecker()
    
    # Analyze all lists
    all_violations = []
    
    csv_files = [f for f in os.listdir(checker.word_lists_dir) 
                if f.startswith('skill_') and f.endswith('.csv')]
    
    print("MAJOR VIOLATIONS BY SKILL:")
    print("-" * 80)
    
    for csv_file in sorted(csv_files)[:15]:  # Show first 15 skills
        skill_id = csv_file.split('_')[1]
        filepath = os.path.join(checker.word_lists_dir, csv_file)
        
        df = pd.read_csv(filepath)
        violations = checker.validate_skill_words(skill_id, df)
        
        if violations:
            print(f"\nSkill {skill_id}:")
            
            # Group by violation type
            vowel_team_violations = [v for v in violations 
                                   if any(viol['pattern_type'] == 'vowel_team' 
                                         for viol in v['violations'])]
            r_controlled_violations = [v for v in violations 
                                     if any(viol['pattern_type'] == 'r_controlled' 
                                           for viol in v['violations'])]
            
            if vowel_team_violations:
                print(f"  Vowel team violations ({len(vowel_team_violations)}): ", end='')
                print(', '.join([v['word'] for v in vowel_team_violations[:5]]))
            
            if r_controlled_violations:
                print(f"  R-controlled violations ({len(r_controlled_violations)}): ", end='')
                print(', '.join([v['word'] for v in r_controlled_violations[:5]]))
    
    # Generate clean lists
    print("\n\nGENERATING CLEAN WORD LISTS...")
    print("=" * 80)
    
    summary = checker.generate_clean_lists()
    
    # Print summary
    print("\nCLEANING SUMMARY:")
    print("-" * 80)
    
    total_original = sum(s['original_count'] for s in summary)
    total_removed = sum(s['violations_removed'] for s in summary)
    total_clean = sum(s['clean_count'] for s in summary)
    
    print(f"Total words checked: {total_original}")
    print(f"Total violations removed: {total_removed} ({total_removed/total_original*100:.1f}%)")
    print(f"Total clean words: {total_clean}")
    
    print("\nClean word lists saved to: word_lists_clean/")
    print("Summary saved to: word_lists_clean/cleaning_summary.csv")

if __name__ == "__main__":
    main()
