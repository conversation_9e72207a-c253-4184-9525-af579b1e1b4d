#!/usr/bin/env python3
"""
Extract and export all skill pattern definitions and requirements to CSV
This creates a master configuration file for the O-G word generator
"""

import pandas as pd
import json

def extract_skill_configurations():
    """Extract all skill configurations from the generator scripts"""
    
    # Define all skill configurations in one place
    skill_configs = []
    
    # Skills 1.x - CVC with short vowels
    skill_configs.extend([
        {
            'skill_id': '1.1',
            'skill_name': 'CVC short a',
            'grapheme_patterns': 'single vowel: a',
            'phoneme_requirement': 'AE (short a)',
            'word_length': '3',
            'pattern_type': 'CVC',
            'additional_constraints': 'no blends, no digraphs'
        },
        {
            'skill_id': '1.2',
            'skill_name': 'CVC short e',
            'grapheme_patterns': 'single vowel: e',
            'phoneme_requirement': 'EH (short e)',
            'word_length': '3',
            'pattern_type': 'CVC',
            'additional_constraints': 'no blends, no digraphs'
        },
        {
            'skill_id': '1.3',
            'skill_name': 'CVC short i',
            'grapheme_patterns': 'single vowel: i',
            'phoneme_requirement': 'IH (short i)',
            'word_length': '3',
            'pattern_type': 'CVC',
            'additional_constraints': 'no blends, no digraphs'
        },
        {
            'skill_id': '1.4',
            'skill_name': 'CVC short o',
            'grapheme_patterns': 'single vowel: o',
            'phoneme_requirement': 'AA (short o)',
            'word_length': '3',
            'pattern_type': 'CVC',
            'additional_constraints': 'no blends, no digraphs'
        },
        {
            'skill_id': '1.5',
            'skill_name': 'CVC short u',
            'grapheme_patterns': 'single vowel: u',
            'phoneme_requirement': 'AH (short u)',
            'word_length': '3',
            'pattern_type': 'CVC',
            'additional_constraints': 'no blends, no digraphs'
        },
    ])
    
    # Skills 2.x - CV open syllables
    skill_configs.extend([
        {
            'skill_id': '2.1',
            'skill_name': 'CV long e',
            'grapheme_patterns': 'specific words: be, he, me, we, she',
            'phoneme_requirement': 'IY (long e)',
            'word_length': '2-3',
            'pattern_type': 'CV',
            'additional_constraints': 'specific word list only'
        },
        {
            'skill_id': '2.2',
            'skill_name': 'CV long o',
            'grapheme_patterns': 'specific words: go, no, so',
            'phoneme_requirement': 'OW (long o)',
            'word_length': '2',
            'pattern_type': 'CV',
            'additional_constraints': 'specific word list only'
        },
        {
            'skill_id': '2.3',
            'skill_name': 'CV long i',
            'grapheme_patterns': 'specific words: I, hi',
            'phoneme_requirement': 'AY (long i)',
            'word_length': '1-2',
            'pattern_type': 'CV',
            'additional_constraints': 'specific word list only'
        },
    ])
    
    # Skills 3.x - C or K rule
    skill_configs.extend([
        {
            'skill_id': '3.1',
            'skill_name': 'c before a,o,u',
            'grapheme_patterns': 'c+a, c+o, c+u',
            'phoneme_requirement': 'K',
            'word_length': '3-4',
            'pattern_type': 'c_or_k',
            'additional_constraints': 'c followed by a, o, or u'
        },
        {
            'skill_id': '3.2',
            'skill_name': 'k before e,i,y',
            'grapheme_patterns': 'k+e, k+i, k+y',
            'phoneme_requirement': 'K',
            'word_length': '3-4',
            'pattern_type': 'c_or_k',
            'additional_constraints': 'k followed by e, i, or y'
        },
    ])
    
    # Skills 4.x - Digraphs
    skill_configs.extend([
        {
            'skill_id': '4.1',
            'skill_name': 'ch digraph',
            'grapheme_patterns': 'ch',
            'phoneme_requirement': 'CH',
            'word_length': '3-4',
            'pattern_type': 'digraph',
            'additional_constraints': 'none'
        },
        {
            'skill_id': '4.2',
            'skill_name': 'sh digraph',
            'grapheme_patterns': 'sh',
            'phoneme_requirement': 'SH',
            'word_length': '3-4',
            'pattern_type': 'digraph',
            'additional_constraints': 'none'
        },
        {
            'skill_id': '4.3',
            'skill_name': 'th digraph',
            'grapheme_patterns': 'th',
            'phoneme_requirement': 'TH or DH',
            'word_length': '3-4',
            'pattern_type': 'digraph',
            'additional_constraints': 'can be voiced or unvoiced'
        },
        {
            'skill_id': '4.4',
            'skill_name': 'wh digraph',
            'grapheme_patterns': 'wh',
            'phoneme_requirement': 'W or HW',
            'word_length': '3-4',
            'pattern_type': 'digraph',
            'additional_constraints': 'regional variation in pronunciation'
        },
        {
            'skill_id': '4.5',
            'skill_name': 'ck digraph',
            'grapheme_patterns': 'ck',
            'phoneme_requirement': 'K',
            'word_length': '3-4',
            'pattern_type': 'digraph',
            'additional_constraints': 'follows short vowel'
        },
    ])
    
    # Skills 5.x - Initial blends
    skill_configs.extend([
        {
            'skill_id': '5.1',
            'skill_name': 'l-blends',
            'grapheme_patterns': 'bl, cl, fl, gl, pl, sl',
            'phoneme_requirement': 'consonant + L',
            'word_length': '4-5',
            'pattern_type': 'initial_blend',
            'additional_constraints': 'beginning of word only'
        },
        {
            'skill_id': '5.2',
            'skill_name': 'r-blends',
            'grapheme_patterns': 'br, cr, dr, fr, gr, pr, tr',
            'phoneme_requirement': 'consonant + R',
            'word_length': '4-5',
            'pattern_type': 'initial_blend',
            'additional_constraints': 'beginning of word only'
        },
        {
            'skill_id': '5.3',
            'skill_name': 's-blends',
            'grapheme_patterns': 'sc, sk, sm, sn, sp, st, sw',
            'phoneme_requirement': 'S + consonant',
            'word_length': '4-5',
            'pattern_type': 'initial_blend',
            'additional_constraints': 'beginning of word only'
        },
        {
            'skill_id': '5.4',
            'skill_name': '3-letter blends',
            'grapheme_patterns': 'scr, spr, str, spl, squ, thr',
            'phoneme_requirement': '3 consonant sounds',
            'word_length': '5-6',
            'pattern_type': 'initial_blend',
            'additional_constraints': 'beginning of word only'
        },
    ])
    
    # Skills 6.x - Final blends
    skill_configs.extend([
        {
            'skill_id': '6.1',
            'skill_name': 'nt ending',
            'grapheme_patterns': 'nt',
            'phoneme_requirement': 'N T',
            'word_length': '4-5',
            'pattern_type': 'final_blend',
            'additional_constraints': 'end of word only'
        },
        {
            'skill_id': '6.2',
            'skill_name': 'nd ending',
            'grapheme_patterns': 'nd',
            'phoneme_requirement': 'N D',
            'word_length': '4-5',
            'pattern_type': 'final_blend',
            'additional_constraints': 'end of word only'
        },
        {
            'skill_id': '6.3',
            'skill_name': 'nk ending',
            'grapheme_patterns': 'nk',
            'phoneme_requirement': 'NG K',
            'word_length': '4-5',
            'pattern_type': 'final_blend',
            'additional_constraints': 'end of word only'
        },
        {
            'skill_id': '6.4',
            'skill_name': 'lt ending',
            'grapheme_patterns': 'lt',
            'phoneme_requirement': 'L T',
            'word_length': '4-5',
            'pattern_type': 'final_blend',
            'additional_constraints': 'end of word only'
        },
        {
            'skill_id': '6.5',
            'skill_name': 'ld ending',
            'grapheme_patterns': 'ld',
            'phoneme_requirement': 'L D',
            'word_length': '4-5',
            'pattern_type': 'final_blend',
            'additional_constraints': 'end of word only'
        },
        {
            'skill_id': '6.6',
            'skill_name': 'lf ending',
            'grapheme_patterns': 'lf',
            'phoneme_requirement': 'L F',
            'word_length': '4-5',
            'pattern_type': 'final_blend',
            'additional_constraints': 'end of word only'
        },
        {
            'skill_id': '6.7',
            'skill_name': 'lk ending',
            'grapheme_patterns': 'lk',
            'phoneme_requirement': 'L K',
            'word_length': '4-5',
            'pattern_type': 'final_blend',
            'additional_constraints': 'end of word only'
        },
        {
            'skill_id': '6.8',
            'skill_name': 'lm ending',
            'grapheme_patterns': 'lm',
            'phoneme_requirement': 'L M',
            'word_length': '4-5',
            'pattern_type': 'final_blend',
            'additional_constraints': 'end of word only'
        },
        {
            'skill_id': '6.9',
            'skill_name': 'lp ending',
            'grapheme_patterns': 'lp',
            'phoneme_requirement': 'L P',
            'word_length': '4-5',
            'pattern_type': 'final_blend',
            'additional_constraints': 'end of word only'
        },
        {
            'skill_id': '6.10',
            'skill_name': 'mp ending',
            'grapheme_patterns': 'mp',
            'phoneme_requirement': 'M P',
            'word_length': '4-5',
            'pattern_type': 'final_blend',
            'additional_constraints': 'end of word only'
        },
        {
            'skill_id': '6.12',
            'skill_name': 'ng ending',
            'grapheme_patterns': 'ng',
            'phoneme_requirement': 'NG',
            'word_length': '4-5',
            'pattern_type': 'final_blend',
            'additional_constraints': 'end of word only'
        },
        {
            'skill_id': '6.13',
            'skill_name': 'sk ending',
            'grapheme_patterns': 'sk',
            'phoneme_requirement': 'S K',
            'word_length': '4-5',
            'pattern_type': 'final_blend',
            'additional_constraints': 'end of word only'
        },
        {
            'skill_id': '6.14',
            'skill_name': 'st ending',
            'grapheme_patterns': 'st',
            'phoneme_requirement': 'S T',
            'word_length': '4-5',
            'pattern_type': 'final_blend',
            'additional_constraints': 'end of word only'
        },
        {
            'skill_id': '6.15',
            'skill_name': 'ft ending',
            'grapheme_patterns': 'ft',
            'phoneme_requirement': 'F T',
            'word_length': '4-5',
            'pattern_type': 'final_blend',
            'additional_constraints': 'end of word only'
        },
    ])
    
    # Skills 7.0-10.0 - Special rules
    skill_configs.extend([
        {
            'skill_id': '7.0',
            'skill_name': 'FLOSS rule',
            'grapheme_patterns': 'ff, ll, ss, zz',
            'phoneme_requirement': 'double consonant sound',
            'word_length': '4-6',
            'pattern_type': 'floss',
            'additional_constraints': 'after short vowel'
        },
        {
            'skill_id': '8.0',
            'skill_name': 'Compound words',
            'grapheme_patterns': 'word+word',
            'phoneme_requirement': 'compound',
            'word_length': '6-10',
            'pattern_type': 'compound',
            'additional_constraints': 'two complete words'
        },
        {
            'skill_id': '9.0',
            'skill_name': 'Catch rule',
            'grapheme_patterns': 'tch',
            'phoneme_requirement': 'CH',
            'word_length': '5-6',
            'pattern_type': 'catch',
            'additional_constraints': 'after short vowel'
        },
        {
            'skill_id': '10.0',
            'skill_name': 'Bridge rule',
            'grapheme_patterns': 'dge',
            'phoneme_requirement': 'JH',
            'word_length': '5-6',
            'pattern_type': 'bridge',
            'additional_constraints': 'after short vowel'
        },
    ])
    
    # Skill 11.0 - Magic E
    skill_configs.append({
        'skill_id': '11.0',
        'skill_name': 'Magic E',
        'grapheme_patterns': 'VCe',
        'phoneme_requirement': 'long vowel',
        'word_length': '4-5',
        'pattern_type': 'magic_e',
        'additional_constraints': 'silent e makes vowel long'
    })
    
    # Skill 12.1 - Closed syllable exceptions
    skill_configs.append({
        'skill_id': '12.1',
        'skill_name': 'Closed syllable exceptions',
        'grapheme_patterns': 'all, alk, ald, old, ild, ind, ost, olt',
        'phoneme_requirement': 'irregular vowel sound',
        'word_length': '3-6',
        'pattern_type': 'rule_breaker',
        'additional_constraints': 'vowel sound different than expected'
    })
    
    # Skills 13.x - Soft sounds
    skill_configs.extend([
        {
            'skill_id': '13.1',
            'skill_name': 'Soft g',
            'grapheme_patterns': 'ge, gi, gy',
            'phoneme_requirement': 'JH',
            'word_length': '4-7',
            'pattern_type': 'soft_sound',
            'additional_constraints': 'g before e, i, y'
        },
        {
            'skill_id': '13.2',
            'skill_name': 'Soft c',
            'grapheme_patterns': 'ce, ci, cy',
            'phoneme_requirement': 'S',
            'word_length': '4-7',
            'pattern_type': 'soft_sound',
            'additional_constraints': 'c before e, i, y'
        },
    ])
    
    # Skill 14.1 - Y as vowel
    skill_configs.append({
        'skill_id': '14.1',
        'skill_name': 'Y as vowel',
        'grapheme_patterns': 'y (final), y (medial)',
        'phoneme_requirement': 'IY or AY or IH',
        'word_length': '3-6',
        'pattern_type': 'y_vowel',
        'additional_constraints': 'y at end or middle of word'
    })
    
    # Skills 15.x - Schwa
    skill_configs.extend([
        {
            'skill_id': '15.1',
            'skill_name': 'o says uh',
            'grapheme_patterns': 'o (unstressed)',
            'phoneme_requirement': 'AH (schwa)',
            'word_length': '4-8',
            'pattern_type': 'schwa',
            'additional_constraints': 'unstressed syllable'
        },
        {
            'skill_id': '15.2',
            'skill_name': 'a says uh',
            'grapheme_patterns': 'a (unstressed)',
            'phoneme_requirement': 'AH (schwa)',
            'word_length': '4-8',
            'pattern_type': 'schwa',
            'additional_constraints': 'unstressed syllable'
        },
        {
            'skill_id': '15.3',
            'skill_name': 'e says uh',
            'grapheme_patterns': 'e (unstressed)',
            'phoneme_requirement': 'AH (schwa)',
            'word_length': '4-8',
            'pattern_type': 'schwa',
            'additional_constraints': 'unstressed syllable'
        },
    ])
    
    # Skill 16.0 - Silent letters
    skill_configs.append({
        'skill_id': '16.0',
        'skill_name': 'Silent letters',
        'grapheme_patterns': 'kn, wr, mb, gh, gn, ps, rh',
        'phoneme_requirement': 'one letter silent',
        'word_length': '4-7',
        'pattern_type': 'silent',
        'additional_constraints': 'specific letter is not pronounced'
    })
    
    # Skill 17.0 - 3 sounds of ED
    skill_configs.append({
        'skill_id': '17.0',
        'skill_name': '3 sounds of ed',
        'grapheme_patterns': 'ed',
        'phoneme_requirement': 'T or D or IH D',
        'word_length': '4-8',
        'pattern_type': 'ed_sounds',
        'additional_constraints': 'depends on preceding sound'
    })
    
    # Skills 18.x - Vowel teams (CRITICAL FOR PHONEME MATCHING)
    skill_configs.extend([
        {
            'skill_id': '18.1',
            'skill_name': 'Long a teams',
            'grapheme_patterns': 'ai, ay, ei, eigh',
            'phoneme_requirement': 'EY (long a)',
            'word_length': '4-8',
            'pattern_type': 'vowel_team',
            'additional_constraints': 'must make long a sound'
        },
        {
            'skill_id': '18.2',
            'skill_name': 'Long e teams',
            'grapheme_patterns': 'ea, ee, ie, ey',
            'phoneme_requirement': 'IY (long e)',
            'word_length': '4-8',
            'pattern_type': 'vowel_team',
            'additional_constraints': 'must make long e sound'
        },
        {
            'skill_id': '18.3',
            'skill_name': 'Long o teams',
            'grapheme_patterns': 'oa, oe, ow',
            'phoneme_requirement': 'OW (long o)',
            'word_length': '4-8',
            'pattern_type': 'vowel_team',
            'additional_constraints': 'must make long o sound'
        },
        {
            'skill_id': '18.4',
            'skill_name': 'ow sound',
            'grapheme_patterns': 'ou, ow',
            'phoneme_requirement': 'AW (/ow/ as in cow)',
            'word_length': '4-8',
            'pattern_type': 'vowel_team',
            'additional_constraints': 'must make /ow/ sound'
        },
        {
            'skill_id': '18.5',
            'skill_name': 'Long i teams',
            'grapheme_patterns': 'ie, igh',
            'phoneme_requirement': 'AY (long i)',
            'word_length': '4-8',
            'pattern_type': 'vowel_team',
            'additional_constraints': 'must make long i sound'
        },
        {
            'skill_id': '18.6',
            'skill_name': 'Long u teams',
            'grapheme_patterns': 'ue, ew, eu',
            'phoneme_requirement': 'UW or YUW (long u)',
            'word_length': '4-8',
            'pattern_type': 'vowel_team',
            'additional_constraints': 'must make long u sound'
        },
        {
            'skill_id': '18.7',
            'skill_name': 'Long oo',
            'grapheme_patterns': 'oo',
            'phoneme_requirement': 'UW (long oo as in moon)',
            'word_length': '4-8',
            'pattern_type': 'vowel_team',
            'additional_constraints': 'must make long oo sound'
        },
        {
            'skill_id': '18.8',
            'skill_name': 'Short oo',
            'grapheme_patterns': 'oo',
            'phoneme_requirement': 'UH (short oo as in book)',
            'word_length': '4-8',
            'pattern_type': 'vowel_team',
            'additional_constraints': 'must make short oo sound'
        },
        {
            'skill_id': '18.9',
            'skill_name': 'ou says u',
            'grapheme_patterns': 'ou',
            'phoneme_requirement': 'AH (short u)',
            'word_length': '4-8',
            'pattern_type': 'vowel_team',
            'additional_constraints': 'ou making short u sound'
        },
        {
            'skill_id': '18.10',
            'skill_name': 'aw sound',
            'grapheme_patterns': 'au, aw, augh',
            'phoneme_requirement': 'AO (/aw/)',
            'word_length': '4-8',
            'pattern_type': 'vowel_team',
            'additional_constraints': 'must make /aw/ sound'
        },
        {
            'skill_id': '18.11',
            'skill_name': 'oy sound',
            'grapheme_patterns': 'oi, oy',
            'phoneme_requirement': 'OY (/oy/)',
            'word_length': '4-8',
            'pattern_type': 'vowel_team',
            'additional_constraints': 'must make /oy/ sound'
        },
        {
            'skill_id': '18.12',
            'skill_name': 'ea says e',
            'grapheme_patterns': 'ea',
            'phoneme_requirement': 'EH (short e)',
            'word_length': '4-8',
            'pattern_type': 'vowel_team',
            'additional_constraints': 'ea making short e sound'
        },
    ])
    
    # Skill 19.0 - Homophones
    skill_configs.append({
        'skill_id': '19.0',
        'skill_name': 'Homophones',
        'grapheme_patterns': 'various',
        'phoneme_requirement': 'same sound, different spelling',
        'word_length': '2-10',
        'pattern_type': 'homophones',
        'additional_constraints': 'words that sound the same'
    })
    
    # Skills 20.x - R-controlled
    skill_configs.extend([
        {
            'skill_id': '20.1',
            'skill_name': 'er sound',
            'grapheme_patterns': 'er, ir, ur',
            'phoneme_requirement': 'ER',
            'word_length': '3-10',
            'pattern_type': 'r_controlled',
            'additional_constraints': 'r-controlled vowel'
        },
        {
            'skill_id': '20.2',
            'skill_name': 'or sound',
            'grapheme_patterns': 'or, ore',
            'phoneme_requirement': 'AO R',
            'word_length': '3-10',
            'pattern_type': 'r_controlled',
            'additional_constraints': 'r-controlled vowel'
        },
        {
            'skill_id': '20.3',
            'skill_name': 'ar sound',
            'grapheme_patterns': 'ar',
            'phoneme_requirement': 'AA R',
            'word_length': '3-10',
            'pattern_type': 'r_controlled',
            'additional_constraints': 'r-controlled vowel'
        },
        {
            'skill_id': '20.4',
            'skill_name': 'war sound',
            'grapheme_patterns': 'war',
            'phoneme_requirement': 'AO R (sounds like or)',
            'word_length': '3-10',
            'pattern_type': 'r_controlled',
            'additional_constraints': 'w affects ar sound'
        },
        {
            'skill_id': '20.5',
            'skill_name': 'wor sound',
            'grapheme_patterns': 'wor',
            'phoneme_requirement': 'ER (sounds like er)',
            'word_length': '3-10',
            'pattern_type': 'r_controlled',
            'additional_constraints': 'w affects or sound'
        },
    ])
    
    # Skills 21.x - Syllabication
    skill_configs.extend([
        {
            'skill_id': '21.1',
            'skill_name': 'Compound words',
            'grapheme_patterns': 'word+word',
            'phoneme_requirement': 'compound',
            'word_length': '6-12',
            'pattern_type': 'syllable',
            'additional_constraints': 'two complete words'
        },
        {
            'skill_id': '21.2',
            'skill_name': 'Turtle words (C+le)',
            'grapheme_patterns': 'consonant+le',
            'phoneme_requirement': 'consonant + schwa + L',
            'word_length': '5-10',
            'pattern_type': 'c_le',
            'additional_constraints': 'consonant + le syllable'
        },
        {
            'skill_id': '21.5',
            'skill_name': 'Rabbit words (VCCV double)',
            'grapheme_patterns': 'double consonant between vowels',
            'phoneme_requirement': 'short vowel before double',
            'word_length': '5-10',
            'pattern_type': 'vccv_double',
            'additional_constraints': 'syllable split between doubles'
        },
    ])
    
    # Skills 22.0-30.0 - Morphology
    skill_configs.extend([
        {
            'skill_id': '22.0',
            'skill_name': 'Common prefixes',
            'grapheme_patterns': 'un-, re-, pre-, dis-, mis-, sub-, etc.',
            'phoneme_requirement': 'prefix sounds',
            'word_length': '5-15',
            'pattern_type': 'prefix',
            'additional_constraints': 'beginning of word'
        },
        {
            'skill_id': '23.1',
            'skill_name': 'Common suffixes',
            'grapheme_patterns': '-ing, -ed, -er, -est, -ly, -ness, -ful, -less',
            'phoneme_requirement': 'suffix sounds',
            'word_length': '5-15',
            'pattern_type': 'suffix',
            'additional_constraints': 'end of word'
        },
        {
            'skill_id': '23.2',
            'skill_name': 'Irregular suffixes',
            'grapheme_patterns': '-tion, -sion, -ture',
            'phoneme_requirement': 'SHUN, ZHUN, CHER',
            'word_length': '5-15',
            'pattern_type': 'suffix_irregular',
            'additional_constraints': 'special pronunciation'
        },
        {
            'skill_id': '24.0',
            'skill_name': '1-1-1 doubling',
            'grapheme_patterns': 'CVC + suffix',
            'phoneme_requirement': 'double final consonant',
            'word_length': '5-12',
            'pattern_type': '1_1_1_doubling',
            'additional_constraints': '1 syllable, 1 vowel, 1 final consonant'
        },
        {
            'skill_id': '25.0',
            'skill_name': 'E-dropping rule',
            'grapheme_patterns': 'VCe + vowel suffix',
            'phoneme_requirement': 'drop silent e',
            'word_length': '5-12',
            'pattern_type': 'e_drop',
            'additional_constraints': 'drop e before vowel suffix'
        },
        {
            'skill_id': '26.0',
            'skill_name': 'Change y to i',
            'grapheme_patterns': 'y + suffix',
            'phoneme_requirement': 'y changes to i',
            'word_length': '5-12',
            'pattern_type': 'y_to_i',
            'additional_constraints': 'y to i before suffix'
        },
        {
            'skill_id': '27.1',
            'skill_name': 'Plural -s',
            'grapheme_patterns': '-s',
            'phoneme_requirement': 'S or Z',
            'word_length': '3-12',
            'pattern_type': 'plural_s',
            'additional_constraints': 'regular plural'
        },
        {
            'skill_id': '27.2',
            'skill_name': 'Plural -es',
            'grapheme_patterns': '-es',
            'phoneme_requirement': 'IH Z',
            'word_length': '4-12',
            'pattern_type': 'plural_es',
            'additional_constraints': 'after s, x, z, ch, sh'
        },
        {
            'skill_id': '27.3',
            'skill_name': 'f to v plural',
            'grapheme_patterns': 'f/fe → ves',
            'phoneme_requirement': 'F to V change',
            'word_length': '4-12',
            'pattern_type': 'plural_f_to_v',
            'additional_constraints': 'knife→knives, leaf→leaves'
        },
        {
            'skill_id': '27.5',
            'skill_name': 'Irregular plurals',
            'grapheme_patterns': 'various',
            'phoneme_requirement': 'irregular',
            'word_length': '3-12',
            'pattern_type': 'plural_irregular',
            'additional_constraints': 'man→men, child→children'
        },
        {
            'skill_id': '28.0',
            'skill_name': '2-1-1 doubling',
            'grapheme_patterns': '2 syllable + suffix',
            'phoneme_requirement': 'double if stress on 2nd',
            'word_length': '6-15',
            'pattern_type': '2_1_1_doubling',
            'additional_constraints': 'stress on second syllable'
        },
        {
            'skill_id': '29.0',
            'skill_name': 'Silent e not plural',
            'grapheme_patterns': 'VCe (not plural)',
            'phoneme_requirement': 'silent e',
            'word_length': '4-10',
            'pattern_type': 'silent_e_not_plural',
            'additional_constraints': 'words ending in e that aren\'t plurals'
        },
        {
            'skill_id': '30.0',
            'skill_name': 'Contractions',
            'grapheme_patterns': 'apostrophe replaces letters',
            'phoneme_requirement': 'contracted sounds',
            'word_length': '3-10',
            'pattern_type': 'contractions',
            'additional_constraints': 'don\'t, can\'t, won\'t, etc.'
        },
    ])
    
    return skill_configs

def export_to_csv(skill_configs):
    """Export skill configurations to CSV"""
    # Create DataFrame
    df = pd.DataFrame(skill_configs)
    
    # Save to CSV
    filename = 'og_skill_pattern_configurations.csv'
    df.to_csv(filename, index=False)
    
    print(f"Exported {len(skill_configs)} skill configurations to {filename}")
    
    # Also create a JSON version for easy programmatic use
    json_filename = 'og_skill_pattern_configurations.json'
    with open(json_filename, 'w') as f:
        json.dump(skill_configs, f, indent=2)
    
    print(f"Also exported to {json_filename} for programmatic use")
    
    return df

def create_simplified_config():
    """Create a simplified version focusing on key vowel team issues"""
    vowel_team_configs = [
        # The critical vowel team configurations
        {
            'skill_id': '18.1',
            'patterns': ['ai', 'ay', 'ei', 'eigh'],
            'target_sound': 'long a',
            'phoneme': 'EY',
            'examples_correct': 'rain, day, vein, eight',
            'examples_wrong': 'said (short e)'
        },
        {
            'skill_id': '18.2',
            'patterns': ['ea', 'ee', 'ie', 'ey'],
            'target_sound': 'long e',
            'phoneme': 'IY',
            'examples_correct': 'seat, tree, thief, key',
            'examples_wrong': 'bread (short e), friend (short e)'
        },
        {
            'skill_id': '18.3',
            'patterns': ['oa', 'oe', 'ow'],
            'target_sound': 'long o',
            'phoneme': 'OW',
            'examples_correct': 'boat, toe, grow',
            'examples_wrong': 'cow (ow sound)'
        },
        {
            'skill_id': '18.4',
            'patterns': ['ou', 'ow'],
            'target_sound': '/ow/ as in cow',
            'phoneme': 'AW',
            'examples_correct': 'out, how',
            'examples_wrong': 'soul (long o), could (short u)'
        },
        {
            'skill_id': '18.5',
            'patterns': ['ie', 'igh'],
            'target_sound': 'long i',
            'phoneme': 'AY',
            'examples_correct': 'pie, light',
            'examples_wrong': 'field (long e), eight (long a)'
        },
        {
            'skill_id': '18.6',
            'patterns': ['ue', 'ew', 'eu'],
            'target_sound': 'long u',
            'phoneme': 'UW or YUW',
            'examples_correct': 'blue, new, feud',
            'examples_wrong': 'none common'
        },
        {
            'skill_id': '18.7',
            'patterns': ['oo'],
            'target_sound': 'long oo',
            'phoneme': 'UW',
            'examples_correct': 'moon, food',
            'examples_wrong': 'book (short oo)'
        },
        {
            'skill_id': '18.8',
            'patterns': ['oo'],
            'target_sound': 'short oo',
            'phoneme': 'UH',
            'examples_correct': 'book, good',
            'examples_wrong': 'moon (long oo)'
        },
    ]
    
    # Save as simplified CSV
    df = pd.DataFrame(vowel_team_configs)
    df.to_csv('vowel_team_phoneme_requirements.csv', index=False)
    print("\nAlso created vowel_team_phoneme_requirements.csv for quick reference")

def main():
    """Extract and export all skill configurations"""
    print("EXTRACTING O-G SKILL PATTERN CONFIGURATIONS")
    print("=" * 70)
    
    # Extract all configurations
    skill_configs = extract_skill_configurations()
    
    # Export to CSV
    df = export_to_csv(skill_configs)
    
    # Create simplified vowel team reference
    create_simplified_config()
    
    # Print summary
    print(f"\nTotal skills configured: {len(skill_configs)}")
    print("\nColumns in CSV:")
    print("- skill_id: Sub-skill identifier")
    print("- skill_name: Name of the skill")
    print("- grapheme_patterns: Spelling patterns for this skill")
    print("- phoneme_requirement: Required sound(s)")
    print("- word_length: Appropriate word length range")
    print("- pattern_type: Type of pattern")
    print("- additional_constraints: Other requirements")
    
    print("\n✓ You can now edit these CSVs to adjust any requirements!")

if __name__ == "__main__":
    main()
