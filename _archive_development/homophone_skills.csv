skill_id,skill_name,pattern_type,graphemes,required_phonemes,phoneme_description,position_constraints,word_structure,special_rules,examples,non_examples,notes
19.1,Homophones - silent letters,homophones,kn/n; wr/r; wh/w; mb/m; gn/n; ps/s,matching pairs,silent letter creates same sound as single letter,any position,any,One spelling has silent letter that makes it sound like simpler spelling,"know/no, knight/night, write/right, whole/hole, lamb/lam, gnome/nome, psalm/salm","now (not a homophone), knit (no homophone pair)",Silent letters learned in skill 16.0 can create homophones
19.2,Homophones - vowel team variants,homophones,ai/a-e/ay; ee/ea/e-e; oa/o-e/ow; ew/oo/ue; au/aw/a; or/ore/oar,matching pairs,different vowel patterns making same sound,varies by pattern,any,Different spelling patterns for same vowel sound create homophones,"mail/male, rain/reign/rein, meet/meat/mete, road/rode/rowed, new/knew/gnu, paws/pause, for/four/fore","main/man (different sounds), beat/bet (different sounds)","Requires knowledge of multiple vowel teams (skills 18.1-18.12, 11.0)"
19.3,Homophones - grammar/function words,homophones,to/too/two; there/their/they're; your/you're; its/it's; for/four; by/buy/bye; our/hour,matching pairs,same sound but different meaning/function,any,any,Common words with different meanings/parts of speech that sound identical,"to/too/two, there/their/they're, your/you're, its/it's, by/buy/bye, our/hour, I/eye, we/wee","then/than (near-homophones), of/off (different sounds)",These are high-frequency words that often need explicit teaching