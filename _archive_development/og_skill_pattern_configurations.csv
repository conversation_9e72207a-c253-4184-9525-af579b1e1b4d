skill_id,skill_name,grapheme_patterns,phoneme_requirement,word_length,pattern_type,additional_constraints
1.1,CVC short a,single vowel: a,AE (short a),3,CVC,"no blends, no digraphs"
1.2,CVC short e,single vowel: e,EH (short e),3,CVC,"no blends, no digraphs"
1.3,CVC short i,single vowel: i,IH (short i),3,CVC,"no blends, no digraphs"
1.4,CVC short o,single vowel: o,AA (short o),3,CVC,"no blends, no digraphs"
1.5,CVC short u,single vowel: u,AH (short u),3,CVC,"no blends, no digraphs"
2.1,CV long e,"specific words: be, he, me, we, she",IY (long e),2-3,CV,specific word list only
2.2,CV long o,"specific words: go, no, so",OW (long o),2,CV,specific word list only
2.3,CV long i,"specific words: I, hi",AY (long i),1-2,CV,specific word list only
3.1,c before a o u,"c+a, c+o, c+u",K,3-4,c_or_k,"c followed by a, o, or u"
3.2,k before e i y,"k+e, k+i, k+y",K,3-4,c_or_k,"k followed by e, i, or y"
4.1,ch digraph,ch,CH,3-4,digraph,none
4.2,sh digraph,sh,SH,3-4,digraph,none
4.3,th digraph,th,TH or DH,3-4,digraph,can be voiced or unvoiced
4.4,wh digraph,wh,W or HW,3-4,digraph,regional variation in pronunciation
4.5,ck digraph,ck,K,3-4,digraph,follows short vowel
5.1,l-blends,"bl, cl, fl, gl, pl, sl",consonant + L,4-5,initial_blend,beginning of word only
5.2,r-blends,"br, cr, dr, fr, gr, pr, tr",consonant + R,4-5,initial_blend,beginning of word only
5.3,s-blends,"sc, sk, sm, sn, sp, st, sw",S + consonant,4-5,initial_blend,beginning of word only
5.4,3-letter blends,"scr, spr, str, spl, squ, thr",3 consonant sounds,5-6,initial_blend,beginning of word only
6.1,nt ending,nt,N T,4-5,final_blend,end of word only
6.2,nd ending,nd,N D,4-5,final_blend,end of word only
6.3,nk ending,nk,NG K,4-5,final_blend,end of word only
6.4,lt ending,lt,L T,4-5,final_blend,end of word only
6.5,ld ending,ld,L D,4-5,final_blend,end of word only
6.6,lf ending,lf,L F,4-5,final_blend,end of word only
6.7,lk ending,lk,L K,4-5,final_blend,end of word only
6.8,lm ending,lm,L M,4-5,final_blend,end of word only
6.9,lp ending,lp,L P,4-5,final_blend,end of word only
6.10,mp ending,mp,M P,4-5,final_blend,end of word only
6.12,ng ending,ng,NG,4-5,final_blend,end of word only
6.13,sk ending,sk,S K,4-5,final_blend,end of word only
6.14,st ending,st,S T,4-5,final_blend,end of word only
6.15,ft ending,ft,F T,4-5,final_blend,end of word only
7.0,FLOSS rule,"ff, ll, ss, zz",double consonant sound,4-6,floss,after short vowel
8.0,Compound words,word+word,compound,6-10,compound,two complete words
9.0,Catch rule,tch,CH,5-6,catch,after short vowel
10.0,Bridge rule,dge,JH,5-6,bridge,after short vowel
11.0,Magic E,VCe,long vowel,4-5,magic_e,silent e makes vowel long
12.1,Closed syllable exceptions,"all, alk, ald, old, ild, ind, ost, olt",irregular vowel sound,3-6,rule_breaker,vowel sound different than expected
13.1,Soft g,"ge, gi, gy",JH,4-7,soft_sound,"g before e, i, y"
13.2,Soft c,"ce, ci, cy",S,4-7,soft_sound,"c before e, i, y"
14.1,Y as vowel,"y (final), y (medial)",IY or AY or IH,3-6,y_vowel,y at end or middle of word
15.1,o says uh,o (unstressed),AH (schwa),4-8,schwa,unstressed syllable
15.2,a says uh,a (unstressed),AH (schwa),4-8,schwa,unstressed syllable
15.3,e says uh,e (unstressed),AH (schwa),4-8,schwa,unstressed syllable
16.0,Silent letters,"kn, wr, mb, gh, gn, ps, rh",one letter silent,4-7,silent,specific letter is not pronounced
17.0,3 sounds of ed,ed,T or D or IH D,4-8,ed_sounds,depends on preceding sound
18.1,Long a teams,"ai, ay, ei, eigh",EY (long a),4-8,vowel_team,must make long a sound
18.2,Long e teams,"ea, ee, ie, ey",IY (long e),4-8,vowel_team,must make long e sound
18.3,Long o teams,"oa, oe, ow",OW (long o),4-8,vowel_team,must make long o sound
18.4,ow sound,"ou, ow",AW (/ow/ as in cow),4-8,vowel_team,must make /ow/ sound
18.5,Long i teams,"ie, igh",AY (long i),4-8,vowel_team,must make long i sound
18.6,Long u teams,"ue, ew, eu",UW or YUW (long u),4-8,vowel_team,must make long u sound
18.7,Long oo,oo,UW (long oo as in moon),4-8,vowel_team,must make long oo sound
18.8,Short oo,oo,UH (short oo as in book),4-8,vowel_team,must make short oo sound
18.9,ou says u,ou,AH (short u),4-8,vowel_team,ou making short u sound
18.10,aw sound,"au, aw, augh",AO (/aw/),4-8,vowel_team,must make /aw/ sound
18.11,oy sound,"oi, oy",OY (/oy/),4-8,vowel_team,must make /oy/ sound
18.12,ea says e,ea,EH (short e),4-8,vowel_team,ea making short e sound
19.0,Homophones,various,"same sound, different spelling",2-10,homophones,words that sound the same
20.1,er sound,"er, ir, ur",ER,3-10,r_controlled,r-controlled vowel
20.2,or sound,"or, ore",AO R,3-10,r_controlled,r-controlled vowel
20.3,ar sound,ar,AA R,3-10,r_controlled,r-controlled vowel
20.4,war sound,war,AO R (sounds like or),3-10,r_controlled,w affects ar sound
20.5,wor sound,wor,ER (sounds like er),3-10,r_controlled,w affects or sound
21.1,Compound words,word+word,compound,6-12,syllable,two complete words
21.2,Turtle words (C+le),consonant+le,consonant + schwa + L,5-10,c_le,consonant + le syllable
21.5,Rabbit words (VCCV double),double consonant between vowels,short vowel before double,5-10,vccv_double,syllable split between doubles
22.0,Common prefixes,"un-, re-, pre-, dis-, mis-, sub-, etc.",prefix sounds,5-15,prefix,beginning of word
23.1,Common suffixes,"-ing, -ed, -er, -est, -ly, -ness, -ful, -less",suffix sounds,5-15,suffix,end of word
23.2,Irregular suffixes,"-tion, -sion, -ture","SHUN, ZHUN, CHER",5-15,suffix_irregular,special pronunciation
24.0,1-1-1 doubling,CVC + suffix,double final consonant,5-12,1_1_1_doubling,"1 syllable, 1 vowel, 1 final consonant"
25.0,E-dropping rule,VCe + vowel suffix,drop silent e,5-12,e_drop,drop e before vowel suffix
26.0,Change y to i,y + suffix,y changes to i,5-12,y_to_i,y to i before suffix
27.1,Plural -s,-s,S or Z,3-12,plural_s,regular plural
27.2,Plural -es,-es,IH Z,4-12,plural_es,"after s, x, z, ch, sh"
27.3,f to v plural,f/fe → ves,F to V change,4-12,plural_f_to_v,"knife→knives, leaf→leaves"
27.5,Irregular plurals,various,irregular,3-12,plural_irregular,"man→men, child→children"
28.0,2-1-1 doubling,2 syllable + suffix,double if stress on 2nd,6-15,2_1_1_doubling,stress on second syllable
29.0,Silent e not plural,VCe (not plural),silent e,4-10,silent_e_not_plural,words ending in e that aren't plurals
30.0,Contractions,apostrophe replaces letters,contracted sounds,3-10,contractions,"don't, can't, won't, etc."