#!/usr/bin/env python3
"""
Orton-Gillingham Word List Generator
Generates appropriate word lists for all 30 O-G skills with:
- Word length constraints by skill level
- Inappropriate word filtering
- Frequency-based sorting
- CSV export functionality
"""

import nltk
import pandas as pd
import re
import os
import json
from collections import defaultdict
from datetime import datetime

# First-time setup (uncomment and run once)
# nltk.download('cmudict')
# nltk.download('brown')
# nltk.download('words')

class OrtonGillinghamCompleteGenerator:
    def __init__(self):
        print("Initializing Orton-Gillingham Word Generator...")
        
        # Load CMU Pronouncing Dictionary
        self.cmu_dict = nltk.corpus.cmudict.dict()
        
        # Load word frequency data
        self.freq_dist = self._load_frequency_data()
        
        # Define high-frequency words (top 500 most common)
        self.high_freq_words = self._get_high_frequency_words()
        
        # Load inappropriate word filters
        self.inappropriate_words = self._load_inappropriate_words()
        self.mild_concerns = self._load_mild_concerns()
        
        # Define word length constraints by skill
        self.word_length_limits = self._define_length_limits()
        
        # Complete phoneme mappings for all 30 skills
        self.skill_mappings = self._load_all_skill_mappings()
        
        print("Initialization complete!")
    
    def _load_frequency_data(self):
        """Load word frequency from Brown corpus"""
        brown_words = nltk.corpus.brown.words()
        return nltk.FreqDist(word.lower() for word in brown_words)
    
    def _get_high_frequency_words(self, top_n=500):
        """Get the most frequent words"""
        return set([word for word, _ in self.freq_dist.most_common(top_n)])
    
    def _load_inappropriate_words(self):
        """Load list of inappropriate words to exclude"""
        inappropriate = {
            # Profanity and slurs
            'damn', 'hell', 'crap', 'piss', 'bastard', 'ass',
            # Body parts that might be awkward in elementary context
            'butt', 'boob', 'poop', 'fart', 'pee', 'penis', 'vagina',
            # Drug/alcohol related
            'beer', 'wine', 'drug', 'weed', 'pot', 'drunk', 'booze',
            # Violence-related that might be concerning
            'kill', 'stab', 'gun', 'bomb', 'dead', 'die', 'murder',
            # Other potentially problematic words
            'sexy', 'stupid', 'dumb', 'idiot', 'hate', 'suck',
            # Archaic/outdated terms that might be offensive
            'queer', 'spook', 'gyp', 'retard'
        }
        
        # Add common variations
        variations = set()
        for word in inappropriate:
            variations.add(word + 's')  # plurals
            variations.add(word + 'ed')  # past tense
            variations.add(word + 'ing')  # gerunds
            
        return inappropriate.union(variations)
    
    def _load_mild_concerns(self):
        """Words that might need context or teacher discretion"""
        return {
            # Potentially sensitive topics
            'fat', 'ugly', 'poor', 'rich', 'old', 'dumb',
            # Religious terms (may want to avoid in public schools)
            'god', 'lord', 'pray', 'sin', 'hell', 'devil',
            # Political/controversial
            'vote', 'tax', 'war', 'fight',
            # Body/health topics that need age-appropriate context
            'sick', 'hurt', 'pain', 'blood', 'cut',
            # Scary for younger kids
            'ghost', 'witch', 'devil', 'demon', 'scary'
        }
    
    def _define_length_limits(self):
        """Define appropriate word length limits for each skill"""
        return {
            # CVC words - exactly 3 letters
            '1.1': {'min': 3, 'max': 3},  # CVC short a
            '1.2': {'min': 3, 'max': 3},  # CVC short e
            '1.3': {'min': 3, 'max': 3},  # CVC short i
            '1.4': {'min': 3, 'max': 3},  # CVC short o
            '1.5': {'min': 3, 'max': 3},  # CVC short u
            
            # CV words - exactly 2 letters
            '2.1': {'min': 2, 'max': 2},  # CV long e
            '2.2': {'min': 2, 'max': 2},  # CV long o
            '2.3': {'min': 2, 'max': 2},  # CV long i
            
            # C or K spelling - 3-4 letters for early practice
            '3.1': {'min': 3, 'max': 4},  # c before a,o,u
            '3.2': {'min': 3, 'max': 4},  # k before e,i,y
            
            # Digraphs - 3-4 letters
            '4.1': {'min': 3, 'max': 4},  # ch digraph
            '4.2': {'min': 3, 'max': 4},  # sh digraph
            '4.3': {'min': 3, 'max': 4},  # th digraph
            '4.4': {'min': 3, 'max': 4},  # wh digraph
            '4.5': {'min': 3, 'max': 4},  # ck digraph
            
            # Initial blends - 4-5 letters
            '5.1': {'min': 4, 'max': 5},  # l-blends
            '5.2': {'min': 4, 'max': 5},  # r-blends
            '5.3': {'min': 4, 'max': 5},  # s-blends
            '5.4': {'min': 5, 'max': 6},  # 3-letter blends
            
            # Final blends - 4-5 letters
            '6.1': {'min': 4, 'max': 5},  # nt ending
            '6.2': {'min': 4, 'max': 5},  # nd ending
            '6.3': {'min': 4, 'max': 5},  # nk ending
            '6.4': {'min': 4, 'max': 5},  # lt ending
            '6.5': {'min': 4, 'max': 5},  # ld ending
            '6.6': {'min': 4, 'max': 5},  # lf ending
            '6.7': {'min': 4, 'max': 5},  # lk ending
            '6.8': {'min': 4, 'max': 5},  # lm ending
            '6.9': {'min': 4, 'max': 5},  # lp ending
            '6.10': {'min': 4, 'max': 5}, # mp ending
            '6.11': {'min': 4, 'max': 5}, # nd ending
            '6.12': {'min': 4, 'max': 5}, # ng ending
            '6.13': {'min': 4, 'max': 5}, # sk ending
            '6.14': {'min': 4, 'max': 5}, # st ending
            '6.15': {'min': 4, 'max': 5}, # ft ending
            
            # Special rules
            '7.0': {'min': 4, 'max': 6},  # FLOSS rule
            '8.0': {'min': 6, 'max': 10}, # Compound words
            '9.0': {'min': 5, 'max': 6},  # Catch rule
            '10.0': {'min': 5, 'max': 6}, # Bridge rule
            
            # Magic E
            '11.0': {'min': 4, 'max': 5}, # Magic E
            
            # More advanced patterns
            '12.1': {'min': 3, 'max': 6}, # Closed syllable exceptions
            '13.1': {'min': 4, 'max': 7}, # Soft g
            '13.2': {'min': 4, 'max': 7}, # Soft c
            '14.1': {'min': 3, 'max': 6}, # Y as vowel
            
            # Schwa
            '15.1': {'min': 4, 'max': 8}, # o says uh
            '15.2': {'min': 4, 'max': 8}, # a says uh
            '15.3': {'min': 4, 'max': 8}, # e says uh
            
            # Silent letters
            '16.0': {'min': 4, 'max': 7}, # Silent letters
            
            # -ED endings
            '17.0': {'min': 4, 'max': 8}, # 3 sounds of ed
            
            # Vowel teams
            '18.1': {'min': 4, 'max': 8}, # Long a teams
            '18.2': {'min': 4, 'max': 8}, # Long e teams
            '18.3': {'min': 4, 'max': 8}, # Long o teams
            '18.4': {'min': 4, 'max': 8}, # ow sound
            '18.5': {'min': 4, 'max': 8}, # Long i teams
            '18.6': {'min': 4, 'max': 8}, # Long u teams
            '18.7': {'min': 4, 'max': 8}, # Long oo
            '18.8': {'min': 4, 'max': 8}, # Short oo
            '18.9': {'min': 4, 'max': 8}, # ou says u
            '18.10': {'min': 4, 'max': 8}, # aw sound
            '18.11': {'min': 4, 'max': 8}, # oy sound
            '18.12': {'min': 4, 'max': 8}, # ea says e
            
            # Advanced patterns
            '19.0': {'min': 2, 'max': 10}, # Homophones
            '20.1': {'min': 3, 'max': 10}, # er/ir/ur
            '20.2': {'min': 3, 'max': 10}, # or
            '20.3': {'min': 3, 'max': 10}, # ar
            '20.4': {'min': 3, 'max': 10}, # war
            '20.5': {'min': 3, 'max': 10}, # wor
            
            # Syllabication
            '21.1': {'min': 6, 'max': 12}, # Compound words
            '21.2': {'min': 5, 'max': 10}, # C+le words
            '21.3': {'min': 5, 'max': 12}, # Prefix/suffix
            '21.4': {'min': 5, 'max': 10}, # VCCV different
            '21.5': {'min': 5, 'max': 10}, # VCCV double
            '21.6': {'min': 5, 'max': 10}, # VC/V
            '21.7': {'min': 5, 'max': 10}, # V/CV
            '21.8': {'min': 4, 'max': 10}, # VV
            
            # Morphology
            '22.0': {'min': 5, 'max': 15}, # Prefixes
            '23.1': {'min': 5, 'max': 15}, # Suffixes
            '23.2': {'min': 5, 'max': 15}, # Irregular suffixes
            '24.0': {'min': 5, 'max': 12}, # 1-1-1 doubling
            '25.0': {'min': 5, 'max': 12}, # E-dropping
            '26.0': {'min': 5, 'max': 12}, # Y to i
            '27.1': {'min': 3, 'max': 12}, # Plural -s
            '27.2': {'min': 4, 'max': 12}, # Plural -es
            '27.3': {'min': 4, 'max': 12}, # f to v plural
            '27.4': {'min': 4, 'max': 12}, # o plurals
            '27.5': {'min': 3, 'max': 12}, # Irregular plurals
            '28.0': {'min': 6, 'max': 15}, # 2-1-1 doubling
            '29.0': {'min': 4, 'max': 10}, # Silent e not plural
            '30.0': {'min': 3, 'max': 10}, # Contractions
        }
    
    def _load_all_skill_mappings(self):
        """Define all skill patterns and their phoneme mappings"""
        return {
            # 1. CVC WORDS
            '1.1': {'type': 'cvc', 'vowel': 'AE', 'name': 'CVC short a'},
            '1.2': {'type': 'cvc', 'vowel': 'EH', 'name': 'CVC short e'},
            '1.3': {'type': 'cvc', 'vowel': 'IH', 'name': 'CVC short i'},
            '1.4': {'type': 'cvc', 'vowel': 'AA', 'name': 'CVC short o'},
            '1.5': {'type': 'cvc', 'vowel': 'AH', 'name': 'CVC short u'},
            
            # 2. CV WORDS (Open Syllables)
            '2.1': {'type': 'cv', 'vowel': 'IY', 'name': 'CV long e'},
            '2.2': {'type': 'cv', 'vowel': 'OW', 'name': 'CV long o'},
            '2.3': {'type': 'cv', 'vowel': 'AY', 'name': 'CV long i'},
            
            # 3. C OR K SPELLING RULE
            '3.1': {'type': 'c_or_k', 'rule': 'c_before_aou', 'name': 'c before a,o,u'},
            '3.2': {'type': 'c_or_k', 'rule': 'k_before_eiy', 'name': 'k before e,i,y'},
            
            # 4. DIGRAPHS
            '4.1': {'type': 'digraph', 'pattern': 'ch', 'sound': 'CH', 'name': 'ch digraph'},
            '4.2': {'type': 'digraph', 'pattern': 'sh', 'sound': 'SH', 'name': 'sh digraph'},
            '4.3': {'type': 'digraph', 'pattern': 'th', 'sound': ['TH', 'DH'], 'name': 'th digraph'},
            '4.4': {'type': 'digraph', 'pattern': 'wh', 'sound': 'W', 'name': 'wh digraph'},
            '4.5': {'type': 'digraph', 'pattern': 'ck', 'sound': 'K', 'name': 'ck digraph'},
            
            # 5. INITIAL BLENDS
            '5.1': {'type': 'initial_blend', 'patterns': ['bl', 'cl', 'fl', 'gl', 'pl', 'sl'], 'name': 'l-blends'},
            '5.2': {'type': 'initial_blend', 'patterns': ['br', 'cr', 'dr', 'fr', 'gr', 'pr', 'tr'], 'name': 'r-blends'},
            '5.3': {'type': 'initial_blend', 'patterns': ['sc', 'sk', 'sm', 'sn', 'sp', 'st', 'sw'], 'name': 's-blends'},
            '5.4': {'type': 'initial_blend', 'patterns': ['scr', 'spr', 'str', 'spl', 'squ', 'thr'], 'name': '3-letter blends'},
            
            # 6. FINAL BLENDS
            '6.1': {'type': 'final_blend', 'pattern': 'nt', 'name': 'nt ending'},
            '6.2': {'type': 'final_blend', 'pattern': 'nd', 'name': 'nd ending'},
            '6.3': {'type': 'final_blend', 'pattern': 'nk', 'name': 'nk ending'},
            '6.4': {'type': 'final_blend', 'pattern': 'lt', 'name': 'lt ending'},
            '6.5': {'type': 'final_blend', 'pattern': 'ld', 'name': 'ld ending'},
            '6.6': {'type': 'final_blend', 'pattern': 'lf', 'name': 'lf ending'},
            '6.7': {'type': 'final_blend', 'pattern': 'lk', 'name': 'lk ending'},
            '6.8': {'type': 'final_blend', 'pattern': 'lm', 'name': 'lm ending'},
            '6.9': {'type': 'final_blend', 'pattern': 'lp', 'name': 'lp ending'},
            '6.10': {'type': 'final_blend', 'pattern': 'mp', 'name': 'mp ending'},
            '6.11': {'type': 'final_blend', 'pattern': 'nd', 'name': 'nd ending'},
            '6.12': {'type': 'final_blend', 'pattern': 'ng', 'name': 'ng ending'},
            '6.13': {'type': 'final_blend', 'pattern': 'sk', 'name': 'sk ending'},
            '6.14': {'type': 'final_blend', 'pattern': 'st', 'name': 'st ending'},
            '6.15': {'type': 'final_blend', 'pattern': 'ft', 'name': 'ft ending'},
            
            # 7-10. SPECIAL RULES
            '7.0': {'type': 'floss', 'name': 'FLOSS rule'},
            '8.0': {'type': 'compound', 'name': 'Backpack rule'},
            '9.0': {'type': 'catch', 'name': 'Catch rule'},
            '10.0': {'type': 'bridge', 'name': 'Bridge rule'},
            
            # 11. MAGIC E
            '11.0': {'type': 'magic_e', 'name': 'Magic E'},
            
            # 12. CLOSED SYLLABLE RULE BREAKERS
            '12.1': {'type': 'rule_breaker', 'name': 'Closed syllable exceptions'},
            
            # 13. SOFT SOUNDS
            '13.1': {'type': 'soft_sound', 'letter': 'g', 'name': 'Soft g'},
            '13.2': {'type': 'soft_sound', 'letter': 'c', 'name': 'Soft c'},
            
            # 14. Y AS VOWEL
            '14.1': {'type': 'y_vowel', 'name': 'Y as vowel'},
            
            # 15. SCHWA
            '15.1': {'type': 'schwa', 'vowel': 'o', 'name': 'o says uh'},
            '15.2': {'type': 'schwa', 'vowel': 'a', 'name': 'a says uh'},
            '15.3': {'type': 'schwa', 'vowel': 'e', 'name': 'e says uh'},
            
            # 16. SILENT LETTERS
            '16.0': {'type': 'silent', 'name': 'Silent letters'},
            
            # 17. 3 SOUNDS OF ED
            '17.0': {'type': 'ed_sounds', 'name': '3 sounds of ed'},
            
            # 18. VOWEL TEAMS
            '18.1': {'type': 'vowel_team', 'patterns': ['ai', 'ay', 'ey', 'eigh', 'ea', 'ei'], 'sound': 'long a', 'name': 'Long a teams'},
            '18.2': {'type': 'vowel_team', 'patterns': ['ee', 'ea', 'ie', 'ey', 'ei'], 'sound': 'long e', 'name': 'Long e teams'},
            '18.3': {'type': 'vowel_team', 'patterns': ['oa', 'oe', 'ow', 'ou'], 'sound': 'long o', 'name': 'Long o teams'},
            '18.4': {'type': 'vowel_team', 'patterns': ['ou', 'ow'], 'sound': 'ow', 'name': 'ow sound'},
            '18.5': {'type': 'vowel_team', 'patterns': ['ie', 'igh', 'ei', 'uy'], 'sound': 'long i', 'name': 'Long i teams'},
            '18.6': {'type': 'vowel_team', 'patterns': ['ue', 'ew', 'eu'], 'sound': 'long u', 'name': 'Long u teams'},
            '18.7': {'type': 'vowel_team', 'patterns': ['oo', 'ue', 'ew', 'ui', 'eu', 'ou'], 'sound': 'long oo', 'name': 'Long oo'},
            '18.8': {'type': 'vowel_team', 'patterns': ['oo'], 'sound': 'short oo', 'name': 'Short oo'},
            '18.9': {'type': 'vowel_team', 'patterns': ['ou'], 'sound': 'short u', 'name': 'ou says u'},
            '18.10': {'type': 'vowel_team', 'patterns': ['au', 'aw'], 'sound': 'aw', 'name': 'aw sound'},
            '18.11': {'type': 'vowel_team', 'patterns': ['oi', 'oy'], 'sound': 'oy', 'name': 'oy sound'},
            '18.12': {'type': 'vowel_team', 'patterns': ['ea'], 'sound': 'short e', 'name': 'ea says e'},
            
            # 19. HOMOPHONES
            '19.0': {'type': 'homophones', 'name': 'Homophones'},
            
            # 20. R-CONTROLLED
            '20.1': {'type': 'r_controlled', 'patterns': ['er', 'ir', 'ur'], 'sound': 'er', 'name': 'er sound'},
            '20.2': {'type': 'r_controlled', 'pattern': 'or', 'sound': 'or', 'name': 'or sound'},
            '20.3': {'type': 'r_controlled', 'pattern': 'ar', 'sound': 'ar', 'name': 'ar sound'},
            '20.4': {'type': 'r_controlled', 'pattern': 'war', 'sound': 'wor', 'name': 'war sound'},
            '20.5': {'type': 'r_controlled', 'pattern': 'wor', 'sound': 'wer', 'name': 'wor sound'},
            
            # 21. SYLLABICATION
            '21.1': {'type': 'syllable', 'pattern': 'compound', 'name': 'Compound words'},
            '21.2': {'type': 'syllable', 'pattern': 'c_le', 'name': 'Turtle words'},
            '21.3': {'type': 'syllable', 'pattern': 'prefix_suffix', 'name': 'Retriever words'},
            '21.4': {'type': 'syllable', 'pattern': 'vccv_different', 'name': 'Ostrich words'},
            '21.5': {'type': 'syllable', 'pattern': 'vccv_double', 'name': 'Rabbit words'},
            '21.6': {'type': 'syllable', 'pattern': 'vcv_short', 'name': 'Tiger words'},
            '21.7': {'type': 'syllable', 'pattern': 'vcv_long', 'name': 'Camel words'},
            '21.8': {'type': 'syllable', 'pattern': 'vv', 'name': 'Lion words'},
            
            # 22-30. MORPHOLOGY & ADVANCED
            '22.0': {'type': 'prefix', 'name': 'Common prefixes'},
            '23.1': {'type': 'suffix', 'name': 'Common suffixes'},
            '23.2': {'type': 'suffix', 'name': 'Irregular suffixes'},
            '24.0': {'type': '1_1_1_doubling', 'name': '1-1-1 doubling'},
            '25.0': {'type': 'e_drop', 'name': 'E-dropping rule'},
            '26.0': {'type': 'y_to_i', 'name': 'Change y to i'},
            '27.1': {'type': 'plural', 'rule': 's', 'name': 'Plural -s'},
            '27.2': {'type': 'plural', 'rule': 'es', 'name': 'Plural -es'},
            '27.3': {'type': 'plural', 'rule': 'f_to_v', 'name': 'f to v plural'},
            '27.4': {'type': 'plural', 'rule': 'o_plural', 'name': 'o plurals'},
            '27.5': {'type': 'plural', 'rule': 'irregular', 'name': 'Irregular plurals'},
            '28.0': {'type': '2_1_1_doubling', 'name': '2-1-1 doubling'},
            '29.0': {'type': 'silent_e_not_plural', 'name': 'Silent e not plural'},
            '30.0': {'type': 'contractions', 'name': 'Contractions'}
        }
    
    def is_appropriate(self, word, strict=True):
        """Check if a word is appropriate for K-6 education"""
        word_lower = word.lower()
        
        # Always exclude clearly inappropriate words
        if word_lower in self.inappropriate_words:
            return False
        
        # Check for inappropriate substrings
        inappropriate_substrings = ['sex', 'xxx', 'ass', 'tit', 'fag']
        for substring in inappropriate_substrings:
            if substring in word_lower and len(substring) >= 3:
                # Allow words like 'class', 'pass', 'title'
                if not self._is_safe_substring_usage(word_lower, substring):
                    return False
        
        # In strict mode, also exclude mild concerns
        if strict and word_lower in self.mild_concerns:
            return False
        
        # Check for number/letter combinations that look like leet speak
        if any(char.isdigit() for char in word):
            return False
            
        return True
    
    def _is_safe_substring_usage(self, word, substring):
        """Check if a substring appears in a safe context"""
        safe_words = {
            'ass': ['class', 'pass', 'grass', 'glass', 'mass', 'bass', 
                   'brass', 'compass', 'classify', 'assault', 'assure'],
            'sex': ['sixteen', 'sixty'],
            'tit': ['title', 'tittle', 'petition', 'competition', 'altitude',
                   'attitude', 'gratitude', 'institute', 'substitute'],
            'fag': ['faggot']  # the bundle of sticks meaning
        }
        
        return word in safe_words.get(substring, [])
    
    def apply_length_filter(self, words, skill_id):
        """Filter words based on skill-appropriate length"""
        if skill_id not in self.word_length_limits:
            return words
        
        limits = self.word_length_limits[skill_id]
        min_length = limits['min']
        max_length = limits['max']
        
        filtered_words = [
            w for w in words 
            if min_length <= len(w['word']) <= max_length
        ]
        
        removed_count = len(words) - len(filtered_words)
        if removed_count > 0:
            print(f"  Removed {removed_count} words outside length range {min_length}-{max_length}")
        
        return filtered_words
    
    def filter_words(self, words, skill_id, max_words=50, exclude_rare=True, 
                     strict_appropriate=True):
        """Enhanced filter with length and appropriateness checks"""
        # Apply length filter FIRST
        words = self.apply_length_filter(words, skill_id)
        
        # Then appropriateness filter
        appropriate_words = [
            w for w in words 
            if self.is_appropriate(w['word'], strict=strict_appropriate)
        ]
        
        # Then rarity filter
        if exclude_rare:
            appropriate_words = [
                w for w in appropriate_words 
                if self.freq_dist[w['word']] > 2
            ]
        
        # Remove duplicates
        seen = set()
        unique_words = []
        for w in appropriate_words:
            if w['word'] not in seen:
                seen.add(w['word'])
                unique_words.append(w)
        
        # Sort by frequency
        unique_words.sort(key=lambda x: self.freq_dist[x['word']], reverse=True)
        
        return unique_words[:max_words]
    
    def generate_for_skill(self, skill_id):
        """Route to appropriate generator based on skill type"""
        skill_info = self.skill_mappings[skill_id]
        skill_type = skill_info['type']
        
        generators = {
            'cvc': self.get_cvc_words,
            'cv': self.get_cv_words,
            'c_or_k': self.get_c_or_k_words,
            'digraph': self.get_digraph_words,
            'initial_blend': self.get_initial_blend_words,
            'final_blend': self.get_final_blend_words,
            'floss': self.get_floss_words,
            'compound': self.get_compound_words,
            'catch': self.get_catch_words,
            'bridge': self.get_bridge_words,
            'magic_e': self.get_magic_e_words,
            'rule_breaker': self.get_rule_breaker_words,
            'soft_sound': self.get_soft_sound_words,
            'y_vowel': self.get_y_vowel_words,
            'schwa': self.get_schwa_words,
            'silent': self.get_silent_letter_words,
            'ed_sounds': self.get_ed_sounds_words,
            'vowel_team': self.get_vowel_team_words,
            'homophones': self.get_homophones,
            'r_controlled': self.get_r_controlled_words,
            'syllable': self.get_syllable_pattern_words,
            'prefix': self.get_prefix_words,
            'suffix': self.get_suffix_words,
            '1_1_1_doubling': self.get_1_1_1_doubling_words,
            'e_drop': self.get_e_drop_words,
            'y_to_i': self.get_y_to_i_words,
            'plural': self.get_plural_words,
            '2_1_1_doubling': self.get_2_1_1_doubling_words,
            'silent_e_not_plural': self.get_silent_e_not_plural_words,
            'contractions': self.get_contractions
        }
        
        if skill_type in generators:
            return generators[skill_type](skill_id)
        else:
            print(f"  Generator not implemented for {skill_type}")
            return []
    
    # === GENERATOR METHODS FOR EACH SKILL TYPE ===
    
    def get_cvc_words(self, skill_id):
        """Generate CVC words for a specific vowel - EXACTLY 3 letters"""
        pattern_info = self.skill_mappings[skill_id]
        target_vowel = pattern_info['vowel']
        words = []
        
        for word, pronunciations in self.cmu_dict.items():
            if len(word) != 3 or not word.isalpha():
                continue
                
            phonemes = pronunciations[0]
            
            if (len(phonemes) == 3 and 
                self._is_consonant_sound(phonemes[0]) and
                target_vowel in phonemes[1] and
                self._is_consonant_sound(phonemes[2])):
                
                words.append(self._create_word_entry(
                    skill_id, word, word[1], 'medial', 'target'
                ))
        
        return words
    
    def get_cv_words(self, skill_id):
        """Generate CV (open syllable) words - EXACTLY 2 letters"""
        pattern_info = self.skill_mappings[skill_id]
        target_vowel = pattern_info['vowel']
        words = []
        
        for word, pronunciations in self.cmu_dict.items():
            if len(word) != 2 or not word.isalpha():
                continue
                
            phonemes = pronunciations[0]
            
            if (len(phonemes) == 2 and 
                self._is_consonant_sound(phonemes[0]) and
                target_vowel in phonemes[1]):
                
                words.append(self._create_word_entry(
                    skill_id, word, word[1], 'final', 'target'
                ))
        
        return words
    
    def get_c_or_k_words(self, skill_id):
        """Generate words following c/k spelling rules"""
        pattern_info = self.skill_mappings[skill_id]
        rule = pattern_info['rule']
        words = []
        
        for word in self.cmu_dict.keys():
            if rule == 'c_before_aou':
                if any(word.startswith(f'c{v}') for v in ['a', 'o', 'u']):
                    words.append(self._create_word_entry(
                        skill_id, word, 'c', 'initial', 'target'
                    ))
            elif rule == 'k_before_eiy':
                if any(word.startswith(f'k{v}') for v in ['e', 'i', 'y']):
                    words.append(self._create_word_entry(
                        skill_id, word, 'k', 'initial', 'target'
                    ))
        
        return words
    
    def get_digraph_words(self, skill_id):
        """Generate digraph words - LIMITED TO 3-4 LETTERS"""
        pattern_info = self.skill_mappings[skill_id]
        target_sound = pattern_info['sound']
        digraph = pattern_info['name'].split()[0]
        words = []
        
        for word, pronunciations in self.cmu_dict.items():
            # Apply strict length limit for digraphs
            if len(word) < 3 or len(word) > 4:
                continue
                
            if digraph not in word:
                continue
                
            phonemes = ' '.join(pronunciations[0])
            
            # Handle th having two sounds
            if isinstance(target_sound, list):
                if not any(sound in phonemes for sound in target_sound):
                    continue
            elif target_sound not in phonemes:
                continue
            
            position = self._get_pattern_position(word, digraph)
            
            words.append(self._create_word_entry(
                skill_id, word, digraph, position, 'target'
            ))
        
        return words
    
    def get_initial_blend_words(self, skill_id):
        """Generate words with initial blends"""
        pattern_info = self.skill_mappings[skill_id]
        patterns = pattern_info['patterns']
        words = []
        
        for pattern in patterns:
            for word in self.cmu_dict.keys():
                if word.startswith(pattern) and len(word) > len(pattern):
                    words.append(self._create_word_entry(
                        skill_id, word, pattern, 'initial', 'target'
                    ))
        
        return words
    
    def get_final_blend_words(self, skill_id):
        """Generate words with final blends"""
        pattern_info = self.skill_mappings[skill_id]
        pattern = pattern_info['pattern']
        words = []
        
        for word in self.cmu_dict.keys():
            if word.endswith(pattern) and len(word) > len(pattern):
                words.append(self._create_word_entry(
                    skill_id, word, pattern, 'final', 'target'
                ))
        
        return words
    
    def get_floss_words(self, skill_id):
        """Generate words following FLOSS rule (ff, ll, ss after short vowel)"""
        words = []
        floss_patterns = ['ff', 'll', 'ss', 'zz']
        
        for word in self.cmu_dict.keys():
            for pattern in floss_patterns:
                if pattern in word:
                    idx = word.find(pattern)
                    if idx > 0 and word[idx-1] in 'aeiou':
                        words.append(self._create_word_entry(
                            skill_id, word, pattern, 'medial/final', 'target'
                        ))
        
        return words
    
    def get_compound_words(self, skill_id):
        """Generate compound words (backpack rule)"""
        words = []
        word_set = set(self.cmu_dict.keys())
        
        for word in word_set:
            if len(word) > 6:
                for i in range(3, len(word)-3):
                    part1, part2 = word[:i], word[i:]
                    if part1 in word_set and part2 in word_set:
                        words.append(self._create_word_entry(
                            skill_id, word, 'compound', 'multiple', 'target',
                            syllable_breaks=f"{part1}-{part2}"
                        ))
                        break
        
        return words
    
    def get_catch_words(self, skill_id):
        """Generate words with -tch pattern"""
        words = []
        
        for word in self.cmu_dict.keys():
            if 'tch' in word:
                words.append(self._create_word_entry(
                    skill_id, word, 'tch', self._get_pattern_position(word, 'tch'), 'target'
                ))
        
        return words
    
    def get_bridge_words(self, skill_id):
        """Generate words with -dge pattern"""
        words = []
        
        for word in self.cmu_dict.keys():
            if 'dge' in word:
                words.append(self._create_word_entry(
                    skill_id, word, 'dge', self._get_pattern_position(word, 'dge'), 'target'
                ))
        
        return words
    
    def get_magic_e_words(self, skill_id):
        """Generate VCe pattern words"""
        words = []
        
        for word in self.cmu_dict.keys():
            if (len(word) >= 4 and 
                word[-1] == 'e' and 
                word[-2] not in 'aeiou' and
                word[-3] in 'aeiou' and
                word[-4] not in 'aeiou'):
                
                words.append(self._create_word_entry(
                    skill_id, word, 'VCe', 'final', 'target'
                ))
        
        return words
    
    def get_rule_breaker_words(self, skill_id):
        """Generate closed syllable rule breakers (ind, ild, old, ost, olt)"""
        patterns = ['ind', 'ild', 'old', 'ost', 'olt']
        words = []
        
        for pattern in patterns:
            for word in self.cmu_dict.keys():
                if pattern in word:
                    words.append(self._create_word_entry(
                        skill_id, word, pattern, 
                        self._get_pattern_position(word, pattern), 'target'
                    ))
        
        return words
    
    def get_soft_sound_words(self, skill_id):
        """Generate words with soft c or g"""
        pattern_info = self.skill_mappings[skill_id]
        letter = pattern_info['letter']
        words = []
        
        soft_triggers = ['e', 'i', 'y']
        
        for word in self.cmu_dict.keys():
            for i, char in enumerate(word):
                if char == letter and i < len(word) - 1:
                    if word[i + 1] in soft_triggers:
                        words.append(self._create_word_entry(
                            skill_id, word, f"{letter}+{word[i+1]}", 
                            'varies', 'target'
                        ))
        
        return words
    
    def get_y_vowel_words(self, skill_id):
        """Generate words where y acts as a vowel"""
        words = []
        
        for word in self.cmu_dict.keys():
            if 'y' in word:
                if word.endswith('y') and len(word) > 1:
                    words.append(self._create_word_entry(
                        skill_id, word, 'y', 'final', 'target'
                    ))
                elif 'y' in word[1:-1]:
                    words.append(self._create_word_entry(
                        skill_id, word, 'y', 'medial', 'target'
                    ))
        
        return words
    
    def get_schwa_words(self, skill_id):
        """Generate words with schwa sounds"""
        pattern_info = self.skill_mappings[skill_id]
        target_vowel = pattern_info['vowel']
        words = []
        
        # Common schwa patterns
        schwa_endings = ['al', 'el', 'il', 'ol', 'ul', 'le', 'on', 'en']
        
        for word in self.cmu_dict.keys():
            for ending in schwa_endings:
                if word.endswith(ending) and target_vowel in ending:
                    words.append(self._create_word_entry(
                        skill_id, word, ending, 'final', 'target',
                        notes='Schwa sound'
                    ))
        
        return words
    
    def get_silent_letter_words(self, skill_id):
        """Generate words with silent letters"""
        silent_patterns = {
            'kn': 'k', 'wr': 'w', 'mb': 'b', 'gh': 'gh',
            'gn': 'g', 'ps': 'p', 'rh': 'h'
        }
        
        words = []
        for pattern, silent in silent_patterns.items():
            for word in self.cmu_dict.keys():
                if pattern in word:
                    words.append(self._create_word_entry(
                        skill_id, word, pattern,
                        self._get_pattern_position(word, pattern), 'target',
                        notes=f'Silent: {silent}'
                    ))
        
        return words
    
    def get_ed_sounds_words(self, skill_id):
        """Generate words showing three sounds of -ed"""
        words = []
        
        for word in self.cmu_dict.keys():
            if word.endswith('ed') and len(word) > 3:
                base = word[:-2]
                
                # Try to determine which sound
                if base and base[-1] in 'td':
                    sound = '/id/'
                elif base and base[-1] in 'bglmnrvwyz':
                    sound = '/d/'
                else:
                    sound = '/t/'
                
                words.append(self._create_word_entry(
                    skill_id, word, 'ed', 'final', 'target',
                    notes=f'-ed says {sound}'
                ))
        
        return words
    
    def get_vowel_team_words(self, skill_id):
        """Generate words with vowel teams"""
        pattern_info = self.skill_mappings[skill_id]
        patterns = pattern_info['patterns']
        words = []
        
        for pattern in patterns:
            for word in self.cmu_dict.keys():
                if pattern in word:
                    words.append(self._create_word_entry(
                        skill_id, word, pattern, 
                        self._get_pattern_position(word, pattern), 'target'
                    ))
        
        return words
    
    def get_homophones(self, skill_id):
        """Generate homophone pairs"""
        homophone_pairs = [
            ('to', 'too', 'two'), ('there', 'their', 'they\'re'),
            ('your', 'you\'re'), ('its', 'it\'s'), ('know', 'no'),
            ('right', 'write'), ('meet', 'meat'), ('peace', 'piece'),
            ('break', 'brake'), ('flour', 'flower'), ('mail', 'male'),
            ('tail', 'tale'), ('sail', 'sale'), ('wait', 'weight')
        ]
        
        words = []
        for group in homophone_pairs:
            for word in group:
                words.append(self._create_word_entry(
                    skill_id, word, 'homophone', 'n/a', 'target',
                    notes=f'Homophones: {", ".join(group)}'
                ))
        
        return words
    
    def get_r_controlled_words(self, skill_id):
        """Generate r-controlled vowel words"""
        pattern_info = self.skill_mappings[skill_id]
        
        if 'patterns' in pattern_info:
            patterns = pattern_info['patterns']
        else:
            patterns = [pattern_info['pattern']]
            
        words = []
        
        for pattern in patterns:
            for word in self.cmu_dict.keys():
                if pattern in word:
                    words.append(self._create_word_entry(
                        skill_id, word, pattern,
                        self._get_pattern_position(word, pattern), 'target'
                    ))
        
        return words
    
    def get_syllable_pattern_words(self, skill_id):
        """Generate words for syllabication patterns"""
        pattern_info = self.skill_mappings[skill_id]
        pattern_type = pattern_info['pattern']
        words = []
        
        if pattern_type == 'compound':
            return self.get_compound_words(skill_id)
        
        elif pattern_type == 'c_le':
            # Words ending in consonant + le
            for word in self.cmu_dict.keys():
                if len(word) > 3 and word.endswith('le') and word[-3] not in 'aeiou':
                    words.append(self._create_word_entry(
                        skill_id, word, 'C+le', 'final', 'target',
                        syllable_breaks=f"{word[:-3]}-{word[-3:]}"
                    ))
        
        elif pattern_type == 'vccv_double':
            # Words with double consonants in middle (rabbit)
            for word in self.cmu_dict.keys():
                for i in range(1, len(word)-2):
                    if (word[i] == word[i+1] and 
                        word[i] not in 'aeiou' and
                        word[i-1] in 'aeiou' and
                        i+2 < len(word) and word[i+2] in 'aeiou'):
                        words.append(self._create_word_entry(
                            skill_id, word, 'VCCV', 'medial', 'target',
                            syllable_breaks=f"{word[:i+1]}-{word[i+1:]}"
                        ))
                        break
        
        return words
    
    def get_prefix_words(self, skill_id):
        """Generate words with common prefixes"""
        prefixes = ['un', 're', 'pre', 'dis', 'mis', 'sub', 'inter', 'fore', 'de', 'trans', 'super', 'semi', 'anti', 'mid', 'under']
        words = []
        
        for prefix in prefixes:
            for word in self.cmu_dict.keys():
                if word.startswith(prefix) and len(word) > len(prefix) + 2:
                    words.append(self._create_word_entry(
                        skill_id, word, prefix, 'initial', 'target',
                        syllable_breaks=f"{prefix}-{word[len(prefix):]}"
                    ))
        
        return words
    
    def get_suffix_words(self, skill_id):
        """Generate words with common suffixes"""
        suffixes = ['ing', 'ed', 'er', 'est', 'ly', 'ness', 'ful', 'less', 'ment', 'tion']
        words = []
        
        for suffix in suffixes:
            for word in self.cmu_dict.keys():
                if word.endswith(suffix) and len(word) > len(suffix) + 2:
                    words.append(self._create_word_entry(
                        skill_id, word, suffix, 'final', 'target'
                    ))
        
        return words
    
    def get_1_1_1_doubling_words(self, skill_id):
        """Generate words following 1-1-1 doubling rule"""
        words = []
        
        # Look for words ending in doubled consonant + suffix
        for word in self.cmu_dict.keys():
            if len(word) > 5:
                for suffix in ['ing', 'ed', 'er']:
                    if word.endswith(suffix):
                        base = word[:-len(suffix)]
                        if (len(base) > 1 and 
                            base[-1] == base[-2] and 
                            base[-1] not in 'aeiou'):
                            words.append(self._create_word_entry(
                                skill_id, word, '1-1-1', 'final', 'target',
                                notes='Doubled consonant before suffix'
                            ))
        
        return words
    
    def get_e_drop_words(self, skill_id):
        """Generate words showing e-drop rule"""
        words = []
        
        # Common e-drop examples
        e_drop_pairs = [
            ('make', 'making'), ('take', 'taking'), ('bake', 'baking'),
            ('hope', 'hoping'), ('ride', 'riding'), ('write', 'writing'),
            ('dance', 'dancing'), ('love', 'loving'), ('move', 'moving')
        ]
        
        for base, inflected in e_drop_pairs:
            if inflected in self.cmu_dict:
                words.append(self._create_word_entry(
                    skill_id, inflected, 'e-drop', 'n/a', 'target',
                    notes=f'Base word: {base}'
                ))
        
        return words
    
    def get_y_to_i_words(self, skill_id):
        """Generate words showing y to i change"""
        words = []
        
        # Common y to i examples
        y_to_i_pairs = [
            ('happy', 'happiness'), ('busy', 'business'), ('beauty', 'beautiful'),
            ('carry', 'carried'), ('try', 'tried'), ('cry', 'cried'),
            ('baby', 'babies'), ('lady', 'ladies'), ('city', 'cities')
        ]
        
        for base, changed in y_to_i_pairs:
            if changed in self.cmu_dict:
                words.append(self._create_word_entry(
                    skill_id, changed, 'y→i', 'varies', 'target',
                    notes=f'Base word: {base}'
                ))
        
        return words
    
    def get_plural_words(self, skill_id):
        """Generate plural words based on rule type"""
        pattern_info = self.skill_mappings[skill_id]
        rule = pattern_info['rule']
        words = []
        
        if rule == 's':
            # Regular -s plurals
            for word in self.cmu_dict.keys():
                if word.endswith('s') and len(word) > 2 and word[:-1] in self.cmu_dict:
                    words.append(self._create_word_entry(
                        skill_id, word, '-s', 'final', 'target'
                    ))
        
        elif rule == 'es':
            # -es plurals (after s, x, z, ch, sh)
            for word in self.cmu_dict.keys():
                if word.endswith('es') and len(word) > 3:
                    base = word[:-2]
                    if any(base.endswith(end) for end in ['s', 'x', 'z', 'ch', 'sh']):
                        words.append(self._create_word_entry(
                            skill_id, word, '-es', 'final', 'target'
                        ))
        
        elif rule == 'f_to_v':
            # f to v plurals
            f_to_v_pairs = [
                ('leaf', 'leaves'), ('wife', 'wives'), ('knife', 'knives'),
                ('life', 'lives'), ('half', 'halves'), ('self', 'selves'),
                ('calf', 'calves'), ('shelf', 'shelves'), ('wolf', 'wolves')
            ]
            
            for singular, plural in f_to_v_pairs:
                if plural in self.cmu_dict:
                    words.append(self._create_word_entry(
                        skill_id, plural, 'f→ves', 'final', 'target',
                        notes=f'Singular: {singular}'
                    ))
        
        elif rule == 'irregular':
            # Irregular plurals
            irregular_pairs = [
                ('child', 'children'), ('man', 'men'), ('woman', 'women'),
                ('foot', 'feet'), ('tooth', 'teeth'), ('mouse', 'mice'),
                ('goose', 'geese'), ('person', 'people')
            ]
            
            for singular, plural in irregular_pairs:
                if plural in self.cmu_dict:
                    words.append(self._create_word_entry(
                        skill_id, plural, 'irregular', 'n/a', 'target',
                        notes=f'Singular: {singular}'
                    ))
        
        return words
    
    def get_2_1_1_doubling_words(self, skill_id):
        """Generate words following 2-1-1 doubling rule"""
        words = []
        
        # Two-syllable words with stress on second syllable
        doubling_examples = [
            'beginning', 'forgetting', 'admitted', 'controlled',
            'occurred', 'preferred', 'referring', 'regretted'
        ]
        
        for word in doubling_examples:
            if word in self.cmu_dict:
                words.append(self._create_word_entry(
                    skill_id, word, '2-1-1', 'varies', 'target',
                    notes='Doubled consonant in 2-syllable word'
                ))
        
        return words
    
    def get_silent_e_not_plural_words(self, skill_id):
        """Generate words with silent e that aren't plurals"""
        words = []
        
        for word in self.cmu_dict.keys():
            if word.endswith('e') and len(word) > 2:
                # Check if it's not a plural (word without 'e' exists)
                without_e = word[:-1]
                if without_e not in self.cmu_dict or not without_e.endswith('s'):
                    # Check if it follows VCe pattern
                    if (len(word) >= 4 and 
                        word[-2] not in 'aeiou' and
                        word[-3] in 'aeiou'):
                        words.append(self._create_word_entry(
                            skill_id, word, 'silent-e', 'final', 'target'
                        ))
        
        return words
    
    def get_contractions(self, skill_id):
        """Generate contractions"""
        common_contractions = {
            "don't": "do not", "can't": "cannot", "won't": "will not",
            "isn't": "is not", "aren't": "are not", "wasn't": "was not",
            "weren't": "were not", "hasn't": "has not", "haven't": "have not",
            "hadn't": "had not", "doesn't": "does not", "didn't": "did not",
            "it's": "it is", "that's": "that is", "what's": "what is",
            "there's": "there is", "here's": "here is", "who's": "who is",
            "I'm": "I am", "you're": "you are", "we're": "we are",
            "they're": "they are", "I've": "I have", "you've": "you have",
            "we've": "we have", "they've": "they have", "I'd": "I would",
            "you'd": "you would", "he'd": "he would", "she'd": "she would",
            "we'd": "we would", "they'd": "they would", "I'll": "I will",
            "you'll": "you will", "he'll": "he will", "she'll": "she will",
            "we'll": "we will", "they'll": "they will"
        }
        
        words = []
        for contraction, full_form in common_contractions.items():
            words.append({
                'skill_id': skill_id,
                'word': contraction,
                'primary_pattern': "'",
                'pattern_position': 'medial',
                'is_HF': True,
                'is_heart': False,
                'irregular_part': '',
                'irregular_sound': '',
                'syllable_breaks': contraction,
                'word_type': 'target',
                'notes': f'Contraction of: {full_form}'
            })
        
        return words
    
    # === HELPER METHODS ===
    
    def _create_word_entry(self, skill_id, word, pattern, position, word_type, 
                          syllable_breaks=None, notes=''):
        """Create a standardized word entry"""
        return {
            'skill_id': skill_id,
            'word': word,
            'primary_pattern': pattern,
            'pattern_position': position,
            'is_HF': word in self.high_freq_words,
            'is_heart': False,
            'irregular_part': '',
            'irregular_sound': '',
            'syllable_breaks': syllable_breaks or word,
            'word_type': word_type,
            'notes': notes
        }
    
    def _is_consonant_sound(self, phoneme):
        """Check if phoneme is a consonant"""
        vowel_sounds = ['AA', 'AE', 'AH', 'AO', 'AW', 'AY', 'EH', 'ER', 
                       'EY', 'IH', 'IY', 'OW', 'OY', 'UH', 'UW']
        return not any(v in phoneme for v in vowel_sounds)
    
    def _get_pattern_position(self, word, pattern):
        """Determine where pattern appears in word"""
        if word.startswith(pattern):
            return 'initial'
        elif word.endswith(pattern):
            return 'final'
        else:
            return 'medial'
    
    def _get_syllable_breaks(self, word):
        """Simple syllable breaking (can be enhanced)"""
        if len(word) <= 4:
            return word
        # This is simplified - you might want a more sophisticated approach
        return word
    
    # === MAIN GENERATION AND EXPORT METHODS ===
    
    def generate_all_skills(self, output_dir='word_lists'):
        """Generate word lists for all 30 skills"""
        os.makedirs(output_dir, exist_ok=True)
        
        summary = []
        
        for skill_id in sorted(self.skill_mappings.keys()):
            skill_info = self.skill_mappings[skill_id]
            print(f"\nGenerating words for skill {skill_id}: {skill_info['name']}")
            
            # Show length constraints
            if skill_id in self.word_length_limits:
                limits = self.word_length_limits[skill_id]
                print(f"  Length constraints: {limits['min']}-{limits['max']} letters")
            
            try:
                words = self.generate_for_skill(skill_id)
                
                if words:
                    # Filter with all constraints
                    words = self.filter_words(
                        words, 
                        skill_id=skill_id,
                        max_words=50
                    )
                    
                    print(f"  Generated {len(words)} appropriate words")
                    
                    # Export to CSV
                    filename = f"{output_dir}/skill_{skill_id}_{skill_info['name'].replace(' ', '_').replace('/', '_')}.csv"
                    self.export_to_csv(words, filename)
                    
                    summary.append({
                        'skill_id': skill_id,
                        'skill_name': skill_info['name'],
                        'word_count': len(words),
                        'min_length': self.word_length_limits.get(skill_id, {}).get('min', 'N/A'),
                        'max_length': self.word_length_limits.get(skill_id, {}).get('max', 'N/A'),
                        'filename': filename
                    })
                else:
                    print(f"  No words generated for {skill_id}")
                    
            except Exception as e:
                print(f"  Error generating {skill_id}: {str(e)}")
        
        # Export summary
        summary_df = pd.DataFrame(summary)
        summary_df.to_csv(f"{output_dir}/generation_summary.csv", index=False)
        print(f"\nGeneration complete! Summary saved to {output_dir}/generation_summary.csv")
        
        return summary
    
    def export_to_csv(self, words, filename):
        """Export word list to CSV"""
        df = pd.DataFrame(words)
        df.to_csv(filename, index=False)
    
    def generate_reports(self, output_dir='reports'):
        """Generate various analysis reports"""
        os.makedirs(output_dir, exist_ok=True)
        
        # Word length limits report
        self.generate_length_report(output_dir)
        
        # Filtered words report
        self.generate_appropriateness_report(output_dir)
        
        print(f"Reports generated in {output_dir}/")
    
    def generate_length_report(self, output_dir):
        """Generate a report showing word length distribution by skill"""
        report = []
        
        for skill_id in sorted(self.skill_mappings.keys()):
            skill_name = self.skill_mappings[skill_id]['name']
            limits = self.word_length_limits.get(skill_id, {'min': 'N/A', 'max': 'N/A'})
            
            report.append({
                'skill_id': skill_id,
                'skill_name': skill_name,
                'min_length': limits.get('min', 'N/A'),
                'max_length': limits.get('max', 'N/A')
            })
        
        df = pd.DataFrame(report)
        df.to_csv(f'{output_dir}/word_length_limits_by_skill.csv', index=False)
    
    def generate_appropriateness_report(self, output_dir):
        """Generate a report of filtered inappropriate words"""
        with open(f'{output_dir}/filtered_words_report.txt', 'w') as f:
            f.write("INAPPROPRIATE WORDS FILTER REPORT\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("INAPPROPRIATE WORDS LIST:\n")
            for word in sorted(self.inappropriate_words):
                f.write(f"  - {word}\n")
            
            f.write("\nMILD CONCERNS LIST:\n")
            for word in sorted(self.mild_concerns):
                f.write(f"  - {word}\n")
            
            f.write("\nSUBSTRING FILTERS:\n")
            f.write("  - sex (except: sixteen, sixty)\n")
            f.write("  - ass (except: class, pass, etc.)\n")
            f.write("  - tit (except: title, competition, etc.)\n")

# === MAIN EXECUTION ===

def main():
    """Main execution function"""
    print("=" * 60)
    print("ORTON-GILLINGHAM WORD LIST GENERATOR")
    print("=" * 60)
    
    # Initialize generator
    generator = OrtonGillinghamCompleteGenerator()
    
    # Generate all word lists
    print("\nGenerating word lists for all 30 skills...")
    summary = generator.generate_all_skills()
    
    # Generate reports
    print("\nGenerating analysis reports...")
    generator.generate_reports()
    
    # Print final summary
    print("\n" + "=" * 60)
    print("GENERATION COMPLETE!")
    print("=" * 60)
    print(f"Total skills processed: {len(summary)}")
    print(f"Total words generated: {sum(s['word_count'] for s in summary)}")
    print(f"Average words per skill: {sum(s['word_count'] for s in summary) / len(summary):.1f}")
    print("\nFiles created:")
    print("  - word_lists/ (contains all skill CSVs)")
    print("  - word_lists/generation_summary.csv")
    print("  - reports/word_length_limits_by_skill.csv")
    print("  - reports/filtered_words_report.txt")

if __name__ == "__main__":
    main()