#!/usr/bin/env python3
"""
Complete Phoneme-Aware Decodable Word Generator for Orton-Gillingham

This generator ensures:
1. All patterns in a word are decodable at the skill level (no "stood" at 5.3)
2. Patterns make the correct SOUND, not just have the right spelling
"""

import nltk
import pandas as pd
import os
from collections import defaultdict

class PhonemeAwareDecodableGenerator:
    def __init__(self):
        print("Initializing Phoneme-Aware Decodable Word Generator...")
        
        # Load CMU dictionary
        self.cmu_dict = nltk.corpus.cmudict.
        dict()
        
        # Load frequency data
        self.freq_dist = self._load_frequency_data()
        
        # Define skill sequence
        self.skill_sequence = self._define_skill_sequence()
        
        # Define what patterns are taught at each skill
        self.patterns_by_skill = self._define_patterns_by_skill()
        
        # Define phoneme mappings
        self.skill_phoneme_requirements = self._define_phoneme_requirements()
        
        # Load heart words
        self.heart_words_by_skill = self._load_heart_words()
        
        print("Initialization complete!")
    
    def _load_frequency_data(self):
        """Load word frequency from Brown corpus"""
        brown_words = nltk.corpus.brown.words()
        return nltk.FreqDist(word.lower() for word in brown_words)
    
    def _load_heart_words(self):
        """Load heart words by skill"""
        heart_words = defaultdict(set)
        heart_words_path = '/Users/<USER>/word-generator/heart_words/heart_words_reference_by_skill.csv'
        
        if os.path.exists(heart_words_path):
            df = pd.read_csv(heart_words_path)
            for _, row in df.iterrows():
                word = row['word'].lower()
                skill = str(row['skill_introduction'])
                heart_words[skill].add(word)
        
        return heart_words
    
    def _define_skill_sequence(self):
        """Define the order skills are taught"""
        return [
            '1.1', '1.2', '1.3', '1.4', '1.5',  # CVC
            '2.1', '2.2', '2.3',                  # CV
            '3.1', '3.2',                         # C/K rule
            '4.1', '4.2', '4.3', '4.4', '4.5',   # Digraphs
            '5.1', '5.2', '5.3', '5.4',           # Initial blends
            '6.1', '6.2', '6.3', '6.4', '6.5',   # Final blends
            '6.6', '6.7', '6.8', '6.9', '6.10',
            '6.11', '6.12', '6.13', '6.14', '6.15',
            '7.0', '8.0', '9.0', '10.0',          # Special rules
            '11.0',                               # Magic E
            '12.1',                               # Closed syllable exceptions
            '13.1', '13.2',                       # Soft sounds
            '14.1',                               # Y as vowel
            '15.1', '15.2', '15.3',               # Schwa
            '16.0',                               # Silent letters
            '17.0',                               # 3 sounds of ED
            '18.1', '18.2', '18.3', '18.4',      # Vowel teams
            '18.5', '18.6', '18.7', '18.8',
            '18.9', '18.10', '18.11', '18.12',
            '19.0',                               # Homophones
            '20.1', '20.2', '20.3', '20.4', '20.5', # R-controlled
            '21.1', '21.2', '21.5',               # Syllabication
            '22.0', '23.1', '23.2',               # Morphology
            '24.0', '25.0', '26.0',
            '27.1', '27.2', '27.3', '27.5',
            '28.0', '29.0', '30.0'
        ]
    
    def _define_patterns_by_skill(self):
        """Define what spelling patterns are taught at each skill"""
        patterns = {}
        
        # Basic CVC - only short vowels
        patterns['1.1'] = {'short_vowels': ['a'], 'pattern': 'CVC'}
        patterns['1.2'] = {'short_vowels': ['a', 'e'], 'pattern': 'CVC'}
        patterns['1.3'] = {'short_vowels': ['a', 'e', 'i'], 'pattern': 'CVC'}
        patterns['1.4'] = {'short_vowels': ['a', 'e', 'i', 'o'], 'pattern': 'CVC'}
        patterns['1.5'] = {'short_vowels': ['a', 'e', 'i', 'o', 'u'], 'pattern': 'CVC'}
        
        # CV open syllables
        patterns['2.1'] = {'specific_words': ['be', 'he', 'me', 'we', 'she']}
        patterns['2.2'] = {'specific_words': ['go', 'no', 'so']}
        patterns['2.3'] = {'specific_words': ['I', 'hi']}
        
        # Digraphs
        patterns['4.1'] = {'digraphs': ['ch']}
        patterns['4.2'] = {'digraphs': ['sh']}
        patterns['4.3'] = {'digraphs': ['th']}
        patterns['4.4'] = {'digraphs': ['wh']}
        patterns['4.5'] = {'digraphs': ['ck']}
        
        # Initial blends
        patterns['5.1'] = {'initial_blends': ['bl', 'cl', 'fl', 'gl', 'pl', 'sl']}
        patterns['5.2'] = {'initial_blends': ['br', 'cr', 'dr', 'fr', 'gr', 'pr', 'tr']}
        patterns['5.3'] = {'initial_blends': ['sc', 'sk', 'sm', 'sn', 'sp', 'st', 'sw']}
        patterns['5.4'] = {'initial_blends': ['scr', 'spr', 'str', 'spl', 'squ', 'thr']}
        
        # Final blends
        patterns['6.1'] = {'final_blends': ['nt']}
        patterns['6.2'] = {'final_blends': ['nd']}
        patterns['6.3'] = {'final_blends': ['nk']}
        patterns['6.4'] = {'final_blends': ['lt']}
        patterns['6.5'] = {'final_blends': ['ld']}
        patterns['6.6'] = {'final_blends': ['lf']}
        patterns['6.7'] = {'final_blends': ['lk']}
        patterns['6.8'] = {'final_blends': ['lm']}
        patterns['6.9'] = {'final_blends': ['lp']}
        patterns['6.10'] = {'final_blends': ['mp']}
        patterns['6.12'] = {'final_blends': ['ng']}
        patterns['6.13'] = {'final_blends': ['sk']}
        patterns['6.14'] = {'final_blends': ['st']}
        patterns['6.15'] = {'final_blends': ['ft']}
        
        # Special rules
        patterns['7.0'] = {'floss': ['ff', 'll', 'ss', 'zz']}
        patterns['9.0'] = {'patterns': ['tch']}
        patterns['10.0'] = {'patterns': ['dge']}
        patterns['11.0'] = {'magic_e': True}
        patterns['12.1'] = {'exceptions': ['all', 'ald', 'alk', 'old', 'ild', 'ind']}
        
        # Soft sounds
        patterns['13.1'] = {'soft_g': ['ge', 'gi', 'gy']}
        patterns['13.2'] = {'soft_c': ['ce', 'ci', 'cy']}
        
        # Y as vowel
        patterns['14.1'] = {'y_as_vowel': True}
        
        # Silent letters
        patterns['16.0'] = {'silent': ['kn', 'wr', 'mb', 'gh', 'gn']}
        
        # Vowel teams - WITH PHONEME REQUIREMENTS
        patterns['18.1'] = {'vowel_teams': ['ai', 'ay', 'ei', 'eigh'], 'phoneme': 'EY'}
        patterns['18.2'] = {'vowel_teams': ['ea', 'ee', 'ie', 'ey'], 'phoneme': 'IY'}
        patterns['18.3'] = {'vowel_teams': ['oa', 'oe', 'ow'], 'phoneme': 'OW'}
        patterns['18.4'] = {'vowel_teams': ['ou', 'ow'], 'phoneme': 'AW'}
        patterns['18.5'] = {'vowel_teams': ['ie', 'igh'], 'phoneme': 'AY'}
        patterns['18.6'] = {'vowel_teams': ['ue', 'ew', 'eu'], 'phonemes': ['UW', 'YUW']}
        patterns['18.7'] = {'vowel_teams': ['oo'], 'phoneme': 'UW'}
        patterns['18.8'] = {'vowel_teams': ['oo'], 'phoneme': 'UH'}
        patterns['18.9'] = {'vowel_teams': ['ou'], 'phoneme': 'AH'}
        patterns['18.10'] = {'vowel_teams': ['au', 'aw'], 'phoneme': 'AO'}
        patterns['18.11'] = {'vowel_teams': ['oi', 'oy'], 'phoneme': 'OY'}
        patterns['18.12'] = {'vowel_teams': ['ea'], 'phoneme': 'EH'}
        
        # R-controlled
        patterns['20.1'] = {'r_controlled': ['er', 'ir', 'ur'], 'phoneme': 'ER'}
        patterns['20.2'] = {'r_controlled': ['or'], 'phonemes': ['AO R', 'AO']}
        patterns['20.3'] = {'r_controlled': ['ar'], 'phoneme': 'AA R'}
        patterns['20.4'] = {'r_controlled': ['war'], 'phoneme': 'AO R'}
        patterns['20.5'] = {'r_controlled': ['wor'], 'phoneme': 'ER'}
        
        return patterns
    
    def _define_phoneme_requirements(self):
        """Define phoneme requirements for skills"""
        return {
            # CVC skills - must have specific short vowel sounds
            '1.1': {'vowel_phoneme': 'AE'},  # short a
            '1.2': {'vowel_phoneme': 'EH'},  # short e
            '1.3': {'vowel_phoneme': 'IH'},  # short i
            '1.4': {'vowel_phoneme': 'AA'},  # short o
            '1.5': {'vowel_phoneme': 'AH'},  # short u
            
            # CV skills - must have long vowel sounds
            '2.1': {'vowel_phoneme': 'IY'},  # long e
            '2.2': {'vowel_phoneme': 'OW'},  # long o
            '2.3': {'vowel_phoneme': 'AY'},  # long i
            
            # Vowel teams - patterns must make specific sounds
            '18.1': {'target_phoneme': 'EY'},     # long a
            '18.2': {'target_phoneme': 'IY'},     # long e
            '18.3': {'target_phoneme': 'OW'},     # long o
            '18.4': {'target_phoneme': 'AW'},     # /ow/ as in cow
            '18.5': {'target_phoneme': 'AY'},     # long i
            '18.6': {'target_phonemes': ['UW', 'YUW']},  # long u
            '18.7': {'target_phoneme': 'UW'},     # long oo
            '18.8': {'target_phoneme': 'UH'},     # short oo
            '18.9': {'target_phoneme': 'AH'},     # short u
            '18.10': {'target_phoneme': 'AO'},    # /aw/
            '18.11': {'target_phoneme': 'OY'},    # /oy/
            '18.12': {'target_phoneme': 'EH'},    # short e
            
            # R-controlled
            '20.1': {'target_phoneme': 'ER'},
            '20.2': {'target_phonemes': ['AO R', 'AO']},
            '20.3': {'target_phoneme': 'AA R'}
        }
    
    def get_available_patterns(self, skill_id):
        """Get all patterns available up to a skill level"""
        available = {
            'short_vowels': [],
            'digraphs': [],
            'initial_blends': [],
            'final_blends': [],
            'vowel_teams': [],
            'r_controlled': [],
            'special_patterns': [],
            'magic_e': False,
            'y_as_vowel': False
        }
        
        if skill_id not in self.skill_sequence:
            return available
        
        skill_index = self.skill_sequence.index(skill_id)
        
        for i in range(skill_index + 1):
            current_skill = self.skill_sequence[i]
            if current_skill in self.patterns_by_skill:
                skill_patterns = self.patterns_by_skill[current_skill]
                
                if 'short_vowels' in skill_patterns:
                    available['short_vowels'] = skill_patterns['short_vowels']
                if 'digraphs' in skill_patterns:
                    available['digraphs'].extend(skill_patterns['digraphs'])
                if 'initial_blends' in skill_patterns:
                    available['initial_blends'].extend(skill_patterns['initial_blends'])
                if 'final_blends' in skill_patterns:
                    available['final_blends'].extend(skill_patterns['final_blends'])
                if 'vowel_teams' in skill_patterns:
                    available['vowel_teams'].extend(skill_patterns['vowel_teams'])
                if 'r_controlled' in skill_patterns:
                    available['r_controlled'].extend(skill_patterns['r_controlled'])
                if 'floss' in skill_patterns:
                    available['special_patterns'].extend(skill_patterns['floss'])
                if 'patterns' in skill_patterns:
                    available['special_patterns'].extend(skill_patterns['patterns'])
                if 'magic_e' in skill_patterns:
                    available['magic_e'] = True
                if 'y_as_vowel' in skill_patterns:
                    available['y_as_vowel'] = True
        
        return available
    
    def is_word_decodable(self, word, skill_id):
        """Check if word is decodable using only patterns taught so far"""
        word_lower = word.lower()
        
        # Get available patterns
        available = self.get_available_patterns(skill_id)
        
        # Get available heart words
        available_heart_words = set()
        if skill_id in self.skill_sequence:
            skill_index = self.skill_sequence.index(skill_id)
            for i in range(skill_index + 1):
                current_skill = self.skill_sequence[i]
                available_heart_words.update(self.heart_words_by_skill.get(current_skill, set()))
        
        # Heart words are allowed
        if word_lower in available_heart_words:
            return True, "heart word"
        
        # Check for vowel teams first
        all_vowel_teams = [
            'eigh', 'augh', 'ough',
            'ai', 'ay', 'ea', 'ee', 'ei', 'ey', 'ie', 'oa', 'oe',
            'oi', 'oo', 'ou', 'ow', 'oy', 'ue', 'ui', 'au', 'aw',
            'ew', 'eu', 'igh'
        ]
        
        for vt in all_vowel_teams:
            if vt in word_lower and vt not in available['vowel_teams']:
                return False, f"contains '{vt}' vowel team not yet taught"
        
        # Check for r-controlled patterns
        r_patterns = ['war', 'wor', 'ar', 'er', 'ir', 'or', 'ur']
        for rp in r_patterns:
            if rp in word_lower and rp not in available['r_controlled']:
                return False, f"contains '{rp}' r-controlled pattern not yet taught"
        
        # Check for Magic E
        if (len(word_lower) >= 4 and
            word_lower[-1] == 'e' and
            word_lower[-2] not in 'aeiou' and
            word_lower[-3] in 'aeiou' and
            not available['magic_e']):
            return False, "contains Magic E pattern not yet taught"
        
        # Check for initial blends
        for blend in ['scr', 'spr', 'str', 'spl', 'squ', 'thr',
                      'bl', 'cl', 'fl', 'gl', 'pl', 'sl',
                      'br', 'cr', 'dr', 'fr', 'gr', 'pr', 'tr',
                      'sc', 'sk', 'sm', 'sn', 'sp', 'st', 'sw']:
            if word_lower.startswith(blend) and blend not in available['initial_blends']:
                return False, f"contains '{blend}' initial blend not yet taught"
        
        # Check for final blends
        for blend in ['nt', 'nd', 'nk', 'lt', 'ld', 'lf', 'lk', 'lm', 'lp',
                      'mp', 'ng', 'sk', 'st', 'ft']:
            if word_lower.endswith(blend) and blend not in available['final_blends']:
                return False, f"contains '{blend}' final blend not yet taught"
        
        # Check for digraphs
        for digraph in ['ch', 'sh', 'th', 'wh', 'ck']:
            if digraph in word_lower and digraph not in available['digraphs']:
                return False, f"contains '{digraph}' digraph not yet taught"
        
        # Special patterns
        if 'tch' in word_lower and 'tch' not in available['special_patterns']:
            return False, "contains 'tch' pattern not yet taught"
        if 'dge' in word_lower and 'dge' not in available['special_patterns']:
            return False, "contains 'dge' pattern not yet taught"
        
        # Y as vowel check
        if 'y' in word_lower and not available['y_as_vowel']:
            for i, char in enumerate(word_lower):
                if char == 'y':
                    if i == len(word_lower) - 1 or (i < len(word_lower) - 1 and word_lower[i + 1] not in 'aeiou'):
                        return False, "contains Y as vowel not yet taught"
        
        # For CVC skills, enforce strict constraints
        if skill_id.startswith('1.'):
            if len(word) != 3:
                return False, "not a 3-letter CVC word"
            if not (word[0] not in 'aeiou' and
                   word[1] in available['short_vowels'] and
                   word[2] not in 'aeiou'):
                return False, "not CVC structure"
            # No blends or digraphs
            if any(pattern in word_lower for pattern in 
                   ['bl', 'cl', 'fl', 'gl', 'pl', 'sl', 'br', 'cr', 'dr',
                    'fr', 'gr', 'pr', 'tr', 'sc', 'sk', 'sm', 'sn', 'sp',
                    'st', 'sw', 'ch', 'sh', 'th', 'wh', 'ck']):
                return False, "contains blends or digraphs not allowed in CVC"
        
        return True, "decodable"
    
    def check_vowel_team_phoneme(self, word, pattern, expected_phonemes):
        """Check if a vowel team makes the expected sound"""
        word_lower = word.lower()
        
        if pattern not in word_lower:
            return False, "pattern not found"
        
        if word_lower not in self.cmu_dict:
            return False, "not in dictionary"
        
        # Convert to list if needed
        if isinstance(expected_phonemes, str):
            expected_phonemes = [expected_phonemes]
        
        # Check pronunciations
        for pron in self.cmu_dict[word_lower]:
            pron_str = ' '.join(pron)
            for expected in expected_phonemes:
                if expected in pron_str:
                    return True, f"makes {expected} sound"
        
        return False, "doesn't make expected sound"
    
    def generate_cvc_words(self, skill_id):
        """Generate CVC words with correct vowel phoneme"""
        skill_patterns = self.patterns_by_skill.get(skill_id, {})
        phoneme_req = self.skill_phoneme_requirements.get(skill_id, {})
        
        if 'vowel_phoneme' not in phoneme_req:
            return []
        
        target_phoneme = phoneme_req['vowel_phoneme']
        candidates = []
        
        print(f"\nGenerating CVC words with {target_phoneme} sound...")
        
        for word, pronunciations in self.cmu_dict.items():
            if len(word) != 3 or not word.isalpha():
                continue
            
            # Check structure
            if not (word[0] not in 'aeiou' and
                   word[1] in 'aeiou' and
                   word[2] not in 'aeiou'):
                continue
            
            # Check phonemes
            for pron in pronunciations:
                if (len(pron) == 3 and
                    self._is_consonant_sound(pron[0]) and
                    target_phoneme in pron[1] and
                    self._is_consonant_sound(pron[2])):
                    
                    # Check decodability
                    is_decodable, reason = self.is_word_decodable(word, skill_id)
                    if is_decodable:
                        candidates.append({
                            'word': word,
                            'pattern': word[1],
                            'skill_id': skill_id,
                            'phoneme': target_phoneme
                        })
                        break
        
        return candidates
    
    def generate_vowel_team_words(self, skill_id):
        """Generate vowel team words with correct phonemes"""
        skill_patterns = self.patterns_by_skill.get(skill_id, {})
        phoneme_req = self.skill_phoneme_requirements.get(skill_id, {})
        
        if 'vowel_teams' not in skill_patterns:
            return []
        
        patterns = skill_patterns['vowel_teams']
        expected_phonemes = phoneme_req.get('target_phonemes', 
                                           [phoneme_req.get('target_phoneme')])
        
        candidates = []
        stats = defaultdict(lambda: {'correct': 0, 'incorrect': 0})
        
        print(f"\nGenerating vowel team words for skill {skill_id}")
        print(f"  Patterns: {patterns}")
        print(f"  Expected sound(s): {expected_phonemes}")
        
        for pattern in patterns:
            for word in self.cmu_dict.keys():
                if pattern in word:
                    # Check if pattern makes correct sound
                    makes_sound, reason = self.check_vowel_team_phoneme(
                        word, pattern, expected_phonemes
                    )
                    
                    if makes_sound:
                        # Check overall decodability
                        is_decodable, decode_reason = self.is_word_decodable(word, skill_id)
                        
                        if is_decodable:
                            candidates.append({
                                'word': word,
                                'pattern': pattern,
                                'skill_id': skill_id,
                                'phoneme_verified': True
                            })
                            stats[pattern]['correct'] += 1
                    else:
                        stats[pattern]['incorrect'] += 1
        
        # Report stats
        for pattern, counts in stats.items():
            print(f"  {pattern}: {counts['correct']} correct, {counts['incorrect']} incorrect")
        
        return candidates
    
    def generate_r_controlled_words(self, skill_id):
        """Generate r-controlled words with correct phonemes"""
        skill_patterns = self.patterns_by_skill.get(skill_id, {})
        phoneme_req = self.skill_phoneme_requirements.get(skill_id, {})
        
        if 'r_controlled' not in skill_patterns:
            return []
        
        patterns = skill_patterns['r_controlled']
        expected_phonemes = phoneme_req.get('target_phonemes',
                                           [phoneme_req.get('target_phoneme')])
        
        candidates = []
        
        for pattern in patterns:
            for word in self.cmu_dict.keys():
                if pattern in word:
                    # Check decodability
                    is_decodable, reason = self.is_word_decodable(word, skill_id)
                    
                    if is_decodable:
                        # For r-controlled, we can be more flexible with phoneme checking
                        # since the CMU dict represents these differently
                        candidates.append({
                            'word': word,
                            'pattern': pattern,
                            'skill_id': skill_id
                        })
        
        return candidates
    
    def generate_words_for_skill(self, skill_id):
        """Generate words for any skill type"""
        skill_info = self.patterns_by_skill.get(skill_id, {})
        
        # Route to appropriate generator
        if skill_id.startswith('1.'):
            return self.generate_cvc_words(skill_id)
        elif skill_id.startswith('18.'):
            return self.generate_vowel_team_words(skill_id)
        elif skill_id.startswith('20.'):
            return self.generate_r_controlled_words(skill_id)
        else:
            # For other skills, use basic pattern matching with decodability
            return self.generate_basic_pattern_words(skill_id)
    
    def generate_basic_pattern_words(self, skill_id):
        """Generate words for skills without special phoneme requirements"""
        skill_patterns = self.patterns_by_skill.get(skill_id, {})
        candidates = []
        
        # Handle different pattern types
        if 'initial_blends' in skill_patterns:
            for blend in skill_patterns['initial_blends']:
                for word in self.cmu_dict.keys():
                    if word.startswith(blend):
                        is_decodable, reason = self.is_word_decodable(word, skill_id)
                        if is_decodable:
                            candidates.append({
                                'word': word,
                                'pattern': blend,
                                'skill_id': skill_id
                            })
        
        elif 'final_blends' in skill_patterns:
            for blend in skill_patterns['final_blends']:
                for word in self.cmu_dict.keys():
                    if word.endswith(blend):
                        is_decodable, reason = self.is_word_decodable(word, skill_id)
                        if is_decodable:
                            candidates.append({
                                'word': word,
                                'pattern': blend,
                                'skill_id': skill_id
                            })
        
        elif 'digraphs' in skill_patterns:
            for digraph in skill_patterns['digraphs']:
                for word in self.cmu_dict.keys():
                    if digraph in word:
                        is_decodable, reason = self.is_word_decodable(word, skill_id)
                        if is_decodable:
                            candidates.append({
                                'word': word,
                                'pattern': digraph,
                                'skill_id': skill_id
                            })
        
        return candidates
    
    def filter_and_sort(self, candidates, max_words=50):
        """Filter and sort candidates by frequency"""
        # Remove duplicates
        seen = set()
        unique = []
        for c in candidates:
            if c['word'] not in seen:
                seen.add(c['word'])
                unique.append(c)
        
        # Filter by minimum frequency
        filtered = [c for c in unique if self.freq_dist[c['word']] > 2]
        
        # Sort by frequency
        filtered.sort(key=lambda x: self.freq_dist[x['word']], reverse=True)
        
        return filtered[:max_words]
    
    def _is_consonant_sound(self, phoneme):
        """Check if phoneme is a consonant"""
        vowel_sounds = ['AA', 'AE', 'AH', 'AO', 'AW', 'AY', 'EH', 'ER',
                       'EY', 'IH', 'IY', 'OW', 'OY', 'UH', 'UW']
        return not any(v in phoneme for v in vowel_sounds)
    
    def demonstrate_skill_18_5(self):
        """Demonstrate the fix for skill 18.5"""
        print("\n" + "=" * 70)
        print("DEMONSTRATION: Skill 18.5 (Long I teams)")
        print("=" * 70)
        
        # Show old problematic words
        old_words = ['field', 'believe', 'friend', 'piece', 'chief', 'eight']
        
        print("\nOLD APPROACH PROBLEMS:")
        print("-" * 40)
        for word in old_words:
            if word in self.cmu_dict:
                pron = ' '.join(self.cmu_dict[word][0])
                has_ay = 'AY' in pron
                print(f"✗ {word.ljust(12)} → {pron.ljust(20)} {'Has AY' if has_ay else 'NO AY sound!'}")
        
        # Generate with new approach
        print("\nNEW APPROACH (Phoneme-verified):")
        print("-" * 40)
        
        candidates = self.generate_vowel_team_words('18.5')
        filtered = self.filter_and_sort(candidates, max_words=15)
        
        for entry in filtered[:10]:
            word = entry['word']
            if word in self.cmu_dict:
                pron = ' '.join(self.cmu_dict[word][0])
                print(f"✓ {word.ljust(12)} → {pron.ljust(20)} Has AY sound")
        
        return filtered
    
    def export_to_csv(self, words, skill_id, skill_name, filename):
        """Export words to CSV"""
        export_data = []
        
        for w in words:
            export_data.append({
                'skill_id': skill_id,
                'word': w['word'],
                'primary_pattern': w.get('pattern', ''),
                'pattern_position': self._get_pattern_position(w['word'], w.get('pattern', '')),
                'is_HF': self.freq_dist[w['word']] > 100,
                'is_heart': False,
                'irregular_part': '',
                'irregular_sound': '',
                'syllable_breaks': w['word'],
                'word_type': 'target',
                'notes': 'Phoneme-verified & decodable'
            })
        
        df = pd.DataFrame(export_data)
        df.to_csv(filename, index=False)
        print(f"\nExported {len(export_data)} words to {filename}")
    
    def _get_pattern_position(self, word, pattern):
        """Get position of pattern in word"""
        if not pattern:
            return ''
        if word.startswith(pattern):
            return 'initial'
        elif word.endswith(pattern):
            return 'final'
        else:
            return 'medial'

def main():
    """Main demonstration"""
    print("PHONEME-AWARE DECODABLE WORD GENERATOR")
    print("=" * 70)
    print("Ensuring both decodability AND correct sounds!")
    
    generator = PhonemeAwareDecodableGenerator()
    
    # Demonstrate skill 18.5 fix
    words_18_5 = generator.demonstrate_skill_18_5()
    
    # Export corrected list
    generator.export_to_csv(
        words_18_5,
        '18.5',
        'Long i teams',
        'skill_18.5_PHONEME_DECODABLE.csv'
    )
    
    # Show skill 5.3 is still decodable
    print("\n\nDEMONSTRATION: Skill 5.3 (s-blends)")
    print("=" * 70)
    print("Showing that basic decodability still works...")
    
    test_words = ['stood', 'state', 'stop', 'step', 'stick']
    for word in test_words:
        is_decodable, reason = generator.is_word_decodable(word, '5.3')
        status = "✓" if is_decodable else "✗"
        print(f"{status} {word.ljust(10)} - {reason}")
    
    print("\n" + "=" * 70)
    print("KEY IMPROVEMENTS:")
    print("1. Words must be decodable (no 'stood' at 5.3)")
    print("2. Vowel teams must make the correct sound (no 'field' for long I)")
    print("3. True systematic phonics instruction!")

if __name__ == "__main__":
    main()
