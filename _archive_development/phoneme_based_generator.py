#!/usr/bin/env python3
"""
Phoneme-Based Word Generator for Orton-Gillingham

This generator checks that graphemes (spelling patterns) actually produce
the correct phonemes (sounds) for each skill.
"""

import nltk
import pandas as pd
import re
import os
from collections import defaultdict

class PhonemeBasedWordGenerator:
    def __init__(self):
        print("Initializing Phoneme-Based Word Generator...")
        
        # Load CMU Pronouncing Dictionary
        self.cmu_dict = nltk.corpus.cmudict.dict()
        
        # Load word frequency
        self.freq_dist = self._load_frequency_data()
        
        # Define phoneme mappings for vowel sounds
        self.vowel_phonemes = {
            # Short vowels
            'short_a': ['AE'],          # cat
            'short_e': ['EH'],          # bed
            'short_i': ['IH'],          # sit
            'short_o': ['AA'],          # hot
            'short_u': ['AH'],          # cup
            
            # Long vowels
            'long_a': ['EY'],           # make, rain
            'long_e': ['IY'],           # see, beat
            'long_i': ['AY'],           # pie, light
            'long_o': ['OW'],           # go, boat
            'long_u': ['UW', 'YUW'],    # blue, cute
            
            # Other vowel sounds
            'oo_long': ['UW'],          # moon, food
            'oo_short': ['UH'],         # book, good
            'ow_sound': ['AW'],         # cow, out
            'aw_sound': ['AO'],         # saw, caught
            'oy_sound': ['OY'],         # boy, coin
            
            # R-controlled
            'ar': ['AA R'],             # car, start
            'er': ['ER'],               # her, bird, turn
            'or': ['AO R'],             # for, more
            
            # Schwa
            'schwa': ['AH']             # about, taken
        }
        
        # Define grapheme-to-phoneme mappings for checking
        self.grapheme_phoneme_map = self._build_grapheme_phoneme_map()
        
        # Define skill mappings with expected phonemes
        self.skill_mappings = self._load_skill_mappings()
        
        print("Initialization complete!")
    
    def _load_frequency_data(self):
        """Load word frequency from Brown corpus"""
        brown_words = nltk.corpus.brown.words()
        return nltk.FreqDist(word.lower() for word in brown_words)
    
    def _build_grapheme_phoneme_map(self):
        """Build comprehensive grapheme-to-phoneme mappings"""
        return {
            # Vowel teams for long A
            'ai': {'expected': ['EY'], 'skill': '18.1'},  # rain, main
            'ay': {'expected': ['EY'], 'skill': '18.1'},  # day, play
            'ei': {'expected': ['EY'], 'skill': '18.1'},  # vein, rein
            'eigh': {'expected': ['EY'], 'skill': '18.1'}, # eight, weight
            
            # Vowel teams for long E
            'ea': {
                'expected_long_e': ['IY'], 'skill_long_e': '18.2',  # seat, beat
                'expected_short_e': ['EH'], 'skill_short_e': '18.12' # bread, head
            },
            'ee': {'expected': ['IY'], 'skill': '18.2'},  # see, tree
            'ie': {
                'expected_long_e': ['IY'], 'skill_long_e': '18.2',  # field, believe
                'expected_long_i': ['AY'], 'skill_long_i': '18.5'   # pie, tie
            },
            'ey': {'expected': ['IY'], 'skill': '18.2'},  # key, money
            
            # Vowel teams for long I
            'igh': {'expected': ['AY'], 'skill': '18.5'}, # light, night
            # 'ie' handled above
            
            # Vowel teams for long O
            'oa': {'expected': ['OW'], 'skill': '18.3'},  # boat, road
            'oe': {'expected': ['OW'], 'skill': '18.3'},  # toe, hoe
            'ow': {
                'expected_long_o': ['OW'], 'skill_long_o': '18.3',  # grow, snow
                'expected_ow': ['AW'], 'skill_ow': '18.4'           # cow, how
            },
            
            # Vowel teams for /ow/ sound
            'ou': {
                'expected_ow': ['AW'], 'skill_ow': '18.4',      # out, house
                'expected_short_u': ['AH'], 'skill_short_u': '18.9' # could, would
            },
            
            # Vowel teams for long U
            'ue': {'expected': ['UW', 'YUW'], 'skill': '18.6'}, # blue, cue
            'ew': {'expected': ['UW', 'YUW'], 'skill': '18.6'}, # new, few
            'eu': {'expected': ['UW', 'YUW'], 'skill': '18.6'}, # feud
            
            # Vowel teams for /oo/ sounds
            'oo': {
                'expected_long': ['UW'], 'skill_long': '18.7',   # moon, food
                'expected_short': ['UH'], 'skill_short': '18.8'  # book, good
            },
            
            # Vowel teams for /aw/ sound
            'au': {'expected': ['AO'], 'skill': '18.10'}, # haul, caught
            'aw': {'expected': ['AO'], 'skill': '18.10'}, # saw, draw
            
            # Vowel teams for /oy/ sound
            'oi': {'expected': ['OY'], 'skill': '18.11'}, # coin, boil
            'oy': {'expected': ['OY'], 'skill': '18.11'}, # boy, toy
            
            # R-controlled
            'ar': {'expected': ['AA R'], 'skill': '20.3'},
            'er': {'expected': ['ER'], 'skill': '20.1'},
            'ir': {'expected': ['ER'], 'skill': '20.1'},
            'ur': {'expected': ['ER'], 'skill': '20.1'},
            'or': {'expected': ['AO R'], 'skill': '20.2'}
        }
    
    def _load_skill_mappings(self):
        """Load skill mappings with phoneme requirements"""
        return {
            # CVC skills with specific short vowel phonemes
            '1.1': {'type': 'cvc', 'vowel_phoneme': 'AE', 'name': 'CVC short a'},
            '1.2': {'type': 'cvc', 'vowel_phoneme': 'EH', 'name': 'CVC short e'},
            '1.3': {'type': 'cvc', 'vowel_phoneme': 'IH', 'name': 'CVC short i'},
            '1.4': {'type': 'cvc', 'vowel_phoneme': 'AA', 'name': 'CVC short o'},
            '1.5': {'type': 'cvc', 'vowel_phoneme': 'AH', 'name': 'CVC short u'},
            
            # Vowel team skills with specific phoneme requirements
            '18.1': {
                'type': 'vowel_team',
                'patterns': ['ai', 'ay', 'ei', 'eigh'],
                'target_phoneme': 'EY',  # Long A sound
                'name': 'Long a teams'
            },
            '18.2': {
                'type': 'vowel_team',
                'patterns': ['ea', 'ee', 'ie', 'ey'],
                'target_phoneme': 'IY',  # Long E sound
                'name': 'Long e teams'
            },
            '18.3': {
                'type': 'vowel_team',
                'patterns': ['oa', 'oe', 'ow'],
                'target_phoneme': 'OW',  # Long O sound
                'name': 'Long o teams'
            },
            '18.4': {
                'type': 'vowel_team',
                'patterns': ['ou', 'ow'],
                'target_phoneme': 'AW',  # /ow/ sound as in "cow"
                'name': 'ow sound'
            },
            '18.5': {
                'type': 'vowel_team',
                'patterns': ['ie', 'igh'],
                'target_phoneme': 'AY',  # Long I sound
                'name': 'Long i teams'
            },
            '18.6': {
                'type': 'vowel_team',
                'patterns': ['ue', 'ew', 'eu'],
                'target_phonemes': ['UW', 'YUW'],  # Long U sounds
                'name': 'Long u teams'
            },
            '18.7': {
                'type': 'vowel_team',
                'patterns': ['oo'],
                'target_phoneme': 'UW',  # Long OO as in "moon"
                'name': 'Long oo'
            },
            '18.8': {
                'type': 'vowel_team',
                'patterns': ['oo'],
                'target_phoneme': 'UH',  # Short OO as in "book"
                'name': 'Short oo'
            },
            '18.9': {
                'type': 'vowel_team',
                'patterns': ['ou'],
                'target_phoneme': 'AH',  # Short U as in "could"
                'name': 'ou says u'
            },
            '18.10': {
                'type': 'vowel_team',
                'patterns': ['au', 'aw', 'augh'],
                'target_phoneme': 'AO',  # /aw/ sound
                'name': 'aw sound'
            },
            '18.11': {
                'type': 'vowel_team',
                'patterns': ['oi', 'oy'],
                'target_phoneme': 'OY',  # /oy/ sound
                'name': 'oy sound'
            },
            '18.12': {
                'type': 'vowel_team',
                'patterns': ['ea'],
                'target_phoneme': 'EH',  # Short E as in "bread"
                'name': 'ea says e'
            },
            
            # R-controlled with phoneme requirements
            '20.1': {
                'type': 'r_controlled',
                'patterns': ['er', 'ir', 'ur'],
                'target_phoneme': 'ER',
                'name': 'er sound'
            },
            '20.2': {
                'type': 'r_controlled',
                'patterns': ['or', 'ore'],
                'target_phonemes': ['AO R', 'AO'],
                'name': 'or sound'
            },
            '20.3': {
                'type': 'r_controlled',
                'patterns': ['ar'],
                'target_phoneme': 'AA R',
                'name': 'ar sound'
            }
        }
    
    def check_pattern_makes_sound(self, word, pattern, target_phonemes):
        """Check if a pattern in a word makes the expected sound"""
        word_lower = word.lower()
        
        if pattern not in word_lower:
            return False, "Pattern not found"
        
        # Get pronunciations
        if word_lower not in self.cmu_dict:
            return False, "Word not in dictionary"
        
        pronunciations = self.cmu_dict[word_lower]
        
        # Convert target phonemes to list if needed
        if isinstance(target_phonemes, str):
            target_phonemes = [target_phonemes]
        
        # Check each pronunciation
        for pron in pronunciations:
            pron_str = ' '.join(pron)
            
            # Check if any target phoneme is in the pronunciation
            for target in target_phonemes:
                if target in pron_str:
                    # For patterns like 'ea', 'ie', we need to verify the pattern
                    # corresponds to the phoneme location
                    return True, f"Makes {target} sound"
        
        # If we get here, the pattern doesn't make the expected sound
        actual_sounds = []
        for pron in pronunciations:
            pron_str = ' '.join(pron)
            # Try to identify what sound it actually makes
            if pattern == 'ie' and 'IY' in pron_str:
                actual_sounds.append("long E")
            elif pattern == 'ie' and 'EH' in pron_str:
                actual_sounds.append("short E")
            elif pattern == 'ea' and 'IY' in pron_str:
                actual_sounds.append("long E")
            elif pattern == 'ea' and 'EH' in pron_str:
                actual_sounds.append("short E")
            elif pattern == 'igh' and 'EY' in pron_str:
                actual_sounds.append("long A")
            elif pattern == 'ow' and 'AW' in pron_str:
                actual_sounds.append("/ow/ as in cow")
            elif pattern == 'ow' and 'OW' in pron_str:
                actual_sounds.append("long O")
            elif pattern == 'oo' and 'UW' in pron_str:
                actual_sounds.append("long OO")
            elif pattern == 'oo' and 'UH' in pron_str:
                actual_sounds.append("short OO")
        
        if actual_sounds:
            return False, f"Makes {actual_sounds[0]} sound instead"
        else:
            return False, "Doesn't make expected sound"
    
    def generate_vowel_team_words(self, skill_id):
        """Generate words where vowel teams make the correct sound"""
        skill_info = self.skill_mappings[skill_id]
        patterns = skill_info['patterns']
        target_phonemes = skill_info.get('target_phonemes', [skill_info.get('target_phoneme')])
        
        candidates = []
        phoneme_report = defaultdict(lambda: {'correct': 0, 'incorrect': []})
        
        print(f"\nGenerating phoneme-based words for {skill_info['name']}:")
        print(f"  Target sound(s): {target_phonemes}")
        print(f"  Patterns: {patterns}")
        
        for pattern in patterns:
            pattern_candidates = []
            
            # Search through dictionary
            for word in self.cmu_dict.keys():
                if pattern in word:
                    # Check if pattern makes the expected sound
                    makes_sound, reason = self.check_pattern_makes_sound(
                        word, pattern, target_phonemes
                    )
                    
                    if makes_sound:
                        pattern_candidates.append({
                            'word': word,
                            'pattern': pattern,
                            'phoneme_check': 'correct',
                            'reason': reason
                        })
                        phoneme_report[pattern]['correct'] += 1
                    else:
                        phoneme_report[pattern]['incorrect'].append({
                            'word': word,
                            'reason': reason
                        })
            
            # Add the valid candidates
            candidates.extend(pattern_candidates)
            
            # Report findings for this pattern
            print(f"\n  Pattern '{pattern}':")
            print(f"    ✓ Found {phoneme_report[pattern]['correct']} words with correct sound")
            
            # Show a few incorrect examples
            incorrect = phoneme_report[pattern]['incorrect']
            if incorrect:
                print(f"    ✗ Rejected {len(incorrect)} words with wrong sound")
                for ex in incorrect[:3]:
                    print(f"      - '{ex['word']}': {ex['reason']}")
        
        return candidates
    
    def filter_and_sort_words(self, candidates, max_words=50):
        """Filter and sort word candidates"""
        # Remove duplicates
        seen = set()
        unique = []
        for c in candidates:
            if c['word'] not in seen:
                seen.add(c['word'])
                unique.append(c)
        
        # Filter by frequency
        filtered = [c for c in unique if self.freq_dist[c['word']] > 2]
        
        # Sort by frequency
        filtered.sort(key=lambda x: self.freq_dist[x['word']], reverse=True)
        
        return filtered[:max_words]
    
    def generate_skill_18_5_comparison(self):
        """Generate comparison for skill 18.5 to show the difference"""
        print("\n" + "=" * 70)
        print("SKILL 18.5 COMPARISON: Grapheme-based vs Phoneme-based")
        print("=" * 70)
        
        # Words from the old approach
        old_approach_words = [
            'field', 'believe', 'friend', 'piece', 'chief',  # Wrong sound!
            'eight', 'weight',  # Wrong sound!
            'pie', 'tie', 'die', 'light', 'might'  # Correct
        ]
        
        print("\nOLD APPROACH (Grapheme-based):")
        print("-" * 40)
        for word in old_approach_words[:10]:
            if word in self.cmu_dict:
                pron = ' '.join(self.cmu_dict[word][0])
                has_long_i = 'AY' in pron
                status = "✓" if has_long_i else "✗"
                print(f"{status} {word.ljust(12)} → {pron}")
        
        # Generate with new approach
        print("\nNEW APPROACH (Phoneme-based):")
        print("-" * 40)
        
        candidates = self.generate_vowel_team_words('18.5')
        filtered = self.filter_and_sort_words(candidates, max_words=15)
        
        for entry in filtered[:10]:
            word = entry['word']
            if word in self.cmu_dict:
                pron = ' '.join(self.cmu_dict[word][0])
                print(f"✓ {word.ljust(12)} → {pron}")
        
        return filtered
    
    def export_to_csv(self, words, skill_id, filename):
        """Export words to CSV format"""
        # Convert to proper format
        export_data = []
        skill_info = self.skill_mappings[skill_id]
        
        for w in words:
            export_data.append({
                'skill_id': skill_id,
                'word': w['word'],
                'primary_pattern': w['pattern'],
                'pattern_position': self._get_pattern_position(w['word'], w['pattern']),
                'is_HF': self.freq_dist[w['word']] > 100,
                'is_heart': False,
                'irregular_part': '',
                'irregular_sound': '',
                'syllable_breaks': w['word'],
                'word_type': 'target',
                'notes': f"Verified: {w['pattern']} makes {w.get('reason', 'expected sound')}"
            })
        
        df = pd.DataFrame(export_data)
        df.to_csv(filename, index=False)
        print(f"\nExported {len(export_data)} phoneme-verified words to {filename}")
    
    def _get_pattern_position(self, word, pattern):
        """Get position of pattern in word"""
        if word.startswith(pattern):
            return 'initial'
        elif word.endswith(pattern):
            return 'final'
        else:
            return 'medial'

def main():
    """Demonstrate phoneme-based generation"""
    print("PHONEME-BASED WORD GENERATOR")
    print("=" * 70)
    print("Ensuring graphemes (spelling) match phonemes (sounds)")
    
    generator = PhonemeBasedWordGenerator()
    
    # Generate comparison for skill 18.5
    words_18_5 = generator.generate_skill_18_5_comparison()
    
    # Export the corrected list
    generator.export_to_csv(
        words_18_5, 
        '18.5',
        'skill_18.5_long_i_PHONEME_BASED.csv'
    )
    
    # Show other vowel team examples
    print("\n\nOTHER VOWEL TEAM EXAMPLES:")
    print("=" * 70)
    
    # Generate for 18.2 (Long E teams)
    print("\nSkill 18.2 - Long E teams:")
    words_18_2 = generator.generate_vowel_team_words('18.2')
    filtered_18_2 = generator.filter_and_sort_words(words_18_2, max_words=10)
    
    print("\nTop 10 words where 'ea', 'ee', 'ie', 'ey' make long E:")
    for w in filtered_18_2[:10]:
        print(f"  - {w['word']} ({w['pattern']})")
    
    print("\n" + "=" * 70)
    print("KEY IMPROVEMENT:")
    print("Words are now selected based on actual SOUND, not just spelling!")
    print("No more 'field' in Long I lessons!")

if __name__ == "__main__":
    main()
