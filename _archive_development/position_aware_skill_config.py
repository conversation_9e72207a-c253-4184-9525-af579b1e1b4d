#!/usr/bin/env python3
"""
Enhanced skill configuration with position requirements
"""

# Position constants
INITIAL = 'initial'
MEDIAL = 'medial'
FINAL = 'final'
ANY = 'any'
INITIAL_OR_MEDIAL = 'initial_or_medial'
MEDIAL_OR_FINAL = 'medial_or_final'
FINAL_OR_MEDIAL = 'final_or_medial'
INITIAL_OR_FINAL = 'initial_or_final'
MULTIPLE = 'multiple'
VARIOUS = 'various'

# Enhanced skill configurations with position requirements
SKILL_CONFIGS = {
    # CVC Skills - vowel is always medial
    '1.1': {
        'name': 'CVC short a',
        'patterns': {'vowel': 'a'},
        'position': MEDIAL,
        'phoneme': 'AE',  # Short a
        'word_length': {'min': 3, 'max': 3},
        'constraints': ['no_blends', 'no_digraphs', 'no_vowel_teams']
    },
    
    # Initial blends - MUST be at beginning
    '5.1': {
        'name': 'l-blends',
        'patterns': {'initial_blends': ['bl', 'cl', 'fl', 'gl', 'pl', 'sl']},
        'position': INITIAL,  # Must be at beginning
        'word_length': {'min': 4, 'max': 5},
        'constraints': ['no_vowel_teams', 'no_magic_e']
    },
    '5.2': {
        'name': 'r-blends',
        'patterns': {'initial_blends': ['br', 'cr', 'dr', 'fr', 'gr', 'pr', 'tr']},
        'position': INITIAL,  # Must be at beginning
        'word_length': {'min': 4, 'max': 5},
        'constraints': ['no_vowel_teams', 'no_magic_e']
    },
    '5.3': {
        'name': 's-blends',
        'patterns': {'initial_blends': ['sc', 'sk', 'sm', 'sn', 'sp', 'st', 'sw']},
        'position': INITIAL,  # Must be at beginning
        'word_length': {'min': 4, 'max': 5},
        'constraints': ['no_vowel_teams', 'no_magic_e']
    },
    
    # Final blends - MUST be at end
    '6.1': {
        'name': 'nt ending',
        'patterns': {'final_blends': ['nt']},
        'position': FINAL,  # Must be at end
        'word_length': {'min': 4, 'max': 5},
        'constraints': ['no_vowel_teams', 'no_magic_e']
    },
    
    # Digraphs with specific positions
    '4.1': {
        'name': 'ch digraph',
        'patterns': {'digraphs': ['ch']},
        'position': ANY,  # Can appear anywhere
        'phoneme': 'CH',
        'word_length': {'min': 3, 'max': 4}
    },
    '4.4': {
        'name': 'wh digraph',
        'patterns': {'digraphs': ['wh']},
        'position': INITIAL,  # Only at beginning
        'phoneme': 'W',
        'word_length': {'min': 3, 'max': 4}
    },
    '4.5': {
        'name': 'ck digraph',
        'patterns': {'digraphs': ['ck']},
        'position': FINAL,  # Only at end
        'phoneme': 'K',
        'word_length': {'min': 3, 'max': 4}
    },
    
    # Vowel teams with position patterns
    '18.1': {
        'name': 'Long a teams',
        'patterns': {'vowel_teams': ['ai', 'ay', 'ei', 'eigh']},
        'position_by_pattern': {
            'ai': MEDIAL,    # rain, main
            'ay': FINAL,     # day, play
            'ei': MEDIAL,    # vein, rein
            'eigh': MEDIAL   # eight, weight
        },
        'phoneme': 'EY',  # Long a sound
        'word_length': {'min': 4, 'max': 8},
        'phoneme_check': True
    },
    '18.5': {
        'name': 'Long i teams',
        'patterns': {'vowel_teams': ['ie', 'igh']},
        'position_by_pattern': {
            'ie': FINAL,     # pie, tie, die
            'igh': MEDIAL    # light, night, sight
        },
        'phoneme': 'AY',  # Long i sound
        'word_length': {'min': 4, 'max': 8},
        'phoneme_check': True
    },
    
    # Special patterns
    '9.0': {
        'name': 'Catch rule',
        'patterns': {'special': ['tch']},
        'position': FINAL,  # Only at end after short vowel
        'phoneme': 'CH',
        'word_length': {'min': 5, 'max': 6}
    },
    '11.0': {
        'name': 'Magic E',
        'patterns': {'special': 'VCe'},
        'position': FINAL,  # Silent e is always final
        'word_length': {'min': 4, 'max': 5}
    },
}

def check_pattern_position(word, pattern, required_position):
    """
    Check if a pattern appears in the required position in a word
    
    Args:
        word: The word to check
        pattern: The pattern to look for
        required_position: INITIAL, MEDIAL, FINAL, ANY, etc.
    
    Returns:
        bool: True if pattern is in correct position
    """
    word_lower = word.lower()
    
    if pattern not in word_lower:
        return False
    
    if required_position == ANY:
        return True
    
    if required_position == INITIAL:
        return word_lower.startswith(pattern)
    
    if required_position == FINAL:
        return word_lower.endswith(pattern)
    
    if required_position == MEDIAL:
        # Pattern is not at start or end
        return (pattern in word_lower and 
                not word_lower.startswith(pattern) and 
                not word_lower.endswith(pattern))
    
    if required_position == INITIAL_OR_MEDIAL:
        return not word_lower.endswith(pattern)
    
    if required_position == MEDIAL_OR_FINAL:
        return not word_lower.startswith(pattern)
    
    if required_position == INITIAL_OR_FINAL:
        # Pattern must be at start OR end, not in middle
        return word_lower.startswith(pattern) or word_lower.endswith(pattern)
    
    if required_position == MULTIPLE:
        # For compound words, etc.
        return True
    
    if required_position == VARIOUS:
        # Different patterns have different positions
        return True
    
    return False

def validate_word_for_skill(word, skill_id):
    """
    Validate if a word meets all requirements for a skill
    """
    config = SKILL_CONFIGS.get(skill_id, {})
    
    # Check word length
    word_length = config.get('word_length', {})
    if word_length:
        if not (word_length['min'] <= len(word) <= word_length['max']):
            return False, f"Word length {len(word)} not in range {word_length['min']}-{word_length['max']}"
    
    # Check patterns and positions
    patterns = config.get('patterns', {})
    position = config.get('position')
    position_by_pattern = config.get('position_by_pattern', {})
    
    pattern_found = False
    
    # Check each pattern type
    for pattern_type, pattern_list in patterns.items():
        if isinstance(pattern_list, str):
            pattern_list = [pattern_list]
        
        for pattern in pattern_list:
            if pattern in word.lower():
                # Check position requirement
                if position_by_pattern and pattern in position_by_pattern:
                    # Pattern-specific position
                    required_pos = position_by_pattern[pattern]
                else:
                    # General position for this skill
                    required_pos = position or ANY
                
                if check_pattern_position(word, pattern, required_pos):
                    pattern_found = True
                else:
                    return False, f"Pattern '{pattern}' not in required position '{required_pos}'"
    
    if not pattern_found and patterns:
        return False, "No required patterns found"
    
    return True, "Valid"

# Example usage
if __name__ == "__main__":
    print("POSITION-AWARE PATTERN VALIDATION EXAMPLES")
    print("=" * 60)
    
    # Test cases
    test_cases = [
        # Word, Skill, Expected
        ("stop", "5.3", True),    # 'st' at beginning ✓
        ("best", "5.3", False),   # 'st' at end ✗
        ("went", "6.1", True),    # 'nt' at end ✓
        ("into", "6.1", False),   # 'nt' in middle ✗
        ("chop", "4.1", True),    # 'ch' anywhere ✓
        ("when", "4.4", True),    # 'wh' at beginning ✓
        ("awhile", "4.4", False), # 'wh' in middle ✗
        ("back", "4.5", True),    # 'ck' at end ✓
        ("bucket", "4.5", False), # 'ck' in middle ✗
        ("rain", "18.1", True),   # 'ai' in middle ✓
        ("day", "18.1", True),    # 'ay' at end ✓
        ("pie", "18.5", True),    # 'ie' at end ✓
        ("field", "18.5", False), # 'ie' in middle ✗
        ("light", "18.5", True),  # 'igh' in middle ✓
    ]
    
    for word, skill_id, expected in test_cases:
        valid, reason = validate_word_for_skill(word, skill_id)
        status = "✓" if valid == expected else "✗"
        print(f"{status} {word.ljust(8)} (skill {skill_id}): {reason}")
