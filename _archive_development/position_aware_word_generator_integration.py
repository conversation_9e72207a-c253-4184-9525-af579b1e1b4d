#!/usr/bin/env python3
"""
Integration script to use position-aware CSV with the word generator
"""

import pandas as pd
from csv_config_translator import SkillConfigTranslator
from position_aware_skill_config import check_pattern_position
from decodable_word_generator import DecodableWordGenerator

class PositionAwareWordGenerator(DecodableWordGenerator):
    """Enhanced word generator that uses position requirements from CSV"""
    
    def __init__(self):
        super().__init__()
        # Load the CSV configuration translator
        self.config_translator = SkillConfigTranslator()
        
        # Override skill mappings with CSV data
        self.skill_mappings = self._load_skill_mappings_from_csv()
        
        # Override word length limits with CSV data
        self.word_length_limits = self._load_word_lengths_from_csv()
    
    def _load_skill_mappings_from_csv(self):
        """Load skill mappings from the CSV file"""
        mappings = {}
        
        for _, row in self.config_translator.df.iterrows():
            skill_id = row['skill_id']
            config = self.config_translator.get_skill_config(skill_id)
            
            mapping = {
                'type': row['pattern_type'],
                'name': row['skill_name'],
                'position': row['position'],
                'patterns': config['patterns'],
                'phoneme': config['phoneme_code'],
                'position_by_pattern': config['position_by_pattern']
            }
            
            mappings[skill_id] = mapping
        
        return mappings
    
    def _load_word_lengths_from_csv(self):
        """Load word length limits from the CSV file"""
        limits = {}
        
        for _, row in self.config_translator.df.iterrows():
            skill_id = row['skill_id']
            word_length = self.config_translator.parse_word_length(row['word_length'])
            limits[skill_id] = word_length
        
        return limits
    
    def is_word_decodable(self, word, skill_id):
        """Enhanced decodability check with position requirements"""
        # First, do the standard decodability check
        is_decodable, reason = super().is_word_decodable(word, skill_id)
        
        if not is_decodable:
            return False, reason
        
        # Now check position requirements
        valid, position_reason = self.config_translator.validate_word_with_position(word, skill_id)
        
        if not valid:
            return False, position_reason
        
        return True, "decodable with correct position"
    
    def _get_word_candidates(self, skill_id, skill_type, skill_info):
        """Get word candidates with position filtering"""
        # Get base candidates
        candidates = super()._get_word_candidates(skill_id, skill_type, skill_info)
        
        # Filter by position requirements
        config = self.config_translator.get_skill_config(skill_id)
        if not config:
            return candidates
        
        filtered_candidates = []
        
        for candidate in candidates:
            word = candidate['word']
            pattern = candidate['primary_pattern']
            
            # Get required position for this pattern
            required_position = self.config_translator.get_pattern_position(skill_id, pattern)
            
            # Check if pattern is in correct position
            if check_pattern_position(word, pattern, required_position):
                # Update the position in the candidate
                candidate['pattern_position'] = required_position
                filtered_candidates.append(candidate)
        
        return filtered_candidates
    
    def generate_position_aware_report(self, skill_id):
        """Generate a detailed report for a skill showing position requirements"""
        config = self.config_translator.get_skill_config(skill_id)
        
        if not config:
            print(f"Skill {skill_id} not found in CSV")
            return
        
        print(f"\nPOSITION-AWARE WORD GENERATION REPORT")
        print(f"Skill {skill_id}: {config['name']}")
        print("=" * 60)
        
        print(f"\nPattern Requirements:")
        print(f"  Patterns: {config['patterns']}")
        print(f"  General Position: {config['position']}")
        
        if config['position_by_pattern']:
            print(f"\n  Pattern-Specific Positions:")
            for pattern, position in config['position_by_pattern'].items():
                print(f"    '{pattern}' → {position}")
        
        print(f"\n  Word Length: {config['word_length']['min']}-{config['word_length']['max']} letters")
        
        if config['phoneme_code']:
            print(f"  Target Sound: {config['phoneme_code']} ({config['phoneme_description']})")
        
        # Generate words
        print(f"\nGenerating words...")
        words = self.generate_for_skill(skill_id)
        words = self.filter_words(words, skill_id, max_words=20)
        
        if words:
            print(f"\nExample Words (showing position validation):")
            for word_entry in words[:10]:
                word = word_entry['word']
                pattern = word_entry['primary_pattern']
                position = word_entry['pattern_position']
                
                # Show the word with pattern highlighted
                word_display = word
                if pattern in word.lower():
                    idx = word.lower().index(pattern)
                    word_display = (word[:idx] + 
                                  f"[{word[idx:idx+len(pattern)]}]" + 
                                  word[idx+len(pattern):])
                
                print(f"  {word_display.ljust(15)} pattern '{pattern}' at {position} position")
        else:
            print("\n  No words found matching all requirements")

def demonstrate_position_aware_generation():
    """Demonstrate the position-aware word generation"""
    print("POSITION-AWARE WORD GENERATOR DEMONSTRATION")
    print("=" * 70)
    
    generator = PositionAwareWordGenerator()
    
    # Test skills with different position requirements
    test_skills = [
        '5.3',   # s-blends (initial only)
        '4.5',   # ck digraph (final only)
        '18.1',  # long a teams (various positions)
        '18.5',  # long i teams (pattern-specific)
        '4.1',   # ch digraph (any position)
    ]
    
    for skill_id in test_skills:
        generator.generate_position_aware_report(skill_id)
        print("\n" + "-" * 70)

def test_initial_or_final_position():
    """Test the new INITIAL_OR_FINAL position if any skills use it"""
    print("\n\nTESTING INITIAL_OR_FINAL POSITION")
    print("=" * 50)
    
    # Example: If you have a skill that uses initial_or_final
    # You would add it to your CSV like:
    # skill_id,skill_name,grapheme_patterns,phoneme_requirement,position,...
    # 99.1,s boundaries,s,S,initial_or_final,...
    
    test_words = [
        ("sun", "s"),    # initial - should pass
        ("bus", "s"),    # final - should pass
        ("lesson", "s"), # medial - should fail
        ("this", "s"),   # final - should pass
        ("say", "s"),    # initial - should pass
    ]
    
    print("Testing pattern 's' with INITIAL_OR_FINAL requirement:")
    for word, pattern in test_words:
        result = check_pattern_position(word, pattern, 'initial_or_final')
        status = "✓" if result else "✗"
        
        # Show position
        if word.startswith(pattern):
            position = "initial"
        elif word.endswith(pattern):
            position = "final"
        else:
            position = "medial"
        
        print(f"  {status} '{word}' - s at {position} position")

if __name__ == "__main__":
    # Run the demonstration
    demonstrate_position_aware_generation()
    
    # Test the new position option
    test_initial_or_final_position()
    
    print("\n\n✅ Your CSV format is now fully integrated with position requirements!")
    print("Including support for the new INITIAL_OR_FINAL position option.")
