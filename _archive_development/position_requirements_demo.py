#!/usr/bin/env python3
"""
Example: Using position requirements in word generation
"""

import pandas as pd

def demonstrate_position_usage():
    """Show how to use position requirements"""
    
    print("USING POSITION REQUIREMENTS IN WORD GENERATION")
    print("=" * 70)
    
    # Load the enhanced CSV with position column
    df = pd.read_csv('og_skill_pattern_configurations_with_position.csv')
    
    print("\n1. POSITION VALUES IN THE CSV:")
    print("-" * 40)
    print("• initial - Must be at the beginning (e.g., 'bl' in 'blue')")
    print("• medial - Must be in the middle (e.g., 'ai' in 'rain')")
    print("• final - Must be at the end (e.g., 'ck' in 'back')")
    print("• any - Can appear anywhere (e.g., 'ch' in 'chop', 'much', 'teacher')")
    print("• initial_or_medial - Beginning or middle, not end")
    print("• medial_or_final - Middle or end, not beginning")
    print("• initial_or_final - Beginning or end, not middle (e.g., 's' in 'sun' or 'bus')")
    print("• multiple - For compound words or patterns that repeat")
    print("• various - Different patterns have different positions")
    
    print("\n\n2. EXAMPLES FROM THE CSV:")
    print("-" * 40)
    
    # Show some key examples
    examples = [
        '5.3',   # s-blends - initial only
        '6.1',   # nt ending - final only
        '4.5',   # ck digraph - final only
        '18.1',  # long a teams - various positions
        '18.5'   # long i teams - pattern-specific
    ]
    
    for skill_id in examples:
        row = df[df['skill_id'] == skill_id].iloc[0]
        print(f"\nSkill {skill_id}: {row['skill_name']}")
        print(f"  Patterns: {row['grapheme_patterns']}")
        print(f"  Position: {row['position']}")
        if 'various' in str(row['position']) or 'any' in str(row['position']):
            print(f"  Notes: {row['additional_constraints']}")
    
    print("\n\n3. HOW TO USE IN YOUR GENERATOR:")
    print("-" * 40)
    print("""
def generate_words_for_skill(skill_id):
    # Load configuration
    config_df = pd.read_csv('og_skill_pattern_configurations_with_position.csv')
    skill_config = config_df[config_df['skill_id'] == skill_id].iloc[0]
    
    # Get patterns and position requirement
    patterns = skill_config['grapheme_patterns'].split(', ')
    position = skill_config['position']
    
    # Filter words based on position
    valid_words = []
    for word in word_candidates:
        for pattern in patterns:
            if check_pattern_position(word, pattern, position):
                valid_words.append(word)
                break
    
def check_pattern_position(word, pattern, position):
    word_lower = word.lower()
    
    if pattern not in word_lower:
        return False
        
    if position == 'initial':
        return word_lower.startswith(pattern)
    elif position == 'final':
        return word_lower.endswith(pattern)
    elif position == 'medial':
        return (pattern in word_lower and 
                not word_lower.startswith(pattern) and 
                not word_lower.endswith(pattern))
    elif position == 'any':
        return True
    elif position == 'initial_or_final':
        return (word_lower.startswith(pattern) or 
                word_lower.endswith(pattern))
    # Add other position checks as needed
    """)
    
    print("\n\n4. PATTERN-SPECIFIC POSITIONS:")
    print("-" * 40)
    print("Some skills have different positions for different patterns.")
    print("The 'additional_constraints' column provides details:")
    
    # Show vowel team examples
    vowel_team_examples = df[df['skill_id'].str.startswith('18.')].head(5)
    
    for _, row in vowel_team_examples.iterrows():
        if 'various' in str(row['position']) or pd.notna(row['additional_constraints']):
            print(f"\nSkill {row['skill_id']}: {row['skill_name']}")
            print(f"  Details: {row['additional_constraints']}")
    
    print("\n\n5. REAL EXAMPLES:")
    print("-" * 40)
    
    position_examples = {
        'initial': [
            ('stop', 'st', '5.3', True),
            ('best', 'st', '5.3', False),  # Wrong position!
        ],
        'final': [
            ('back', 'ck', '4.5', True),
            ('bucket', 'ck', '4.5', False),  # Wrong position!
            ('went', 'nt', '6.1', True),
            ('into', 'nt', '6.1', False),  # Wrong position!
        ],
        'medial': [
            ('rain', 'ai', '18.1', True),
            ('aim', 'ai', '18.1', False),  # Wrong position (initial)!
        ],
        'any': [
            ('chop', 'ch', '4.1', True),
            ('much', 'ch', '4.1', True),
            ('teacher', 'ch', '4.1', True),
        ],
        'initial_or_final': [
            ('sun', 's', 'example', True),     # s at beginning ✓
            ('bus', 's', 'example', True),     # s at end ✓
            ('lesson', 's', 'example', False), # s in middle ✗
            ('saw', 's', 'example', True),     # s at beginning ✓
        ]
    }
    
    for position_type, examples in position_examples.items():
        print(f"\n{position_type.upper()} position requirement:")
        for word, pattern, skill, expected in examples:
            status = "✓" if expected else "✗"
            reason = "correct position" if expected else "wrong position"
            print(f"  {status} '{word}' has '{pattern}' - {reason}")

def create_position_reference():
    """Create a quick reference for pattern positions"""
    
    position_ref = pd.DataFrame([
        # Initial only patterns
        {'pattern': 'bl, cl, fl, gl, pl, sl', 'position': 'initial', 'example': 'blue, clap, flag'},
        {'pattern': 'br, cr, dr, fr, gr, pr, tr', 'position': 'initial', 'example': 'bring, cry, drop'},
        {'pattern': 'sc, sk, sm, sn, sp, st, sw', 'position': 'initial', 'example': 'scan, skip, stop'},
        {'pattern': 'scr, spr, str, spl, squ, thr', 'position': 'initial', 'example': 'scrap, spring, strong'},
        {'pattern': 'wh', 'position': 'initial', 'example': 'when, what, why'},
        
        # Final only patterns
        {'pattern': 'ck', 'position': 'final', 'example': 'back, duck, stick'},
        {'pattern': 'tch', 'position': 'final', 'example': 'catch, watch, match'},
        {'pattern': 'dge', 'position': 'final', 'example': 'bridge, judge, edge'},
        {'pattern': 'nt, nd, nk, lt, ld, lf, lk, lm, lp, mp, ng, sk, st, ft', 'position': 'final', 'example': 'went, hand, think'},
        {'pattern': 'ay', 'position': 'final', 'example': 'day, play, stay'},
        {'pattern': 'ey', 'position': 'final', 'example': 'key, they, money'},
        {'pattern': 'oy', 'position': 'final', 'example': 'boy, toy, enjoy'},
        {'pattern': 'ow (long o)', 'position': 'final', 'example': 'grow, snow, yellow'},
        
        # Medial patterns
        {'pattern': 'ai', 'position': 'medial', 'example': 'rain, train, sail'},
        {'pattern': 'ea', 'position': 'medial', 'example': 'seat, bread, steak'},
        {'pattern': 'oa', 'position': 'medial', 'example': 'boat, road, soap'},
        {'pattern': 'igh', 'position': 'medial', 'example': 'light, night, sight'},
        {'pattern': 'oo', 'position': 'medial', 'example': 'moon, book, food'},
        
        # Any position patterns
        {'pattern': 'ch, sh, th', 'position': 'any', 'example': 'chop/much/teacher, ship/fish/fishing'},
        {'pattern': 'ar, er, ir, or, ur', 'position': 'any', 'example': 'car/start/dollar'},
    ])
    
    # Save reference
    position_ref.to_csv('pattern_position_reference.csv', index=False)
    print("\n\nCreated pattern_position_reference.csv for quick lookup!")

if __name__ == "__main__":
    demonstrate_position_usage()
    create_position_reference()
