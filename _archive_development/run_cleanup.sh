#!/bin/bash

echo "WORD-GENERATOR DIRECTORY CLEANUP"
echo "================================"
echo ""
echo "This will clean up temporary files and organize the directory."
echo ""
echo "FILES TO BE ARCHIVED:"
echo "- analyze_corrections.py (temporary analysis)"
echo "- test_corrections.py (temporary test)"
echo "- word_list_corrector.py (already run)"
echo "- run_corrector.sh (already run)"
echo "- analyze_book6.py (temporary)"
echo "- integrate_book6.py (temporary)"
echo "- copy_heart_words.sh (temporary)"
echo "- create_master_wordlist.sh (keeping .py version)"
echo ""
echo "DIRECTORIES TO BE REORGANIZED:"
echo "- word_lists/ (original) → archived"
echo "- word_lists_corrected/ → renamed to word_lists/"
echo ""
echo "FILES/DIRS TO KEEP:"
echo "✓ orton_gillingham_generator.py (main generator)"
echo "✓ og_web_app.py (web interface)"
echo "✓ decodable_passage_generator.py"
echo "✓ word_search_generator.py"
echo "✓ create_master_wordlist.py"
echo "✓ master_wordlist_*.csv files"
echo "✓ heart_words/ directory"
echo "✓ reports/ directory"
echo "✓ word_lists/ (the corrected ones)"
echo ""
read -p "Proceed with cleanup? (y/n) " -n 1 -r
echo ""
if [[ $REPLY =~ ^[Yy]$ ]]
then
    python3 cleanup_directory.py
else
    echo "Cleanup cancelled."
fi
