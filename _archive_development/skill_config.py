#!/usr/bin/env python3
"""
Simplified configuration module for O-G word generator
This can be imported and used directly in your word generators
"""

# Master skill configuration dictionary
SKILL_CONFIGS = {
    # CVC Skills
    '1.1': {
        'name': 'CVC short a',
        'patterns': {'vowel': 'a'},
        'phoneme': 'AE',  # Short a
        'word_length': {'min': 3, 'max': 3},
        'constraints': ['no_blends', 'no_digraphs', 'no_vowel_teams']
    },
    '1.2': {
        'name': 'CVC short e',
        'patterns': {'vowel': 'e'},
        'phoneme': 'EH',  # Short e
        'word_length': {'min': 3, 'max': 3},
        'constraints': ['no_blends', 'no_digraphs', 'no_vowel_teams']
    },
    '1.3': {
        'name': 'CVC short i',
        'patterns': {'vowel': 'i'},
        'phoneme': 'IH',  # Short i
        'word_length': {'min': 3, 'max': 3},
        'constraints': ['no_blends', 'no_digraphs', 'no_vowel_teams']
    },
    '1.4': {
        'name': 'CVC short o',
        'patterns': {'vowel': 'o'},
        'phoneme': 'AA',  # Short o
        'word_length': {'min': 3, 'max': 3},
        'constraints': ['no_blends', 'no_digraphs', 'no_vowel_teams']
    },
    '1.5': {
        'name': 'CVC short u',
        'patterns': {'vowel': 'u'},
        'phoneme': 'AH',  # Short u
        'word_length': {'min': 3, 'max': 3},
        'constraints': ['no_blends', 'no_digraphs', 'no_vowel_teams']
    },
    
    # CV Open Syllables
    '2.1': {
        'name': 'CV long e',
        'patterns': {'specific_words': ['be', 'he', 'me', 'we', 'she']},
        'phoneme': 'IY',  # Long e
        'word_length': {'min': 2, 'max': 3}
    },
    '2.2': {
        'name': 'CV long o',
        'patterns': {'specific_words': ['go', 'no', 'so']},
        'phoneme': 'OW',  # Long o
        'word_length': {'min': 2, 'max': 2}
    },
    '2.3': {
        'name': 'CV long i',
        'patterns': {'specific_words': ['I', 'hi']},
        'phoneme': 'AY',  # Long i
        'word_length': {'min': 1, 'max': 2}
    },
    
    # Initial Blends
    '5.1': {
        'name': 'l-blends',
        'patterns': {'initial_blends': ['bl', 'cl', 'fl', 'gl', 'pl', 'sl']},
        'word_length': {'min': 4, 'max': 5},
        'constraints': ['no_vowel_teams', 'no_magic_e']
    },
    '5.2': {
        'name': 'r-blends',
        'patterns': {'initial_blends': ['br', 'cr', 'dr', 'fr', 'gr', 'pr', 'tr']},
        'word_length': {'min': 4, 'max': 5},
        'constraints': ['no_vowel_teams', 'no_magic_e']
    },
    '5.3': {
        'name': 's-blends',
        'patterns': {'initial_blends': ['sc', 'sk', 'sm', 'sn', 'sp', 'st', 'sw']},
        'word_length': {'min': 4, 'max': 5},
        'constraints': ['no_vowel_teams', 'no_magic_e']
    },
    
    # Vowel Teams - WITH PHONEME REQUIREMENTS
    '18.1': {
        'name': 'Long a teams',
        'patterns': {'vowel_teams': ['ai', 'ay', 'ei', 'eigh']},
        'phoneme': 'EY',  # Long a sound
        'word_length': {'min': 4, 'max': 8},
        'phoneme_check': True  # Must verify sound
    },
    '18.2': {
        'name': 'Long e teams',
        'patterns': {'vowel_teams': ['ea', 'ee', 'ie', 'ey']},
        'phoneme': 'IY',  # Long e sound
        'word_length': {'min': 4, 'max': 8},
        'phoneme_check': True
    },
    '18.3': {
        'name': 'Long o teams',
        'patterns': {'vowel_teams': ['oa', 'oe', 'ow']},
        'phoneme': 'OW',  # Long o sound
        'word_length': {'min': 4, 'max': 8},
        'phoneme_check': True
    },
    '18.4': {
        'name': 'ow sound',
        'patterns': {'vowel_teams': ['ou', 'ow']},
        'phoneme': 'AW',  # /ow/ as in cow
        'word_length': {'min': 4, 'max': 8},
        'phoneme_check': True
    },
    '18.5': {
        'name': 'Long i teams',
        'patterns': {'vowel_teams': ['ie', 'igh']},
        'phoneme': 'AY',  # Long i sound
        'word_length': {'min': 4, 'max': 8},
        'phoneme_check': True
    },
    '18.6': {
        'name': 'Long u teams',
        'patterns': {'vowel_teams': ['ue', 'ew', 'eu']},
        'phonemes': ['UW', 'YUW'],  # Long u sounds
        'word_length': {'min': 4, 'max': 8},
        'phoneme_check': True
    },
    '18.7': {
        'name': 'Long oo',
        'patterns': {'vowel_teams': ['oo']},
        'phoneme': 'UW',  # Long oo as in moon
        'word_length': {'min': 4, 'max': 8},
        'phoneme_check': True
    },
    '18.8': {
        'name': 'Short oo',
        'patterns': {'vowel_teams': ['oo']},
        'phoneme': 'UH',  # Short oo as in book
        'word_length': {'min': 4, 'max': 8},
        'phoneme_check': True
    },
}

# CMU Phoneme reference
CMU_PHONEMES = {
    # Vowels
    'AA': 'odd',     # Short o
    'AE': 'at',      # Short a
    'AH': 'hut',     # Short u
    'AO': 'ought',   # /aw/
    'AW': 'cow',     # /ow/
    'AY': 'hide',    # Long i
    'EH': 'Ed',      # Short e
    'ER': 'hurt',    # /er/
    'EY': 'ate',     # Long a
    'IH': 'it',      # Short i
    'IY': 'eat',     # Long e
    'OW': 'oat',     # Long o
    'OY': 'toy',     # /oy/
    'UH': 'hood',    # Short oo
    'UW': 'two',     # Long oo/u
    'YUW': 'cute',   # Long u with y-glide
    
    # Consonants
    'B': 'be',
    'CH': 'cheese',
    'D': 'dee',
    'DH': 'thee',    # Voiced th
    'F': 'fee',
    'G': 'green',
    'HH': 'he',
    'JH': 'gee',     # J or soft g
    'K': 'key',
    'L': 'lee',
    'M': 'me',
    'N': 'knee',
    'NG': 'ping',
    'P': 'pee',
    'R': 'read',
    'S': 'sea',
    'SH': 'she',
    'T': 'tea',
    'TH': 'theta',   # Unvoiced th
    'V': 'vee',
    'W': 'we',
    'Y': 'yield',
    'Z': 'zee',
    'ZH': 'seizure'
}

def get_skill_config(skill_id):
    """Get configuration for a specific skill"""
    return SKILL_CONFIGS.get(skill_id, {})

def get_phoneme_requirement(skill_id):
    """Get phoneme requirement(s) for a skill"""
    config = SKILL_CONFIGS.get(skill_id, {})
    if 'phonemes' in config:
        return config['phonemes']
    elif 'phoneme' in config:
        return [config['phoneme']]
    return []

def get_word_length_limits(skill_id):
    """Get word length limits for a skill"""
    config = SKILL_CONFIGS.get(skill_id, {})
    return config.get('word_length', {'min': 3, 'max': 10})

def needs_phoneme_check(skill_id):
    """Check if skill requires phoneme verification"""
    config = SKILL_CONFIGS.get(skill_id, {})
    return config.get('phoneme_check', False)

def get_pattern_constraints(skill_id):
    """Get pattern constraints for a skill"""
    config = SKILL_CONFIGS.get(skill_id, {})
    return config.get('constraints', [])

# Example usage:
if __name__ == "__main__":
    # Show configuration for skill 18.5
    config = get_skill_config('18.5')
    print(f"Skill 18.5: {config['name']}")
    print(f"Patterns: {config['patterns']}")
    print(f"Required phoneme: {config['phoneme']}")
    print(f"Word length: {config['word_length']}")
    print(f"Needs phoneme check: {needs_phoneme_check('18.5')}")
