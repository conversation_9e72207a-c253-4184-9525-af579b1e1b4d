# Syllabication Pattern Integration Notes

## How Syllabication Patterns Differ from Phoneme Patterns

### Key Differences:
1. **Structural vs. Sound-based**: Syllabication looks at letter patterns (V, C, CV) not phonemes
2. **Division vs. Generation**: These patterns help divide existing words, not generate new ones
3. **No phoneme requirements**: The 'required_phonemes' field is set to 'structural' as a flag

### Pattern Notation:
- **V** = Vowel (a, e, i, o, u, sometimes y)
- **C** = Consonant (all other letters)
- **/** = Division point
- **|** = Alternative notation for division point in pattern

### How to Use in Word Generator:

#### Option 1: Teaching Sequence Only
Use these entries to track when syllabication is taught, but don't use for word generation:
- Students learn syllabication patterns after basic phonics
- Words for practice should demonstrate the pattern clearly
- Focus on words students can already decode

#### Option 2: Pattern Recognition
For each word in your word list:
1. Identify vowels and consonants
2. Look for the structural patterns (VCCV, VCV, etc.)
3. Apply the division rules
4. This helps with encoding multi-syllable words

### Special Considerations:

1. **<PERSON> vs. Camel (VCV patterns)**:
   - Same structure, different division points
   - Tiger: V/CV (open first syllable) - try this first
   - Camel: VC/V (closed first syllable) - if <PERSON> doesn't make a real word

2. **Ostrich vs. Hamster (VCCCV patterns)**:
   - Both have 3 consonants
   - Depends on where blends/digraphs are
   - Keep blends and digraphs together

3. **Order of Teaching**:
   - Start with Compound and C+le (most consistent)
   - Then VC/CV (Rabbit - most common)
   - Then V/CV and VC/V (requires trying both)
   - Finally complex 3-consonant patterns

### Integration with Phonics CSV:
The 'graphemes' column contains the structural pattern (e.g., "VC/CV") rather than specific letter combinations. This allows you to:
- See the teaching sequence
- Understand prerequisites
- Generate appropriate practice words
- But requires different parsing logic than phoneme-based patterns