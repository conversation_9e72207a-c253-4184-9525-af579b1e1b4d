#!/usr/bin/env python3
"""
Test script to demonstrate the CSV-based generator
Creates a small test CSV to show functionality
"""

import pandas as pd
import os

# Create a test CSV with a few sample rules
test_data = [
    {
        'skill_id': '1.1',
        'skill_name': 'CVC short a',
        'pattern_type': 'CVC',
        'graphemes': 'a',
        'required_phonemes': 'AE',
        'phoneme_description': 'short /a/ as in cat',
        'position_constraints': 'medial (vowel position)',
        'word_length': '3',
        'word_structure': '3-letter CVC only',
        'special_rules': 'No blends, digraphs, or vowel teams allowed',
        'examples': 'cat, hat, mat, sat, bat',
        'non_examples': 'cake (magic e), rain (vowel team), chat (digraph)',
        'notes': ''
    },
    {
        'skill_id': '4.1',
        'skill_name': 'ch digraph',
        'pattern_type': 'digraph',
        'graphemes': 'ch',
        'required_phonemes': 'CH',
        'phoneme_description': '/ch/ as in chip',
        'position_constraints': 'initial or final',
        'word_length': '3-4',
        'word_structure': 'any',
        'special_rules': 'Two letters make one sound',
        'examples': 'chip, chat, much, lunch, chop',
        'non_examples': 'character (k sound), chef (sh sound)',
        'notes': ''
    },
    {
        'skill_id': '18.5',
        'skill_name': 'Long i teams',
        'pattern_type': 'vowel team',
        'graphemes': 'ie, igh',
        'required_phonemes': 'AY',
        'phoneme_description': 'long /i/ sound',
        'position_constraints': 'any',
        'word_length': '3-6',
        'word_structure': 'any',
        'special_rules': 'Must produce long i sound',
        'examples': 'pie, tie, high, night, bright',
        'non_examples': 'field (long e), friend (short e)',
        'notes': ''
    },
    {
        'skill_id': '24',
        'skill_name': '1-1-1 doubling',
        'pattern_type': 'spelling rule',
        'graphemes': 'double final consonant',
        'required_phonemes': 'various',
        'phoneme_description': 'double before suffix',
        'position_constraints': 'any position',
        'word_length': '3-12',
        'word_structure': 'any',
        'special_rules': 'When the base word has one syllable, one vowel before the final consonant, one final consonant, then you double the final consonant.',
        'examples': 'hop/hopping, sit/sitting, run/running, swim/swimming',
        'non_examples': '',
        'notes': 'CVC pattern doubles final consonant when adding ing'
    }
]

# Save test CSV
test_csv_path = '/Users/<USER>/word-generator/test_phonics_rules.csv'
df = pd.DataFrame(test_data)
df.to_csv(test_csv_path, index=False)
print(f"Created test CSV at {test_csv_path}")

# Now test the generator
print("\nTesting CSV-based generator...")
print("=" * 60)

# Import and run the generator
try:
    from csv_based_phoneme_generator import CSVBasedPhonemeGenerator
    
    # Initialize with test CSV
    generator = CSVBasedPhonemeGenerator(test_csv_path)
    
    # Test each skill
    for skill_id in ['1.1', '4.1', '18.5', '24']:
        print(f"\n{'='*50}")
        words = generator.generate_words_for_skill(skill_id, max_words=5)
        
        if words:
            print(f"\nTop 5 words:")
            for i, word_data in enumerate(words[:5]):
                if 'base_word' in word_data:
                    print(f"  {i+1}. {word_data['word']} (from {word_data['base_word']})")
                else:
                    print(f"  {i+1}. {word_data['word']}")
        else:
            print("  No words generated")
            
except Exception as e:
    print(f"Error running generator: {e}")
    import traceback
    traceback.print_exc()

# Clean up test file
if os.path.exists(test_csv_path):
    os.remove(test_csv_path)
    print(f"\nCleaned up test CSV")