#!/usr/bin/env python3
"""
Test script for the new INITIAL_OR_FINAL position option
"""

from position_aware_skill_config import check_pattern_position, INITIAL_OR_FINAL

def test_initial_or_final_position():
    """Test the INITIAL_OR_FINAL position requirement"""
    
    print("TESTING INITIAL_OR_FINAL POSITION")
    print("=" * 50)
    print("\nThis position allows patterns at the beginning OR end,")
    print("but NOT in the middle of words.\n")
    
    # Test cases for different patterns
    test_cases = [
        # Pattern 's' - common at both positions
        ("Pattern: 's'", [
            ("sun", "s", True, "s at beginning"),
            ("bus", "s", True, "s at end"),
            ("lesson", "s", False, "s in middle"),
            ("stops", "s", True, "s at end"),
            ("say", "s", True, "s at beginning"),
            ("basic", "s", False, "s in middle"),
        ]),
        
        # Pattern 'th' - could appear at either end
        ("Pattern: 'th'", [
            ("the", "th", True, "th at beginning"),
            ("bath", "th", True, "th at end"),
            ("mother", "th", False, "th in middle"),
            ("think", "th", True, "th at beginning"),
            ("with", "th", True, "th at end"),
            ("nothing", "th", False, "th in middle"),
        ]),
        
        # Pattern 'ed' - primarily final but could be initial in some names
        ("Pattern: 'ed'", [
            ("played", "ed", True, "ed at end"),
            ("jumped", "ed", True, "ed at end"),
            ("Edward", "ed", True, "ed at beginning"),
            ("bedroom", "ed", False, "ed in middle"),
            ("needed", "ed", True, "ed at end"),
            ("education", "ed", True, "ed at beginning"),
        ]),
    ]
    
    for pattern_desc, cases in test_cases:
        print(f"\n{pattern_desc}")
        print("-" * 40)
        
        for word, pattern, expected, description in cases:
            result = check_pattern_position(word, pattern, INITIAL_OR_FINAL)
            status = "✓" if result == expected else "✗"
            result_text = "PASS" if result == expected else "FAIL"
            
            print(f"{status} '{word.ljust(10)}' - {description.ljust(20)} [{result_text}]")
    
    print("\n\nPotential use cases for INITIAL_OR_FINAL:")
    print("-" * 40)
    print("• Consonant patterns that commonly appear at word boundaries")
    print("• Morphemes that can be prefixes or suffixes")
    print("• Letter combinations used in both initial and final positions")
    print("• Patterns for advanced spelling rules")

def demonstrate_skill_config():
    """Show how to add a skill with INITIAL_OR_FINAL position"""
    
    print("\n\nEXAMPLE SKILL CONFIGURATION:")
    print("=" * 50)
    print("""
# Example: Adding a skill with INITIAL_OR_FINAL position
'99.1': {
    'name': 's at word boundaries',
    'patterns': {'consonant': ['s']},
    'position': INITIAL_OR_FINAL,  # Only at start or end
    'word_length': {'min': 3, 'max': 6},
    'constraints': ['not_plural_s']  # Avoid confusion with plural -s
}

# Another example: 'ph' digraph (phone, graph)
'99.2': {
    'name': 'ph digraph boundaries',
    'patterns': {'digraphs': ['ph']},
    'position': INITIAL_OR_FINAL,  # photo, graph
    'phoneme': 'F',
    'word_length': {'min': 4, 'max': 8}
}
""")

if __name__ == "__main__":
    test_initial_or_final_position()
    demonstrate_skill_config()
    
    print("\n\n✅ INITIAL_OR_FINAL position has been successfully added!")
    print("You can now use it in your skill configurations.")
