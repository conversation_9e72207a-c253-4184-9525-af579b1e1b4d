#!/usr/bin/env python3
"""Quick script to check CMU dictionary size"""

import nltk

try:
    # Load CMU dictionary
    cmu_dict = nltk.corpus.cmudict.dict()
    
    # Count entries
    total_entries = len(cmu_dict)
    
    # Count total pronunciations (some words have multiple)
    total_pronunciations = sum(len(pronunciations) for pronunciations in cmu_dict.values())
    
    print(f"CMU Pronouncing Dictionary Statistics:")
    print(f"=====================================")
    print(f"Total unique words: {total_entries:,}")
    print(f"Total pronunciations: {total_pronunciations:,}")
    print(f"Average pronunciations per word: {total_pronunciations/total_entries:.2f}")
    
    # Show some example entries
    print(f"\nExample entries:")
    examples = list(cmu_dict.items())[:5]
    for word, pronunciations in examples:
        print(f"  {word}: {pronunciations}")
        
except Exception as e:
    print(f"Error loading CMU dictionary: {e}")
    print("You may need to run: python download_nltk_data.py")