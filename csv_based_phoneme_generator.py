#!/usr/bin/env python3
"""
CSV-Based Phoneme-Aware Decodable Word Generator for Orton-Gillingham

This generator reads all patterns from a CSV file instead of hard-coding them.
It handles:
1. Phoneme-based patterns (generate words matching sounds)
2. Transformation rules (modify existing words)
3. Structural patterns (syllabication)
"""

import nltk
import pandas as pd
import os
import re
from collections import defaultdict
from typing import List, Dict, Tuple, Optional

class CSVBasedPhonemeGenerator:
    def __init__(self, csv_path: str = 'updated_phonics_master_rules_complete.csv'):
        print("Initializing CSV-Based Phoneme-Aware Word Generator...")
        
        # Load the rules from CSV
        self.rules_df = pd.read_csv(csv_path)
        print(f"Loaded {len(self.rules_df)} phonics rules from CSV")
        
        # Load CMU dictionary
        self.cmu_dict = nltk.corpus.cmudict.dict()
        
        # Load frequency data
        self.freq_dist = self._load_frequency_data()
        
        # Parse rules into structured format
        self.rules_by_skill = self._parse_rules()
        
        # Load heart words
        self.heart_words_by_skill = self._load_heart_words()
        
        # Load existing word lists for transformation rules
        self.base_word_lists = self._load_base_word_lists()
        
        print("Initialization complete!")
    
    def _load_frequency_data(self):
        """Load word frequency from Brown corpus"""
        brown_words = nltk.corpus.brown.words()
        return nltk.FreqDist(word.lower() for word in brown_words)
    
    def _parse_rules(self) -> Dict[str, Dict]:
        """Parse CSV rules into structured format"""
        rules = {}
        
        for _, row in self.rules_df.iterrows():
            skill_id = str(row['skill_id'])
            
            # Parse graphemes (handle multiple variants)
            graphemes = []
            if pd.notna(row['graphemes']):
                # Split by comma or semicolon
                raw_graphemes = str(row['graphemes'])
                if ';' in raw_graphemes:
                    graphemes = [g.strip() for g in raw_graphemes.split(';')]
                else:
                    graphemes = [g.strip() for g in raw_graphemes.split(',')]
            
            # Parse phonemes (handle multiple variants)
            phonemes = []
            if pd.notna(row['required_phonemes']):
                raw_phonemes = str(row['required_phonemes'])
                # Handle "or" separator
                if ' or ' in raw_phonemes.lower():
                    phonemes = [p.strip() for p in raw_phonemes.split(' or ')]
                elif ',' in raw_phonemes:
                    phonemes = [p.strip() for p in raw_phonemes.split(',')]
                else:
                    phonemes = [raw_phonemes.strip()]
            
            # Structure the rule
            rules[skill_id] = {
                'name': row['skill_name'],
                'pattern_type': row['pattern_type'],
                'graphemes': graphemes,
                'phonemes': phonemes,
                'phoneme_desc': row.get('phoneme_description', ''),
                'position': row.get('position_constraints', 'any'),
                'word_length': self._parse_word_length(row.get('word_length', '')),
                'structure': row.get('word_structure', ''),
                'special_rules': row.get('special_rules', ''),
                'examples': row.get('examples', ''),
                'non_examples': row.get('non_examples', ''),
                'notes': row.get('notes', '')
            }
        
        return rules
    
    def _parse_word_length(self, length_str):
        """Parse word length constraints"""
        if pd.isna(length_str) or str(length_str) == 'nan':
            return None
        
        length_str = str(length_str)
        
        # Handle ranges like "3-5"
        if '-' in length_str:
            parts = length_str.split('-')
            try:
                return (int(parts[0]), int(parts[1]))
            except:
                return None
        
        # Handle single numbers
        try:
            length = int(length_str)
            return (length, length)
        except:
            return None
    
    def _load_heart_words(self):
        """Load heart words by skill"""
        heart_words = defaultdict(set)
        heart_words_path = '/Users/<USER>/word-generator/heart_words/heart_words_reference_by_skill.csv'
        
        if os.path.exists(heart_words_path):
            df = pd.read_csv(heart_words_path)
            for _, row in df.iterrows():
                word = row['word'].lower()
                skill = str(row['skill_introduction'])
                heart_words[skill].add(word)
        
        return heart_words
    
    def _load_base_word_lists(self):
        """Load existing word lists for transformation rules"""
        base_words = defaultdict(list)
        word_lists_dir = '/Users/<USER>/word-generator/word_lists/'
        
        # Map skills to their word lists
        skill_to_file = {
            '1.1': 'skill_1.1_CVC_short_a.csv',
            '1.2': 'skill_1.2_CVC_short_e.csv',
            '1.3': 'skill_1.3_CVC_short_i.csv',
            '1.4': 'skill_1.4_CVC_short_o.csv',
            '1.5': 'skill_1.5_CVC_short_u.csv',
            '11.0': 'skill_11.0_Magic_E.csv'
        }
        
        for skill, filename in skill_to_file.items():
            filepath = os.path.join(word_lists_dir, filename)
            if os.path.exists(filepath):
                df = pd.read_csv(filepath)
                if 'word' in df.columns:
                    base_words[skill] = df['word'].tolist()
        
        return base_words
    
    def get_skill_sequence(self) -> List[str]:
        """Get ordered list of skills from CSV"""
        return self.rules_df['skill_id'].astype(str).tolist()
    
    def generate_words_for_skill(self, skill_id: str, max_words: int = 50) -> List[Dict]:
        """Generate words for any skill based on its pattern type"""
        
        if skill_id not in self.rules_by_skill:
            print(f"Skill {skill_id} not found in rules")
            return []
        
        rule = self.rules_by_skill[skill_id]
        pattern_type = rule['pattern_type']
        
        print(f"\nGenerating words for skill {skill_id}: {rule['name']}")
        print(f"Pattern type: {pattern_type}")
        
        # Route to appropriate generator based on pattern type
        if pattern_type == 'CVC':
            return self.generate_cvc_words(skill_id, rule)
        elif pattern_type == 'vowel team':
            return self.generate_vowel_team_words(skill_id, rule)
        elif pattern_type == 'r-controlled':
            return self.generate_r_controlled_words(skill_id, rule)
        elif pattern_type in ['initial blend', 'final blend', 'digraph']:
            return self.generate_blend_digraph_words(skill_id, rule)
        elif pattern_type in ['spelling rule', 'plural rule']:
            return self.generate_transformation_words(skill_id, rule)
        elif pattern_type == 'syllabication':
            return self.identify_syllabication_patterns(skill_id, rule)
        else:
            # Generic pattern matching
            return self.generate_pattern_words(skill_id, rule)
    
    def generate_cvc_words(self, skill_id: str, rule: Dict) -> List[Dict]:
        """Generate CVC words with correct vowel phoneme"""
        candidates = []
        
        # Get the target vowel and phoneme
        if not rule['graphemes'] or not rule['phonemes']:
            return []
        
        target_vowel = rule['graphemes'][0]  # e.g., 'a'
        target_phoneme = rule['phonemes'][0]  # e.g., 'AE'
        
        print(f"Looking for CVC words with '{target_vowel}' making /{target_phoneme}/ sound")
        
        for word, pronunciations in self.cmu_dict.items():
            # Check word length constraint
            if rule['word_length'] and not (rule['word_length'][0] <= len(word) <= rule['word_length'][1]):
                continue
            
            # Check CVC structure
            if len(word) == 3 and word[0] not in 'aeiou' and word[1] == target_vowel and word[2] not in 'aeiou':
                # Check phoneme
                for pron in pronunciations:
                    if len(pron) == 3 and target_phoneme in pron[1]:
                        candidates.append({
                            'word': word,
                            'skill_id': skill_id,
                            'pattern': target_vowel,
                            'phoneme': target_phoneme
                        })
                        break
        
        # Sort by frequency and return top words
        candidates.sort(key=lambda x: self.freq_dist[x['word']], reverse=True)
        return candidates[:50]
    
    def generate_vowel_team_words(self, skill_id: str, rule: Dict) -> List[Dict]:
        """Generate words with vowel teams that make the correct sound"""
        candidates = []
        patterns = rule['graphemes']
        expected_phonemes = rule['phonemes']
        
        print(f"Vowel teams: {patterns}")
        print(f"Expected sounds: {expected_phonemes}")
        
        for pattern in patterns:
            pattern_candidates = 0
            for word in self.cmu_dict.keys():
                if pattern in word:
                    # Check word length
                    if rule['word_length'] and not (rule['word_length'][0] <= len(word) <= rule['word_length'][1]):
                        continue
                    
                    # Check position constraints
                    if not self._check_position_constraint(word, pattern, rule['position']):
                        continue
                    
                    # Check phoneme
                    if self._check_phoneme_match(word, expected_phonemes):
                        candidates.append({
                            'word': word,
                            'skill_id': skill_id,
                            'pattern': pattern,
                            'phoneme_verified': True
                        })
                        pattern_candidates += 1
            
            print(f"  {pattern}: found {pattern_candidates} words")
        
        # Sort and limit
        candidates.sort(key=lambda x: self.freq_dist[x['word']], reverse=True)
        return candidates[:50]
    
    def generate_blend_digraph_words(self, skill_id: str, rule: Dict) -> List[Dict]:
        """Generate words with blends or digraphs"""
        candidates = []
        patterns = rule['graphemes']
        
        for pattern in patterns:
            for word in self.cmu_dict.keys():
                # Check if pattern is present
                pattern_found = False
                if rule['position'] == 'initial only' and word.startswith(pattern):
                    pattern_found = True
                elif rule['position'] == 'final only' and word.endswith(pattern):
                    pattern_found = True
                elif rule['position'] == 'any' and pattern in word:
                    pattern_found = True
                
                if pattern_found:
                    # Check word length
                    if rule['word_length'] and not (rule['word_length'][0] <= len(word) <= rule['word_length'][1]):
                        continue
                    
                    candidates.append({
                        'word': word,
                        'skill_id': skill_id,
                        'pattern': pattern
                    })
        
        # Sort and limit
        candidates.sort(key=lambda x: self.freq_dist[x['word']], reverse=True)
        return candidates[:50]
    
    def generate_transformation_words(self, skill_id: str, rule: Dict) -> List[Dict]:
        """Generate words by applying transformation rules"""
        candidates = []
        
        # Determine which transformation to apply
        if skill_id == '24':  # 1-1-1 doubling
            base_words = []
            for cvc_skill in ['1.1', '1.2', '1.3', '1.4', '1.5']:
                base_words.extend(self.base_word_lists.get(cvc_skill, []))
            
            for word in base_words:
                if self._is_cvc_pattern(word):
                    candidates.append({
                        'word': word + word[-1] + 'ing',
                        'base_word': word,
                        'skill_id': skill_id,
                        'transformation': '1-1-1 doubling'
                    })
        
        elif skill_id == '25':  # E-dropping
            base_words = self.base_word_lists.get('11.0', [])  # Magic E words
            
            for word in base_words:
                if word.endswith('e'):
                    candidates.append({
                        'word': word[:-1] + 'ing',
                        'base_word': word,
                        'skill_id': skill_id,
                        'transformation': 'e-dropping'
                    })
        
        elif skill_id == '27.1':  # Regular plural -s
            # Use common nouns
            for word in ['cat', 'dog', 'book', 'car', 'toy', 'desk', 'pen', 'hat']:
                candidates.append({
                    'word': word + 's',
                    'base_word': word,
                    'skill_id': skill_id,
                    'transformation': 'plural -s'
                })
        
        # Add more transformation rules as needed
        
        return candidates[:50]
    
    def identify_syllabication_patterns(self, skill_id: str, rule: Dict) -> List[Dict]:
        """Identify words that follow syllabication patterns"""
        candidates = []
        pattern = rule['graphemes'][0] if rule['graphemes'] else ''
        
        # This would need more sophisticated syllable analysis
        # For now, return examples from the rule
        if rule['examples']:
            examples = [ex.strip() for ex in rule['examples'].split(',')]
            for ex in examples:
                if '/' in ex:
                    candidates.append({
                        'word': ex.replace('/', ''),
                        'syllables': ex,
                        'skill_id': skill_id,
                        'pattern': pattern
                    })
        
        return candidates
    
    def generate_pattern_words(self, skill_id: str, rule: Dict) -> List[Dict]:
        """Generic pattern matching for other pattern types"""
        candidates = []
        patterns = rule['graphemes']
        
        for pattern in patterns:
            # Skip structural patterns
            if pattern in ['structural', 'various', 'any']:
                continue
                
            for word in self.cmu_dict.keys():
                if pattern in word:
                    # Check word length
                    if rule['word_length'] and not (rule['word_length'][0] <= len(word) <= rule['word_length'][1]):
                        continue
                    
                    candidates.append({
                        'word': word,
                        'skill_id': skill_id,
                        'pattern': pattern
                    })
        
        # Sort and limit
        candidates.sort(key=lambda x: self.freq_dist[x['word']], reverse=True)
        return candidates[:50]
    
    def _check_position_constraint(self, word: str, pattern: str, position: str) -> bool:
        """Check if pattern appears in the correct position"""
        if position == 'any' or position == 'any position':
            return True
        elif position == 'initial' or position == 'initial only':
            return word.startswith(pattern)
        elif position == 'final' or position == 'final only':
            return word.endswith(pattern)
        elif position == 'medial':
            return pattern in word and not word.startswith(pattern) and not word.endswith(pattern)
        else:
            return True
    
    def _check_phoneme_match(self, word: str, expected_phonemes: List[str]) -> bool:
        """Check if word contains expected phonemes"""
        if word.lower() not in self.cmu_dict:
            return False
        
        for pron in self.cmu_dict[word.lower()]:
            pron_str = ' '.join(pron)
            for phoneme in expected_phonemes:
                if phoneme in pron_str:
                    return True
        
        return False
    
    def _is_cvc_pattern(self, word: str) -> bool:
        """Check if word follows CVC pattern"""
        return (len(word) == 3 and 
                word[0] not in 'aeiou' and 
                word[1] in 'aeiou' and 
                word[2] not in 'aeiou')
    
    def export_to_csv(self, words: List[Dict], skill_id: str, filename: str):
        """Export generated words to CSV"""
        if not words:
            print(f"No words to export for skill {skill_id}")
            return
        
        df = pd.DataFrame(words)
        df.to_csv(filename, index=False)
        print(f"Exported {len(words)} words to {filename}")

def demonstrate_csv_based_generator():
    """Demonstrate the CSV-based generator"""
    print("\nCSV-BASED PHONEME-AWARE WORD GENERATOR DEMO")
    print("=" * 70)
    
    # Initialize generator
    generator = CSVBasedPhonemeGenerator()
    
    # Test different pattern types
    test_skills = [
        '1.1',   # CVC pattern
        '4.1',   # Digraph
        '5.1',   # Initial blend
        '18.5',  # Vowel team
        '24',    # Transformation rule (1-1-1 doubling)
    ]
    
    for skill_id in test_skills:
        if skill_id in generator.rules_by_skill:
            rule = generator.rules_by_skill[skill_id]
            print(f"\n{'='*60}")
            print(f"Skill {skill_id}: {rule['name']}")
            print(f"Pattern type: {rule['pattern_type']}")
            print(f"Graphemes: {rule['graphemes']}")
            print(f"Phonemes: {rule['phonemes']}")
            
            # Generate words
            words = generator.generate_words_for_skill(skill_id, max_words=10)
            
            print(f"\nGenerated {len(words)} words:")
            for i, word_data in enumerate(words[:5]):
                print(f"  {i+1}. {word_data['word']}")
                if 'base_word' in word_data:
                    print(f"     (from base word: {word_data['base_word']})")

def main():
    """Main function"""
    # Check if CSV exists
    csv_path = '/Users/<USER>/word-generator/updated_phonics_master_rules_complete.csv'
    
    if not os.path.exists(csv_path):
        print(f"ERROR: CSV file not found at {csv_path}")
        print("Please ensure the CSV file exists before running.")
        return
    
    demonstrate_csv_based_generator()

if __name__ == "__main__":
    main()