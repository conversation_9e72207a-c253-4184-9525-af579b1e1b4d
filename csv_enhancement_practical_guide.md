# Specific CSV Enhancements by Skill Group

## 🎯 Quick Wins - High Impact Improvements

### 1. **Fix Position Constraints for Multi-Grapheme Skills**

Current format is ambiguous for skills like 18.1. Here's the improvement:

**Current:**
```csv
18.1,Long a teams,"ai, ay, ei, eigh, ey, ea",any
```

**Enhanced:**
```csv
18.1,<PERSON> a teams,"ai, ay, ei, eigh, ey, ea","ai:medial, ay:final, ei:medial, eigh:medial, ey:final, ea:medial"
```

### 2. **Add Explicit Prerequisites**

This ensures true decodability:

```csv
skill_id,skill_name,...,prerequisite_skills
1.1,CVC short a,...,""
1.2,CVC short e,...,"1.1"
4.5,ck digraph,...,"1.1,1.2,1.3,1.4,1.5"
5.1,l-blends,...,"1.1,1.2,1.3,1.4,1.5"
11,Magic <PERSON>,...,"1.1,1.2,1.3,1.4,1.5,4.1,4.2,4.3"
18.1,Long a teams,...,"1.1,1.2,1.3,1.4,1.5,11"
```

### 3. **Generation Constraints for Better Word Selection**

Add specific rules for the generator:

```csv
skill_id,...,generation_constraints
1.1,CVC short a,...,"max_words:50, prefer_nouns:true, exclude:[qu,x], min_frequency:100"
4.5,ck digraph,...,"require_short_vowel:true, position:final_only, exclude_plurals:true"
5.1,l-blends,...,"blend_position:initial_only, prefer_single_syllable:true"
18.1,Long a teams,...,"verify_pronunciation:true, exclude_homophones:true, min_examples_per_pattern:5"
```

### 4. **Heart Word Triggers**

Track which common words become irregular at each level:

```csv
skill_id,...,heart_word_triggers
1.1,CVC short a,...,"the, a, was, of, to"
4.1,ch digraph,...,"school (ch=/k/), chef (ch=/sh/)"
11,Magic E,...,"have, give, come, some, love"
18.1,Long a teams,...,"said (ai=/e/), again (ai=/e/)"
```

### 5. **Common Error Patterns**

Help teachers anticipate student mistakes:

```csv
skill_id,...,common_errors
4.5,ck digraph,...,"bak (missing c), back-ed (should be backed), ckap (ck can't start words)"
5.3,s-blends,...,"s-top (separating blend), psot (reversing), sop (dropping consonant)"
18.1,Long a teams,...,"rane (wrong team), playing (ay in middle), sayed (should be said)"
21.4,Rabbit Words,...,"ra-bbit (wrong split), rabb-it (keeping double together)"
```

## 📊 Enhanced Data Structure Example

Here's a complete enhanced row example:

```csv
skill_id: 18.1
skill_name: Long a teams  
pattern_type: vowel team
graphemes: ai, ay, ei, eigh, ey, ea
required_phonemes: EY
phoneme_description: long /a/ as in rain
position_constraints: various by pattern
position_by_grapheme: ai:medial, ay:final, ei:medial, eigh:medial, ey:final, ea:medial
word_length: 3-8
word_structure: any
special_rules: Must produce long a sound
examples: rain, play, vein, eight, they, great
non_examples: aisle (different sound), said (irregular)
notes: Most consistent long a patterns
prerequisite_skills: 1.1,1.2,1.3,1.4,1.5,11
frequency_rank: 1 (very common)
generation_constraints: min_5_per_pattern, verify_pronunciation
heart_word_triggers: said, again, against
teaching_tips: "ay=end of word day", "ai=middle of word rain"
assessment_focus: Distinguish from short a; correct team selection
common_errors: rane (wrong team), daizy (ay at end), sayed
morphology_links: 23.1,25 (adding suffixes to ay words)
```

## 🔧 Implementation Priority

1. **Phase 1** (Immediate):
   - Add `position_by_grapheme` column
   - Add `prerequisite_skills` column
   - Split multi-pattern position constraints

2. **Phase 2** (Next Week):
   - Add `generation_constraints` column
   - Add `heart_word_triggers` column
   - Add `frequency_rank` column

3. **Phase 3** (Enhancement):
   - Add `teaching_tips` column
   - Add `common_errors` column
   - Add `assessment_focus` column
   - Add `morphology_links` column

## 💡 Quick SQL/Pandas Commands to Help

```python
# Add new columns with defaults
df['prerequisite_skills'] = ''
df['position_by_grapheme'] = ''
df['generation_constraints'] = ''

# Auto-fill some prerequisites based on skill number
df.loc[df['skill_id'].str.startswith('5.'), 'prerequisite_skills'] = '1.1,1.2,1.3,1.4,1.5'
df.loc[df['skill_id'].str.startswith('18.'), 'prerequisite_skills'] = '1.1,1.2,1.3,1.4,1.5,11'

# Split position constraints for vowel teams
vowel_team_skills = df[df['pattern_type'] == 'vowel team']
# Then manually update position_by_grapheme based on the patterns
```

## 🎯 Validation Rules

Add these checks to ensure data quality:

1. **Position Validation**: If multiple graphemes, `position_by_grapheme` should be filled
2. **Prerequisite Validation**: Skills can't require themselves or later skills
3. **Example Validation**: Examples should contain at least one of the graphemes
4. **Length Validation**: Examples should fit within word_length constraints

Would you like me to create a script to help automate some of these enhancements?
