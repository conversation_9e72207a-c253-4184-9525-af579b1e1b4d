# CSV Enhancement Recommendations

## 1. **Add Decodability Dependencies Column**
Add a column that explicitly lists which skills must be mastered before this one:

```csv
skill_id,skill_name,...,prerequisite_skills
5.1,l-blends,...,"1.1,1.2,1.3,1.4,1.5"
18.1,Long a teams,...,"1.1,11.0"
```

This would help the generator ensure true decodability by checking prerequisites.

## 2. **Add Frequency/Priority Column**
Rank patterns by how common they are in English:

```csv
skill_id,skill_name,...,frequency_rank
4.1,ch digraph,...,1 (very common)
4.2,sh digraph,...,1 (very common)
18.8,Short oo teams,...,3 (less common)
```

## 3. **Separate Position Constraints for Multiple Patterns**
For skills with multiple graphemes, break down position constraints:

```csv
skill_id,graphemes,position_constraints
18.1,"ai, ay, ei, eigh, ey, ea","ai:medial, ay:final, ei:medial, eigh:medial, ey:final, ea:medial"
```

## 4. **Add Teaching Tips Column**
Include brief pedagogical notes:

```csv
skill_id,...,teaching_tips
4.5,ck digraph,...,"Emphasize: only after short vowels, never starts words"
21.4,Rabbit Words,...,"Mnemonic: rabbits have two tall ears = two consonants"
```

## 5. **Add Word Generation Constraints**
Specific constraints for the generator:

```csv
skill_id,...,generation_constraints
1.1,CVC short a,...,"exclude_patterns:[qu,x,y], prefer_high_frequency:true"
```

## 6. **Pattern Conflict Resolution**
How to handle overlapping patterns:

```csv
skill_id,...,pattern_conflicts
18.2,Long e teams,...,"ea can be long e OR short e (18.12) - check word list"
```

## 7. **Assessment Focus Column**
What to assess for mastery:

```csv
skill_id,...,assessment_focus
5.1,l-blends,...,"blend articulation, not individual sounds"
```

## 8. **Heart Word Triggers**
Which common words become irregular at this level:

```csv
skill_id,...,common_heart_words
1.1,CVC short a,...,"the, a, was, of"
```

## 9. **Morphology Connections**
Link to morphological patterns:

```csv
skill_id,...,morphology_links
23.1,Common suffixes,...,"connects_to:24,25,26 (spelling changes)"
```

## 10. **Error Pattern Predictions**
Common mistakes to watch for:

```csv
skill_id,...,common_errors
4.5,ck digraph,...,"using k instead (bak), using c (bac)"
```
