#!/usr/bin/env python3
"""
Enhanced CSV Structure Analyzer and Validator
Analyzes the phonics master rules CSV and suggests improvements
"""

import pandas as pd
import json
from collections import defaultdict

class PhonicsCSVAnalyzer:
    def __init__(self, csv_path):
        self.df = pd.read_csv(csv_path)
        self.skills_by_level = defaultdict(list)
        self.pattern_positions = {}
        self.phoneme_mappings = {}
        
    def analyze_structure(self):
        """Analyze the current CSV structure"""
        print("CSV STRUCTURE ANALYSIS")
        print("=" * 60)
        
        # Basic stats
        print(f"Total Skills: {len(self.df)}")
        print(f"Skill Range: {self.df['skill_id'].min()} to {self.df['skill_id'].max()}")
        print(f"Pattern Types: {self.df['pattern_type'].nunique()}")
        
        # Column analysis
        print("\nColumns Present:")
        for col in self.df.columns:
            null_count = self.df[col].isna().sum()
            print(f"  - {col}: {len(self.df) - null_count}/{len(self.df)} filled")
        
        # Pattern type distribution
        print("\nPattern Type Distribution:")
        pattern_counts = self.df['pattern_type'].value_counts()
        for pattern, count in pattern_counts.items():
            print(f"  {pattern}: {count} skills")
    
    def find_position_inconsistencies(self):
        """Find patterns that might have inconsistent position constraints"""
        print("\nPOSITION CONSTRAINT ANALYSIS")
        print("=" * 60)
        
        # Group by graphemes to find duplicates
        grapheme_positions = defaultdict(set)
        
        for _, row in self.df.iterrows():
            graphemes = str(row['graphemes']).split(', ')
            position = row['position_constraints']
            
            for grapheme in graphemes:
                grapheme = grapheme.strip()
                if grapheme and grapheme != 'nan':
                    grapheme_positions[grapheme].add((row['skill_id'], position))
        
        # Find graphemes with multiple positions
        print("Graphemes with Multiple Position Requirements:")
        for grapheme, positions in grapheme_positions.items():
            if len(positions) > 1:
                print(f"\n'{grapheme}':")
                for skill_id, position in positions:
                    print(f"  Skill {skill_id}: {position}")
    
    def suggest_prerequisite_chains(self):
        """Suggest prerequisite relationships based on complexity"""
        print("\nSUGGESTED PREREQUISITE CHAINS")
        print("=" * 60)
        
        prerequisites = {
            # Basic CVC must come first
            '1.1': [],
            '1.2': ['1.1'],
            '1.3': ['1.1', '1.2'],
            '1.4': ['1.1', '1.2', '1.3'],
            '1.5': ['1.1', '1.2', '1.3', '1.4'],
            
            # Digraphs need CVC
            '4.1': ['1.5'],
            '4.2': ['1.5'],
            '4.3': ['1.5'],
            '4.4': ['1.5'],
            '4.5': ['1.5'],  # ck needs short vowels
            
            # Blends need CVC and digraphs
            '5.1': ['1.5', '4.1'],
            '5.2': ['1.5', '4.1'],
            '5.3': ['1.5', '4.1'],
            '5.4': ['5.3'],  # 3-letter needs 2-letter
            
            # Magic E needs CVC
            '11': ['1.5'],
            
            # Vowel teams need Magic E understanding
            '18.1': ['11'],
            '18.2': ['11'],
            '18.3': ['11'],
            
            # R-controlled needs basic vowels
            '20.1': ['1.5'],
            '20.2': ['1.5'],
            '20.3': ['1.5'],
            
            # Syllabication needs everything
            '21.1': ['8'],  # Compound words
            '21.4': ['1.5', '4.1', '5.1'],  # Rabbit needs consonants
        }
        
        for skill_id, prereqs in list(prerequisites.items())[:10]:
            if skill_id in self.df['skill_id'].values:
                skill_name = self.df[self.df['skill_id'] == skill_id]['skill_name'].iloc[0]
                print(f"\n{skill_id} ({skill_name}):")
                print(f"  Prerequisites: {prereqs if prereqs else 'None (foundational)'}")
    
    def analyze_phoneme_coverage(self):
        """Analyze which phonemes are covered by which skills"""
        print("\nPHONEME COVERAGE ANALYSIS")
        print("=" * 60)
        
        phoneme_skills = defaultdict(list)
        
        for _, row in self.df.iterrows():
            phonemes = str(row['required_phonemes']).split(' or ')
            for phoneme in phonemes:
                phoneme = phoneme.strip()
                if phoneme and phoneme != 'nan':
                    phoneme_skills[phoneme].append(row['skill_id'])
        
        # Find phonemes taught in multiple skills
        print("Phonemes Taught in Multiple Skills:")
        for phoneme, skills in sorted(phoneme_skills.items()):
            if len(skills) > 1:
                print(f"  {phoneme}: Skills {', '.join(skills)}")
    
    def suggest_word_generation_rules(self):
        """Suggest specific rules for word generation"""
        print("\nWORD GENERATION RULE SUGGESTIONS")
        print("=" * 60)
        
        generation_rules = {
            'CVC': {
                'rules': [
                    "exactly 3 letters",
                    "no consonant clusters",
                    "no vowel teams",
                    "single syllable only"
                ],
                'prefer': ["high frequency words", "concrete nouns"],
                'avoid': ["abstract concepts", "compound words"]
            },
            'digraph': {
                'rules': [
                    "digraph must be treated as single unit",
                    "check position constraints strictly"
                ],
                'prefer': ["common words", "clear pronunciation"],
                'avoid': ["words where digraph splits across syllables"]
            },
            'vowel team': {
                'rules': [
                    "team must produce expected sound",
                    "check for variant pronunciations"
                ],
                'prefer': ["consistent pronunciation"],
                'avoid': ["exceptions like 'bread' for ea"]
            },
            'r-controlled': {
                'rules': [
                    "r must immediately follow vowel",
                    "creates new vowel sound"
                ],
                'prefer': ["clear r-controlled sound"],
                'avoid': ["words where r is in different syllable"]
            }
        }
        
        for pattern_type, rules in generation_rules.items():
            skills = self.df[self.df['pattern_type'] == pattern_type]['skill_id'].tolist()
            if skills:
                print(f"\n{pattern_type.upper()} (Skills: {', '.join(map(str, skills[:5]))}...)")
                print("  Rules:")
                for rule in rules['rules']:
                    print(f"    - {rule}")
                print("  Prefer:")
                for pref in rules['prefer']:
                    print(f"    + {pref}")
                print("  Avoid:")
                for avoid in rules['avoid']:
                    print(f"    x {avoid}")
    
    def create_enhanced_csv_template(self):
        """Create a template for enhanced CSV structure"""
        enhanced_columns = [
            'skill_id',
            'skill_name', 
            'pattern_type',
            'graphemes',
            'required_phonemes',
            'phoneme_description',
            'position_constraints',
            'position_by_grapheme',  # NEW: Detailed position for each grapheme
            'word_length',
            'word_structure',
            'special_rules',
            'examples',
            'non_examples',
            'notes',
            'prerequisite_skills',  # NEW: Dependencies
            'frequency_rank',  # NEW: How common in English
            'generation_constraints',  # NEW: Specific for generator
            'heart_word_triggers',  # NEW: Common irregular words at this level
            'teaching_tips',  # NEW: Pedagogical notes
            'assessment_focus',  # NEW: What to test
            'common_errors',  # NEW: Predicted mistakes
            'morphology_links'  # NEW: Related morphology skills
        ]
        
        print("\nENHANCED CSV STRUCTURE TEMPLATE")
        print("=" * 60)
        print("New columns to add:")
        
        current_cols = set(self.df.columns)
        new_cols = set(enhanced_columns) - current_cols
        
        for col in new_cols:
            print(f"  + {col}")
        
        # Create sample enhanced row
        sample_enhancement = {
            'skill_id': '18.1',
            'position_by_grapheme': 'ai:medial, ay:final, ei:medial, eigh:medial',
            'prerequisite_skills': '1.1,1.2,1.3,1.4,1.5,11',
            'frequency_rank': '1',
            'generation_constraints': 'min_examples:20, exclude_homophones:true',
            'heart_word_triggers': 'said, they',
            'teaching_tips': 'ay always at end, ai never at end',
            'assessment_focus': 'distinguish from short a patterns',
            'common_errors': 'using ay in middle (paying→playing)',
            'morphology_links': '23.1 (suffixes change spelling)'
        }
        
        print("\nSample Enhanced Row for Skill 18.1:")
        for key, value in sample_enhancement.items():
            if key in new_cols:
                print(f"  {key}: {value}")

if __name__ == "__main__":
    # Analyze the CSV
    analyzer = PhonicsCSVAnalyzer('/Users/<USER>/word-generator/updated_phonics_master_rules_complete.csv')
    
    print("COMPREHENSIVE PHONICS CSV ANALYSIS")
    print("=" * 80)
    
    analyzer.analyze_structure()
    analyzer.find_position_inconsistencies()
    analyzer.suggest_prerequisite_chains()
    analyzer.analyze_phoneme_coverage()
    analyzer.suggest_word_generation_rules()
    analyzer.create_enhanced_csv_template()
    
    print("\n" + "=" * 80)
    print("ANALYSIS COMPLETE!")
    print("\nNext Steps:")
    print("1. Add the suggested new columns")
    print("2. Fill in prerequisite relationships")
    print("3. Add position_by_grapheme for multi-pattern skills")
    print("4. Include generation constraints for better word selection")
    print("5. Add teaching tips and assessment focus for educational value")
