"""
Decodable Passage Generator
Creates readable passages following Orton-<PERSON> constraints
"""

import random
import pandas as pd
from collections import defaultdict, Counter
import re

class DecodablePassageGenerator:
    def __init__(self, word_lists_dict, skill_mappings):
        """
        Initialize the passage generator
        
        Args:
            word_lists_dict: Dictionary of skill_id -> DataFrame of words
            skill_mappings: Skill configuration from main generator
        """
        self.word_lists = word_lists_dict
        self.skill_mappings = skill_mappings
        
        # Common sentence templates for different skill levels
        self.sentence_templates = self._load_sentence_templates()
        
        # Heart words available at each stage
        self.heart_words_by_stage = self._load_heart_words()
        
    def _load_sentence_templates(self):
        """Load sentence templates appropriate for each skill level"""
        return {
            'foundation': [
                "{noun} {verb}.",
                "The {noun} {verb}.",
                "{noun} {verb} {object}.",
                "A {adjective} {noun} {verb}.",
                "{name} {verb} the {noun}.",
                "The {noun} is {adjective}.",
                "{noun} and {noun} {verb}.",
                "Can the {noun} {verb}?",
                "{name} has a {noun}.",
                "The {adjective} {noun} {verb} {adverb}."
            ],
            'intermediate': [
                "The {adjective} {noun} {verb} {preposition} the {object}.",
                "{name} {verb} to {verb2} the {noun}.",
                "When the {noun} {verb}, the {noun2} {verb2}.",
                "{noun} {verb} because {pronoun} {verb2}.",
                "The {noun} that {verb} is {adjective}.",
                "{name} {adverb} {verb} the {adjective} {noun}.",
                "Before the {noun} {verb}, {pronoun} {verb2}.",
                "The {noun} {verb} and {verb2} {adverb}."
            ],
            'advanced': [
                "Although the {noun} {verb}, the {noun2} {adverb} {verb2}.",
                "{name} {verb} that {pronoun} should {verb2} the {noun}.",
                "The {adjective} {noun} {verb} while {verb_ing} {adverb}.",
                "Having {verb_ed} the {noun}, {name} {verb} {adverb}.",
                "The {noun} {verb} whenever {pronoun} {verb2} the {object}.",
                "{prefix}{noun} {verb} the {adjective} {suffix_noun} {adverb}."
            ]
        }
    
    def _load_heart_words(self):
        """Define heart words available at each stage"""
        return {
            'foundation': {
                'the': {'irregular': 'e=/ə/', 'pos': 'article'},
                'a': {'irregular': 'a=/ə/', 'pos': 'article'},
                'of': {'irregular': 'f=/v/', 'pos': 'preposition'},
                'to': {'irregular': 'o=/oo/', 'pos': 'preposition'},
                'is': {'irregular': None, 'pos': 'verb'},
                'and': {'irregular': None, 'pos': 'conjunction'}
            },
            'intermediate': {
                'said': {'irregular': 'ai=/ɛ/', 'pos': 'verb'},
                'was': {'irregular': 'a=/ʌ/', 'pos': 'verb'},
                'were': {'irregular': 'ere=/ɜr/', 'pos': 'verb'},
                'have': {'irregular': None, 'pos': 'verb'},
                'from': {'irregular': None, 'pos': 'preposition'},
                'they': {'irregular': 'ey=/eɪ/', 'pos': 'pronoun'}
            },
            'advanced': {
                'would': {'irregular': 'oul=/ʊ/', 'pos': 'verb'},
                'could': {'irregular': 'oul=/ʊ/', 'pos': 'verb'},
                'should': {'irregular': 'oul=/ʊ/', 'pos': 'verb'},
                'their': {'irregular': 'ei=/eə/', 'pos': 'pronoun'},
                'because': {'irregular': None, 'pos': 'conjunction'},
                'through': {'irregular': 'ough=/u/', 'pos': 'preposition'}
            }
        }
    
    def generate_passage(self, skill_id, passage_type='mini', word_count=40, 
                        target_density=0.65, strict_decodability=0.90):
        """
        Generate a decodable passage for a specific skill
        
        Args:
            skill_id: Target skill (e.g., '4.1' for ch digraph)
            passage_type: 'mini' for single skill, 'full' for combined
            word_count: Target number of words
            target_density: Percentage of words that should contain target pattern
            strict_decodability: Overall decodability requirement
            
        Returns:
            dict with passage text and statistics
        """
        if skill_id not in self.word_lists:
            return {"error": f"No word list found for skill {skill_id}"}
        
        # Get word pools
        target_words = self._get_target_words(skill_id)
        review_words = self._get_review_words(skill_id)
        heart_words = self._get_appropriate_heart_words(skill_id)
        
        # Calculate word distribution
        distribution = self._calculate_distribution(
            word_count, target_density, strict_decodability
        )
        
        # Generate passage
        passage_words = []
        word_usage = defaultdict(int)
        
        # Select words based on distribution
        selected_words = {
            'target': self._select_words(target_words, distribution['target']),
            'review': self._select_words(review_words, distribution['review']),
            'heart': self._select_words(list(heart_words.keys()), distribution['heart'])
        }
        
        # Create sentences
        sentences = self._create_sentences(selected_words, skill_id)
        
        # Format passage
        passage_text = ' '.join(sentences)
        
        # Calculate statistics
        stats = self._calculate_statistics(passage_text, skill_id)
        
        return {
            'passage': passage_text,
            'word_count': len(passage_text.split()),
            'statistics': stats,
            'word_distribution': distribution,
            'sentences': sentences
        }
    
    def _get_target_words(self, skill_id):
        """Get words containing the target pattern"""
        df = self.word_lists[skill_id]
        return df[df['word_type'] == 'target']['word'].tolist()
    
    def _get_review_words(self, skill_id):
        """Get review words from previously mastered skills"""
        review_words = []
        
        # Determine which skills to pull review words from
        skill_num = float(skill_id)
        for other_id, df in self.word_lists.items():
            if float(other_id) < skill_num:
                # Add high-frequency words from previous skills
                hf_words = df[df['is_HF'] == True]['word'].tolist()
                review_words.extend(hf_words[:5])  # Limit per skill
                
                # Add some regular words too
                regular_words = df[df['is_HF'] == False]['word'].tolist()
                review_words.extend(regular_words[:3])
        
        return list(set(review_words))  # Remove duplicates
    
    def _get_appropriate_heart_words(self, skill_id):
        """Get heart words appropriate for skill level"""
        skill_num = float(skill_id)
        
        if skill_num <= 10:
            return self.heart_words_by_stage['foundation']
        elif skill_num <= 20:
            return {**self.heart_words_by_stage['foundation'], 
                   **self.heart_words_by_stage['intermediate']}
        else:
            all_heart = {}
            for stage in self.heart_words_by_stage.values():
                all_heart.update(stage)
            return all_heart
    
    def _calculate_distribution(self, word_count, target_density, decodability):
        """Calculate how many words of each type to use"""
        target_count = int(word_count * target_density)
        
        # Calculate remaining decodable words
        remaining_decodable = int(word_count * decodability) - target_count
        heart_count = word_count - int(word_count * decodability)
        
        # Split remaining between review and high-frequency
        review_count = remaining_decodable
        
        return {
            'target': target_count,
            'review': review_count,
            'heart': heart_count,
            'total': word_count
        }
    
    def _select_words(self, word_pool, count):
        """Select words from pool with some repetition for natural flow"""
        if not word_pool:
            return []
        
        selected = []
        
        # Allow some repetition for natural flow
        while len(selected) < count:
            word = random.choice(word_pool)
            selected.append(word)
            
            # Limit repetitions
            word_counts = Counter(selected)
            if word_counts[word] > 3:  # Max 3 repetitions
                selected.pop()
        
        return selected
    
    def _create_sentences(self, selected_words, skill_id):
        """Create sentences using selected words"""
        skill_num = float(skill_id)
        
        # Choose appropriate templates
        if skill_num <= 10:
            templates = self.sentence_templates['foundation']
        elif skill_num <= 20:
            templates = self.sentence_templates['intermediate']
        else:
            templates = self.sentence_templates['advanced']
        
        sentences = []
        all_words = (selected_words['target'] + 
                    selected_words['review'] + 
                    selected_words['heart'])
        
        random.shuffle(all_words)
        word_iter = iter(all_words)
        
        # Create sentences until we use all words
        while True:
            try:
                template = random.choice(templates)
                sentence = self._fill_template(template, word_iter, selected_words)
                if sentence:
                    sentences.append(sentence)
            except StopIteration:
                break
        
        return sentences
    
    def _fill_template(self, template, word_iter, selected_words):
        """Fill a sentence template with appropriate words"""
        # This is simplified - in production, you'd want more sophisticated
        # part-of-speech tagging and agreement checking
        
        placeholders = re.findall(r'\{(\w+)\}', template)
        filled = template
        
        for placeholder in placeholders:
            try:
                word = next(word_iter)
                filled = filled.replace(f'{{{placeholder}}}', word, 1)
            except StopIteration:
                return None
        
        # Capitalize first letter
        filled = filled[0].upper() + filled[1:]
        
        return filled
    
    def _calculate_statistics(self, passage, skill_id):
        """Calculate passage statistics"""
        words = passage.lower().split()
        word_count = len(words)
        
        # Count target pattern occurrences
        if skill_id in self.word_lists:
            df = self.word_lists[skill_id]
            target_words = df[df['word_type'] == 'target']['word'].tolist()
            target_count = sum(1 for word in words if word.strip('.,!?') in target_words)
        else:
            target_count = 0
        
        # Count heart words
        all_heart_words = []
        for stage_words in self.heart_words_by_stage.values():
            all_heart_words.extend(stage_words.keys())
        
        heart_count = sum(1 for word in words if word.strip('.,!?') in all_heart_words)
        
        # Calculate percentages
        target_percentage = (target_count / word_count * 100) if word_count > 0 else 0
        decodability = ((word_count - heart_count) / word_count * 100) if word_count > 0 else 0
        
        return {
            'word_count': word_count,
            'target_words': target_count,
            'target_percentage': round(target_percentage, 1),
            'heart_words': heart_count,
            'decodability_percentage': round(decodability, 1),
            'sentence_count': len([s for s in passage.split('.') if s.strip()])
        }
    
    def generate_full_reader(self, skill_ids, title="Adventure Story", 
                           word_count=200, chapters=None):
        """
        Generate a full decodable reader combining multiple skills
        
        Args:
            skill_ids: List of skills to combine
            title: Title for the reader
            word_count: Total target words
            chapters: Number of chapters (None for single story)
            
        Returns:
            dict with full reader content and statistics
        """
        if chapters:
            words_per_chapter = word_count // chapters
            chapter_contents = []
            
            for i in range(chapters):
                # Rotate through skills for each chapter
                primary_skill = skill_ids[i % len(skill_ids)]
                
                chapter_passage = self.generate_passage(
                    primary_skill,
                    passage_type='full',
                    word_count=words_per_chapter,
                    target_density=0.40,  # Lower density for full readers
                    strict_decodability=0.85
                )
                
                chapter_contents.append({
                    'number': i + 1,
                    'title': f"Chapter {i + 1}",
                    'content': chapter_passage['passage'],
                    'statistics': chapter_passage['statistics']
                })
            
            return {
                'title': title,
                'type': 'chapter_book',
                'chapters': chapter_contents,
                'total_words': sum(ch['statistics']['word_count'] for ch in chapter_contents)
            }
        else:
            # Single story combining all skills
            combined_passage = self.generate_passage(
                skill_ids[0],  # Primary skill
                passage_type='full',
                word_count=word_count,
                target_density=0.40,
                strict_decodability=0.85
            )
            
            return {
                'title': title,
                'type': 'single_story',
                'content': combined_passage['passage'],
                'statistics': combined_passage['statistics']
            }