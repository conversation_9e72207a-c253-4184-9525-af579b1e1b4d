#!/usr/bin/env python3
"""
CSV Enhancement Script for Phonics Master Rules
Automatically adds critical columns and fills in logical defaults
"""

import pandas as pd
import re
from typing import Dict, List, Set

class PhonicsCSVEnhancer:
    def __init__(self, input_csv: str, output_csv: str = None):
        self.input_csv = input_csv
        self.output_csv = output_csv or input_csv.replace('.csv', '_enhanced.csv')
        self.df = pd.read_csv(input_csv)
        
        # Define prerequisite rules
        self.prerequisite_rules = self._define_prerequisite_rules()
        
        # Define position mappings
        self.position_mappings = self._define_position_mappings()
        
    def _define_prerequisite_rules(self) -> Dict[str, List[str]]:
        """Define logical prerequisite relationships"""
        return {
            # CVC progression
            '1.1': [],  # First skill, no prerequisites
            '1.2': ['1.1'],
            '1.3': ['1.1', '1.2'],
            '1.4': ['1.1', '1.2', '1.3'],
            '1.5': ['1.1', '1.2', '1.3', '1.4'],
            
            # CV requires CVC
            '2.1': ['1.5'],
            '2.2': ['1.5'],
            '2.3': ['1.5'],
            
            # C/K rule requires basic CVC
            '3.1': ['1.5'],
            '3.2': ['1.5'],
            
            # Digraphs require all short vowels
            '4.1': ['1.5'],
            '4.2': ['1.5'],
            '4.3': ['1.5'],
            '4.4': ['1.5'],
            '4.5': ['1.5'],  # ck specifically needs short vowels
            
            # Blends require CVC and basic digraphs
            '5.1': ['1.5', '4.1'],
            '5.2': ['1.5', '4.1'],
            '5.3': ['1.5', '4.1'],
            '5.4': ['5.3'],  # 3-letter blends need 2-letter
            
            # Final blends need initial blends
            '6.1': ['1.5', '5.1'],
            '6.2': ['1.5', '5.1'],
            '6.3': ['1.5', '5.1'],
            
            # Special rules need basics
            '7': ['1.5', '4.5'],  # FLOSS needs short vowels and ck
            '9': ['1.5', '4.1'],  # tch needs ch
            '10': ['1.5'],  # dge needs short vowels
            
            # Magic E needs all CVC
            '11': ['1.5'],
            
            # Vowel teams need Magic E concept
            '18.1': ['1.5', '11'],
            '18.2': ['1.5', '11'],
            '18.3': ['1.5', '11'],
            '18.4': ['1.5', '11'],
            '18.5': ['1.5', '11'],
            
            # R-controlled needs vowels
            '20.1': ['1.5'],
            '20.2': ['1.5'],
            '20.3': ['1.5'],
            '20.4': ['20.3'],  # war needs ar
            '20.5': ['20.2'],  # wor needs or
            
            # Syllabication needs everything
            '21.1': ['1.5', '5.1', '11'],  # Compound words
            '21.2': ['1.5', '22'],  # Prefix/suffix
            '21.4': ['1.5', '4.1', '5.1'],  # Rabbit needs blends
        }
    
    def _define_position_mappings(self) -> Dict[str, Dict[str, str]]:
        """Define position constraints for multi-grapheme skills"""
        return {
            '18.1': {  # Long a teams
                'ai': 'medial',
                'ay': 'final', 
                'ei': 'medial',
                'eigh': 'medial',
                'ey': 'final',
                'ea': 'medial'
            },
            '18.2': {  # Long e teams
                'ea': 'any',
                'ee': 'any',
                'ie': 'medial',
                'ey': 'final',
                'ei': 'medial'
            },
            '18.3': {  # Long o teams
                'oa': 'medial',
                'oe': 'final',
                'ow': 'final',
                'ou': 'medial'
            },
            '18.4': {  # ow diphthong
                'ou': 'medial',
                'ow': 'final'
            },
            '18.5': {  # Long i teams
                'ie': 'final',
                'igh': 'medial',
                'ei': 'medial',
                'uy': 'final'
            },
            '18.6': {  # Long u teams
                'ue': 'final',
                'ew': 'final',
                'eu': 'medial'
            },
            '18.10': {  # aw sound
                'au': 'medial',
                'aw': 'final'
            },
            '18.11': {  # oy sound
                'oi': 'medial',
                'oy': 'final'
            },
            '20.2': {  # or sound variations
                'or': 'any',
                'ore': 'final'
            }
        }
    
    def enhance_csv(self):
        """Add new columns and fill with intelligent defaults"""
        print("Enhancing CSV structure...")
        
        # Add new columns if they don't exist
        new_columns = {
            'prerequisite_skills': '',
            'position_by_grapheme': '',
            'generation_constraints': '',
            'heart_word_triggers': '',
            'frequency_rank': '',
            'common_errors': '',
            'teaching_tips': '',
            'assessment_focus': ''
        }
        
        for col, default in new_columns.items():
            if col not in self.df.columns:
                self.df[col] = default
                print(f"Added column: {col}")
        
        # Fill prerequisite skills
        print("\nFilling prerequisite skills...")
        for skill_id, prereqs in self.prerequisite_rules.items():
            mask = self.df['skill_id'] == skill_id
            if mask.any():
                self.df.loc[mask, 'prerequisite_skills'] = ','.join(prereqs)
        
        # Fill position_by_grapheme for multi-pattern skills
        print("Filling position constraints...")
        for skill_id, mappings in self.position_mappings.items():
            mask = self.df['skill_id'] == skill_id
            if mask.any():
                position_str = ', '.join([f"{g}:{p}" for g, p in mappings.items()])
                self.df.loc[mask, 'position_by_grapheme'] = position_str
        
        # Add generation constraints based on pattern type
        print("Adding generation constraints...")
        constraint_mappings = {
            'CVC': 'max_words:50, prefer_high_frequency:true, single_syllable:true',
            'digraph': 'verify_position:true, exclude_split_digraphs:true',
            'initial blend': 'position:initial_only, prefer_common_words:true',
            'final blend': 'position:final_only, prefer_common_words:true',
            'vowel team': 'verify_pronunciation:true, min_5_per_pattern:true',
            'r-controlled': 'verify_r_control:true, exclude_split_syllables:true',
            'magic_e': 'verify_long_vowel:true, exclude_exceptions:true'
        }
        
        for pattern_type, constraints in constraint_mappings.items():
            mask = self.df['pattern_type'] == pattern_type
            self.df.loc[mask, 'generation_constraints'] = constraints
        
        # Add frequency rankings
        print("Adding frequency rankings...")
        high_frequency_patterns = ['CVC', 'digraph', 'initial blend', 'magic_e']
        medium_frequency_patterns = ['vowel team', 'r-controlled', 'final blend']
        
        for pattern in high_frequency_patterns:
            mask = self.df['pattern_type'] == pattern
            self.df.loc[mask, 'frequency_rank'] = '1'
        
        for pattern in medium_frequency_patterns:
            mask = self.df['pattern_type'] == pattern
            self.df.loc[mask, 'frequency_rank'] = '2'
        
        # Add common heart word triggers
        heart_word_mappings = {
            '1.1': 'the, a, was, of',
            '1.2': 'he, be, me, we, she',
            '4.1': 'school, chef',
            '11': 'have, give, come, some, love',
            '18.1': 'said, again',
            '18.2': 'bread, dead'
        }
        
        for skill_id, heart_words in heart_word_mappings.items():
            mask = self.df['skill_id'] == skill_id
            if mask.any():
                self.df.loc[mask, 'heart_word_triggers'] = heart_words
        
        print(f"\nEnhancement complete! Saving to: {self.output_csv}")
        self.df.to_csv(self.output_csv, index=False)
        
        return self.df
    
    def generate_summary_report(self):
        """Generate a summary of enhancements made"""
        print("\n" + "=" * 60)
        print("ENHANCEMENT SUMMARY REPORT")
        print("=" * 60)
        
        # Count filled fields
        for col in ['prerequisite_skills', 'position_by_grapheme', 
                   'generation_constraints', 'heart_word_triggers']:
            if col in self.df.columns:
                filled = self.df[col].notna() & (self.df[col] != '')
                print(f"{col}: {filled.sum()}/{len(self.df)} filled")
        
        # Show sample enhanced rows
        print("\nSample Enhanced Skills:")
        sample_skills = ['1.1', '4.5', '18.1', '21.4']
        
        for skill_id in sample_skills:
            row = self.df[self.df['skill_id'] == skill_id]
            if not row.empty:
                row = row.iloc[0]
                print(f"\nSkill {skill_id} - {row['skill_name']}:")
                if row['prerequisite_skills']:
                    print(f"  Prerequisites: {row['prerequisite_skills']}")
                if row['position_by_grapheme']:
                    print(f"  Positions: {row['position_by_grapheme']}")
                if row['generation_constraints']:
                    print(f"  Constraints: {row['generation_constraints']}")

if __name__ == "__main__":
    # Enhance the CSV
    enhancer = PhonicsCSVEnhancer(
        input_csv='/Users/<USER>/word-generator/updated_phonics_master_rules_complete.csv',
        output_csv='/Users/<USER>/word-generator/enhanced_phonics_master_rules_v2.csv'
    )
    
    # Run enhancement
    enhanced_df = enhancer.enhance_csv()
    
    # Generate report
    enhancer.generate_summary_report()
    
    print("\n✅ CSV enhancement complete!")
    print("Next steps:")
    print("1. Review the enhanced CSV for accuracy")
    print("2. Manually fill in any missing teaching tips")
    print("3. Update generators to use new columns")
    print("4. Test with sample word generation")
