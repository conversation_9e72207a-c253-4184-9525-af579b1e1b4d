skill_id,skill_name,pattern_type,graphemes,required_phonemes,phoneme_description,position_constraints,word_length,word_structure,special_rules,examples,non_examples,notes,prerequisite_skills,position_by_grapheme,generation_constraints,heart_word_triggers,frequency_rank,common_errors,teaching_tips,assessment_focus
1.1,CVC short a,CVC,a,AE,short /a/ as in cat,medial (vowel position),3,3-letter CVC only,"No blends, digraphs, or vowel teams allowed","cat, hat, mat, sat, bat","cake (magic e), rain (vowel team), chat (digraph)",,,"","max_words:50, prefer_high_frequency:true, single_syllable:true","the, a, was, of, to",1,"ca (missing t), kate (adding silent e)","Point to each sound as you blend","Can segment and blend 3 sounds"
1.2,CVC short e,CVC,e,EH,short /e/ as in bed,medial (vowel position),3,3-letter CVC only,"No blends, digraphs, or vowel teams allowed","bed, get, met, set, wet","keep (vowel team), shed (digraph), best (blend)",,1.1,"","max_words:50, prefer_high_frequency:true, single_syllable:true","he, be, me, we, she",1,"confusing with short i","Short e is the smile vowel","Distinguish from short a"
1.3,CVC short i,CVC,i,IH,short /i/ as in sit,medial (vowel position),3,3-letter CVC only,"No blends, digraphs, or vowel teams allowed","sit, hit, bit, fit, pit","kite (magic e), rain (vowel team), ship (digraph)",,"1.1,1.2","","max_words:50, prefer_high_frequency:true, single_syllable:true","is, his, this",1,"confusing with short e","Short i is the itch vowel","Distinguish from short e"
1.4,CVC short o,CVC,o,AA,short /o/ as in hot,medial (vowel position),3,3-letter CVC only,"No blends, digraphs, or vowel teams allowed","hot, pot, dot, got, lot","boat (vowel team), shop (digraph), stop (blend)",,"1.1,1.2,1.3","","max_words:50, prefer_high_frequency:true, single_syllable:true","on, not, for",1,"using long o sound","Doctor says ah for short o","Distinguish from short u"
1.5,CVC short u,CVC,u,AH,short /u/ as in cup,medial (vowel position),3,3-letter CVC only,"No blends, digraphs, or vowel teams allowed","cup, cut, but, nut, hut","cute (magic e), out (vowel team), shut (digraph)",,"1.1,1.2,1.3,1.4","","max_words:50, prefer_high_frequency:true, single_syllable:true","but, up, us, much",1,"using long u sound","Short u is the up sound","All 5 short vowels mastered"
2.1,CV long e,CV open syllable,e,IY,long /e/ as in be,final,2-3,CV structure only,"Limited to specific words: be, he, me, we, she","be, he, me, we, she","bee (double vowel), sea (vowel team)",,"1.1,1.2,1.3,1.4,1.5","","specific_words_only:true","the (when pronounced thee)",2,"adding consonant at end","Open syllable = long vowel","Recognize CV pattern"
2.2,CV long o,CV open syllable,o,OW,long /o/ as in go,final,2,CV structure only,"Limited to specific words: go, no, so","go, no, so","toe (CVe), row (vowel team)",,"1.1,1.2,1.3,1.4,1.5","","specific_words_only:true","to, do",2,"adding consonant at end","These are high-frequency words","Limited word set"
2.3,CV long i,CV open syllable,i,AY,long /i/ as in I,final or standalone,1-2,CV structure only,"Limited to specific words: I, hi","I, hi","pie (vowel team), my (y as vowel)",,"1.1,1.2,1.3,1.4,1.5","","specific_words_only:true","",2,"lowercase i for I","Capital I is a word","Sight word recognition"
3.1,"c before a,o,u",hard c rule,"ca, co, cu",K,hard /k/ sound,initial only,3,any,"c makes /k/ sound before a, o, u","cat, cot, cup, can, come","city (soft c), cent (soft c)",,"1.1,1.2,1.3,1.4,1.5","ca:initial, co:initial, cu:initial","verify_c_rule:true","",2,"using soft c sound","C is hard before a, o, u","Apply c/k spelling rule"
3.2,"k before e,i,y",k spelling rule,"ke, ki",K,hard /k/ sound,initial only,3-4,any,Use k (not c) before e or i,"kit, key, kid, kite, keep","city (uses c), cent (uses c)",,"1.1,1.2,1.3,1.4,1.5","ke:initial, ki:initial","verify_k_rule:true","",2,"using c instead of k","K is used before e, i, y","Apply c/k spelling rule"
4.1,ch digraph,digraph,ch,CH,/ch/ as in chip,initial or final,3-4,any,Two letters make one sound,"chip, chat, much, lunch, chop","character (k sound), chef (sh sound)",,"1.1,1.2,1.3,1.4,1.5","","verify_position:true, exclude_split_digraphs:true","school (ch=/k/), chef (ch=/sh/)",1,"separating c and h sounds","Two letters, one sound","Recognize digraph unit"
4.2,sh digraph,digraph,sh,SH,/sh/ as in ship,initial or final,3-4,any,Two letters make one sound,"ship, shop, wish, cash, she",mishap (s-h across syllables),,"1.1,1.2,1.3,1.4,1.5","","verify_position:true, exclude_split_digraphs:true","",1,"separating s and h sounds","Quiet sound - shhhh","Recognize digraph unit"
4.3,th digraph,digraph,th,TH or DH,voiced or unvoiced /th/,initial or final,3-4,any,Can be voiced or unvoiced,"this, that, thin, bath, with",lighthouse (t-h across morphemes),,"1.1,1.2,1.3,1.4,1.5","","verify_position:true, exclude_split_digraphs:true","the, them, there, they",1,"using f or d sound","Tongue between teeth","Distinguish voiced/unvoiced"
4.4,wh digraph,digraph,wh,W or HH W,/w/ or /hw/,initial only,3-4,any,Always at beginning of words/syllables,"when, what, where, why, which",nowhere (compound word),,"1.1,1.2,1.3,1.4,1.5","wh:initial","verify_position:true, position:initial_only","who (says /h/)",1,"wrong position, w sound only","Some dialects pronounce h","Initial position only"
4.5,ck digraph,digraph,ck,K,/k/ as in back,final only,3-4,after short vowel,Only after short vowels in one-syllable words,"back, pack, sick, lock, duck","bark (r-controlled), make (long vowel)",,"1.1,1.2,1.3,1.4,1.5","ck:final","verify_position:true, require_short_vowel:true","",1,"using at beginning, after long vowel","Never starts a word","Final after short vowel only"
5.1,l-blends,initial blend,"bl, cl, fl, gl, pl, sl","B L, K L, F L, G L, P L, S L",consonant + /l/,initial only,4-5,any,Two consonant sounds blend together,"black, clap, flag, glad, play, slow","help (final blend), below (across syllables)",,"1.1,1.2,1.3,1.4,1.5,4.1,4.2","bl:initial, cl:initial, fl:initial, gl:initial, pl:initial, sl:initial","position:initial_only, prefer_common_words:true","",1,"dropping one sound","Both sounds must be heard","Smooth blending of 2 sounds"
5.2,r-blends,initial blend,"br, cr, dr, fr, gr, pr, tr","B R, K R, D R, F R, G R, P R, T R",consonant + /r/,initial only,4-5,any,Two consonant sounds blend together,"bring, crab, drip, from, grab, print, trip","car (not a blend), forth (r-controlled)",,"1.1,1.2,1.3,1.4,1.5,4.1,4.2","br:initial, cr:initial, dr:initial, fr:initial, gr:initial, pr:initial, tr:initial","position:initial_only, prefer_common_words:true","",1,"dropping one sound, adding w sound","Don't add /w/ after consonant","Smooth blending of 2 sounds"
5.3,s-blends,initial blend,"sc, sk, sm, sn, sp, st, sw","S K, S K, S M, S N, S P, S T, S W",/s/ + consonant,initial only,4-5,any,Two consonant sounds blend together,"scan, skip, smell, snap, spot, stop, swim","fast (final blend), sh (digraph not blend)",,"1.1,1.2,1.3,1.4,1.5,4.1,4.2","sc:initial, sk:initial, sm:initial, sn:initial, sp:initial, st:initial, sw:initial","position:initial_only, prefer_common_words:true","",1,"dropping s, adding vowel between","S comes first, blend smoothly","No extra vowel sound"
5.4,3-letter blends,initial blend,"scr, spr, str, spl, squ, thr","S K R, S P R, S T R, S P L, S K W, TH R",three consonant sounds at the beginning of a word,initial only,5-6,any,Three consonant sounds blend together,"scrap, spring, string, split, square, three","first (r-controlled), inst (final blend)",,"1.1,1.2,1.3,1.4,1.5,4.1,4.2,5.3","scr:initial, spr:initial, str:initial, spl:initial, squ:initial, thr:initial","position:initial_only, prefer_common_words:true","",2,"dropping middle sound","All 3 sounds must be heard","Blend 3 sounds smoothly"
6.1,tongue-tap blends,final blend,"nt, st, sk, ct, pt, xt","N T, S T, S K, K T, P T, K S T",two consonant sounds at the end of a word,final only,3-5,any,Two consonant sounds at word end,"ant, went, plant, bent, font, list, past, best, dust, test, mask, risk, task, desk, act, fact, kept, slept, crept, next, text",,,"1.1,1.2,1.3,1.4,1.5,5.1","nt:final, st:final, sk:final, ct:final, pt:final, xt:final","position:final_only, prefer_common_words:true","",2,"dropping final sound","Tongue taps for these sounds","Both final sounds heard"
6.2,lip-pop blends,final blend,"mp, ft, lf, sp","M P, F T, L F, S P",two consonant sounds at the end of a word,final only,3-5,any,Two consonant sounds at word end,"camp, jump, damp, lamp, stamp, lift, soft, craft, gift, shift, self, gulf, half, shelf, elf, lisp, wasp, gasp",,,"1.1,1.2,1.3,1.4,1.5,5.1","mp:final, ft:final, lf:final, sp:final","position:final_only, prefer_common_words:true","",2,"dropping final sound","Lips pop for these sounds","Both final sounds heard"
6.3,liquid blends,final blend,"nd, lm, ld","N D, L M, L D",two consonant sounds at the end of a word,final only,3-5,any,Two consonant sounds at word end,"and, hand, land, bend, fund, film, helm, palm, calm, cold, held, fold, gold, wild",,,"1.1,1.2,1.3,1.4,1.5,5.1","nd:final, lm:final, ld:final","position:final_only, prefer_common_words:true","and, kind, find",2,"dropping final sound","Liquid sounds flow","Both final sounds heard"
7.0,FLOSS rule,spelling rule,"ff, ll, ss, zz","F, L, S, Z",double after short vowel,final after short vowel,4-6,one-syllable words,"Double f, l, s, z after short vowel in one-syllable words","stuff, bell, pass, buzz, cliff",,,"1.1,1.2,1.3,1.4,1.5,4.5","ff:final, ll:final, ss:final, zz:final","one_syllable_only:true, after_short_vowel:true","all, call, ball",1,"single letter after short vowel","FLOSS = double after short","One syllable words only"
8.0,Backpack rule,spelling rule,ck,K,Spell the /k/ sound ck right after a short vowel,final after short vowel,4-5,one-syllable words,Two complete words joined together,"backpack, homework, playground, lunchbox",,"Not explicitly defined in patterns, but used for compound words","1.1,1.2,1.3,1.4,1.5,4.1,4.2,4.3,5.1,5.2","","identify_compound_parts:true","",2,"not recognizing two words","This seems to mix compound words with ck rule","Find two complete words"
9.0,Catch rule,spelling rule,tch,CH,tch grapheme makes /ch/ after short vowel,final after short vowel,5-6,one-syllable words,Use tch after short vowel (not ch),"catch, match, pitch, notch, clutch",,,"1.1,1.2,1.3,1.4,1.5,4.1","tch:final","after_short_vowel:true, one_syllable:true","much, such, which, rich",1,"using ch after short vowel","tch catches the short vowel","After short vowel = tch"
10.0,Bridge rule,spelling rule,dge,JH,dge grapheme makes /j/ after short vowel,final after short vowel,5-6,one-syllable words,Use dge after short vowel (not ge),"badge, edge, bridge, lodge, judge",,,"1.1,1.2,1.3,1.4,1.5","dge:final","after_short_vowel:true, one_syllable:true","",1,"using ge after short vowel","dge bridges the short vowel","After short vowel = dge"
11.0,Magic E,vowel pattern,"a_e, e_e, i_e, u_e","EY, IY, OW, UW",makes vowel long,final e with consonant between,4-5,VCe pattern,Silent e makes vowel say its name,"make, pete, bike, hope, cute","have (exception), love (exception)",,"1.1,1.2,1.3,1.4,1.5","a_e:split, e_e:split, i_e:split, o_e:split, u_e:split","verify_long_vowel:true, exclude_exceptions:true","have, give, come, some, love, done",1,"keeping short vowel sound","Magic e is silent but powerful","Long vowel, silent e"
12.0,Closed syllable exceptions,exception pattern,"oll, ald, alk, old, ild, ind","OW L, AO L D, AO K, OW L D, AY L D, AY N D",exceptions to closed syllable,any position,3-6,any,Vowel sounds long in closed syllables,"all, ball, tall, old, cold, wild",,Specific words where closed syllable has long vowel,"1.1,1.2,1.3,1.4,1.5,6.1,6.2,6.3","","memorize_exceptions:true","",3,"applying short vowel rule","These break the rules","Memorize as sight words"
13.1,initial soft g,soft sound,"ge, gi, gy",JH,"/j/ before e, i, y is spelled with a g",initial,3-6,any,"g makes /j/ sound before e, i, y","giant, gym, gentle",,,"1.1,1.2,1.3,1.4,1.5,4.1","ge:initial, gi:initial, gy:initial","verify_soft_sound:true","",2,"using hard g sound","g is gentle before e, i, y","Soft g = /j/ sound"
13.2,initial soft c,soft sound,"ce, ci, cy",S,"/s/ before e, i, y is spelled with a c",initial,4-5,any,"c makes /s/ sound before e, i, y","city, cent, Cindy",,,"1.1,1.2,1.3,1.4,1.5,4.1","ce:initial, ci:initial, cy:initial","verify_soft_sound:true","",2,"using hard c sound","c is soft before e, i, y","Soft c = /s/ sound"
13.3,final soft g,soft sound,ge,JH,final /j/ sound in a word with a long vowel is spelled ge,final,3-5,any,final /j/ sound in a word with a long vowel is spelled ge,"cage, age, huge",,,"1.1,1.2,1.3,1.4,1.5,11.0","ge:final","after_long_vowel:true","",3,"using j instead","Long vowel + /j/ = ge","Final position only"
13.4,final soft c,soft sound,ce,S,final /s/ sound in a word with a long vowel is spelled ce,final,3-5,any,final /s/ sound in a word with a long vowel is spelled ce,"mice, nice, race, face",,,"1.1,1.2,1.3,1.4,1.5,11.0","ce:final","after_long_vowel:true","",3,"using s instead","Long vowel + /s/ = ce","Final position only"
13.5,final soft s,soft sound,se,Z,final /z/ sound in a word with a long vowel is spelled se,final,3-5,any,final /z/ sound in a word with a long vowel is spelled se,"nose, rose, hose",,,"1.1,1.2,1.3,1.4,1.5,11.0","se:final","after_long_vowel:true","",3,"using z instead","Long vowel + /z/ = se","Final position only"
14.1,y as a vowel long i,y as a vowel,y,AY,y as long i,final,3-4,one-syllable words,must be only 1 syllable,"cry, fly, by",,,"1.1,1.2,1.3,1.4,1.5","y:final","one_syllable_only:true","",2,"in multi-syllable words","Y says /i/ in 1-syllable words","One syllable only"
14.2,y as a vowel long e,y as a vowel,y,IY,y as long e,final,4-5,two-syllable words,must be 2 syllables,"candy, baby, navy",,,"1.1,1.2,1.3,1.4,1.5,21.1","y:final","two_syllable_only:true","",2,"in one-syllable words","Y says /e/ in 2-syllable words","Two syllables only"
14.3,y as a vowel short i,y as a vowel,y,IH,y as short i,medial,3-5,any,y in the middle of a word,"gym, myth",,Y acts as vowel at end of word or when only vowel,"1.1,1.2,1.3,1.4,1.5","y:medial","verify_medial_position:true","",3,"at end of word","Y in middle = short i","Medial position only"
15.1,"o says ""uh""",schwa lazy vowel sound,o,AH,o makes unstressed /uh/ sound instead of long o,medial,3-6,any,Unstressed o makes schwa sound,"dove, glove, son",,Schwa sound in unstressed syllables,"1.1,1.2,1.3,1.4,1.5,21.1","","verify_unstressed:true","love, come, some",3,"using long or short o","Lazy vowel in unstressed syllable","Unstressed = schwa"
15.2,"a says ""uh""",schwa lazy vowel sound,a,AH,a makes unstressed /uh/ sound instead of long o,medial,3-6,any,Unstressed a makes schwa sound,"ago, zebra, alone",,Schwa sound in unstressed syllables,"1.1,1.2,1.3,1.4,1.5,21.1","","verify_unstressed:true","",3,"using long or short a","Lazy vowel in unstressed syllable","Unstressed = schwa"
15.3,"e says ""uh""",schwa lazy vowel sound,e,AH,e makes unstressed /uh/ sound instead of long o,medial,3-6,any,Unstressed e makes schwa sound,"item, camel, silent",,Schwa sound in unstressed syllables,"1.1,1.2,1.3,1.4,1.5,21.1","","verify_unstressed:true","",3,"using long or short e","Lazy vowel in unstressed syllable","Unstressed = schwa"
16.0,"""shy"" silent letters","""shy"" silent letters","kn, wr, gn, mn, mb","N, R, M",consonant pair with only one making a sound,initial or final,4-7,any,consonant pair with only one making a sound,"knee, write, lamb, light, sign",,,"1.1,1.2,1.3,1.4,1.5,4.1,4.2,4.3","kn:initial, wr:initial, gn:any, mn:any, mb:final","verify_silent_letter:true","know, write, lamb, sign",2,"pronouncing both letters","One letter is shy (silent)","Only one sound heard"
17.0,3 sounds of ed,3 sounds of ed,ed,"EH D, D, T",three pronunciations,final,4-7,any,same ending with 3 different sounds,"planted, filled, jumped, melted, cried, hoped",,"Not pattern-based, depends on preceding sound","1.1,1.2,1.3,1.4,1.5,23.1","ed:final","verify_ed_pronunciation:true","",1,"using wrong ed sound","/t/ after unvoiced, /d/ after voiced, /ed/ after t or d","Listen to final sound"
18.1,Long a teams,vowel team,"ai, ay, ei, eigh, ey, ea",EY,long a sound,any,4-7,any,Must produce long a sound,"rain, play, vein, eight, neighbor, great, prey",aisle (different sound),,"1.1,1.2,1.3,1.4,1.5,11.0","ai:medial, ay:final, ei:medial, eigh:medial, ey:final, ea:medial","verify_pronunciation:true, min_5_per_pattern:true","said (ai=/e/), again (ai=/e/), against",1,"wrong team choice, ay in middle","ay at end, ai in middle","Position matters for teams"
18.2,Long e teams,vowel team,"ea, ee, ie, ey, ei",IY,long /e/ sound,any,4-7,any,Must produce long e sound,"eat, see, field, key, turkey, seize, seat","bread (short e), break (long a)",,"1.1,1.2,1.3,1.4,1.5,11.0","ea:any, ee:any, ie:medial, ey:final, ei:medial","verify_pronunciation:true, min_5_per_pattern:true","bread (ea=/e/), dead (ea=/e/), great (ea=/a/)",1,"ea confusion (3 sounds)","ea has 3 sounds - check carefully","Multiple pronunciations"
18.3,Long o teams,vowel team,"oa, oe, ow, ou",OW,long /o/ sound,any,4-7,any,Must produce long o sound,"boat, toe, grow, show, own, soul","cow (ou sound), shoe (oo sound)",,"1.1,1.2,1.3,1.4,1.5,11.0","oa:medial, oe:final, ow:final, ou:medial","verify_pronunciation:true, min_5_per_pattern:true","",1,"ow confusion (2 sounds)","ow can be long o or /ow/","Context determines sound"
18.4,"""ow"" diphthong teams",vowel team,"ou, ow",AW,"""ow"" as in cow",any,3-6,any,Must produce /ow/ diphthong,"out, house, cow, now, howl","soul (long o), low (long o)",Diphthong - two vowels blend together to make a completely unique sound,"1.1,1.2,1.3,1.4,1.5,11.0","ou:medial, ow:final","verify_pronunciation:true, min_5_per_pattern:true","",2,"using long o sound","Diphthong = new sound","Not long o sound"
18.5,Long i teams,vowel team,"ie, igh, ei, uy",AY,long /i/ sound,any,3-6,any,Must produce long i sound,"pie, tie, high, night, bright, guy, heist","field (long e), friend (short e)",,"1.1,1.2,1.3,1.4,1.5,11.0","ie:final, igh:medial, ei:medial, uy:final","verify_pronunciation:true, min_5_per_pattern:true","",2,"ie confusion (2 sounds)","ie at end = long i","Position affects sound"
18.6,Long u teams,vowel team,"ue, ew, eu",YUW,long /u/ sound,"ue/eu medial, ew often final",3-6,any,Must produce YUW sound,"argue, few, feud","sew (long o), build (short i)",,"1.1,1.2,1.3,1.4,1.5,11.0","ue:final, ew:final, eu:medial","verify_pronunciation:true, min_5_per_pattern:true","",3,"missing y glide","Long u often has /y/ sound first","Listen for /y/ glide"
18.7,Long oo teams,vowel team,oo,UW,long /oo/ as in moon,medial,4-7,any,Must produce long oo sound,"moon, food, group, tooth, fruit","book (short oo), blood (short u)",,"1.1,1.2,1.3,1.4,1.5,11.0","oo:medial","verify_pronunciation:true, distinguish_from_short_oo:true","",2,"oo confusion (2 sounds)","oo has 2 sounds","Context clues help"
18.8,Short oo teams,vowel team,oo,UH,short /oo/ as in book,medial,4-6,any,Must produce short oo sound,"book, look, good, wood, foot","moon (long oo), blood (short u)",,"1.1,1.2,1.3,1.4,1.5,11.0","oo:medial","verify_pronunciation:true, distinguish_from_long_oo:true","",2,"oo confusion (2 sounds)","oo has 2 sounds","Context clues help"
18.9,Short u teams,vowel team,ou,AH,short /u/ sound,medial,4-8,any,Must produce short u sound,"touch, young, country, double, trouble","out (ow sound), soul (long o)",,"1.1,1.2,1.3,1.4,1.5,11.0","ou:medial","verify_pronunciation:true","",3,"ou confusion (4 sounds)","ou has many sounds","Less common pattern"
18.10,"""aw"" diphthong teams",vowel team,"au, aw",AO,"""aw"" as in saw",medial or final,3-7,any,"Must produce ""aw"" sound","saw, launch, haul, paw, law, cause","laugh (short a), aunt (varies)",Diphthong - two vowels blend together to make a completely unique sound,"1.1,1.2,1.3,1.4,1.5,11.0","au:medial, aw:final","verify_pronunciation:true, min_5_per_pattern:true","laugh (au=/a/), aunt (varies)",2,"using short a","au in middle, aw at end","Position helps"
18.11,"""oy"" diphthong teams",vowel team,"oi, oy",OY,"""oy"" as in boy",medial or final,3-7,any,"Must produce ""oy"" sound","oil, coin, boy, toy, enjoy",choir (different sound),Diphthong - two vowels blend together to make a completely unique sound,"1.1,1.2,1.3,1.4,1.5,11.0","oi:medial, oy:final","verify_pronunciation:true, min_5_per_pattern:true","",2,"wrong team position","oi in middle, oy at end","Position rule is consistent"
18.12,Short e teams,vowel team,ea,EH,short /e/ sound,medial,4-8,any,Must produce short e sound,"bread, head, dead, spread, heavy","eat (long e), break (long a)",,"1.1,1.2,1.3,1.4,1.5,11.0,18.2","ea:medial","verify_pronunciation:true","",3,"using long e","ea has 3 sounds","Less common ea sound"
19.1,Homophones - silent letters,homophones,kn/n; wr/r; wh/w; mb/m; gn/n; ps/s,matching pairs,silent letter creates same sound as single letter,any position,4-9,any,One spelling has silent letter that makes it sound like simpler spelling,"know/no, knight/night, write/right, whole/hole, lamb/lam, gnome/nome, psalm/salm","now (not a homophone), knit (no homophone pair)",Silent letters learned in skill 16.0 can create homophones,16.0,"","verify_homophones:true","",3,"confusing meanings","Same sound, different spelling and meaning","Vocabulary building"
19.2,Homophones - vowel team variants,homophones,ai/a-e/ay; ee/ea/e-e; oa/o-e/ow; ew/oo/ue; au/aw/a; or/ore/oar,matching pairs,different vowel patterns making same sound,varies by pattern,4-9,any,Different spelling patterns for same vowel sound create homophones,"mail/male, rain/reign/rein, meet/meat/mete, road/rode/rowed, new/knew/gnu, paws/pause, for/four/fore","main/man (different sounds), beat/bet (different sounds)","Requires knowledge of multiple vowel teams (skills 18.1-18.12, 11.0)","18.1,18.2,18.3,11.0","","verify_homophones:true","",3,"confusing patterns","Different spellings, same sound","Pattern variations"
19.3,Homophones - grammar/function words,homophones,to/too/two; there/their/they're; your/you're; its/it's; for/four; by/buy/bye; our/hour,matching pairs,same sound but different meaning/function,any,4-9,any,Common words with different meanings/parts of speech that sound identical,"to/too/two, there/their/they're, your/you're, its/it's, by/buy/bye, our/hour, I/eye, we/wee","then/than (near-homophones), of/off (different sounds)",These are high-frequency words that often need explicit teaching,"1.1,1.2,1.3,1.4,1.5","","verify_homophones:true","",1,"mixing up usage","Know meaning to choose spelling","High-frequency confusion"
20.1,er sound,r-controlled,"er, ir, ur",ER,/er/ as in her,medial or final,3-10,any,All three spellings make same sound,"her, bird, turn, fern, first",,,"1.1,1.2,1.3,1.4,1.5","er:any, ir:any, ur:any","verify_r_control:true, exclude_split_syllables:true","were (er=/air/), where (er=/air/)",1,"separating vowel and r","Three spellings, one sound","R controls the vowel"
20.2,or sound,r-controlled,or,AO R or AO,/or/ as in for,medial or final,3-10,any,Consistent /or/ sound,"for, corn, sport, north, born",,,"1.1,1.2,1.3,1.4,1.5","or:any, ore:final","verify_r_control:true, exclude_split_syllables:true","",1,"using er sound","or says /or/","Consistent sound"
20.3,ar sound,r-controlled,ar,AA R,/ar/ as in car,medial or final,3-10,any,Consistent /ar/ sound,"car, star, park, hard, start",,,"1.1,1.2,1.3,1.4,1.5","ar:any","verify_r_control:true, exclude_split_syllables:true","",1,"using er sound","ar says /ar/","Consistent sound"
20.4,war sound,r-controlled,war,AO R,/or/ as in war,medial or final,3-10,any,w changes ar to or sound,"war, warm, warn, wart, dwarf",,,"20.3","war:initial","verify_r_control:true, w_changes_sound:true","",3,"using ar sound","w changes ar to /or/","W affects the sound"
20.5,wor sound,r-controlled,wor,ER,/er/ as in work,medial or final,3-10,any,w changes or to er sound,"work, word, world, worm, worth",,,"20.2","wor:initial","verify_r_control:true, w_changes_sound:true","",3,"using or sound","w changes or to /er/","W affects the sound"
21.1,Starfish Words - Compound,syllabication,compound,structural,two complete words joined,between word boundaries,6-12,word+word,Find the two words in the compound word and divide between them,"star/fish, cup/cake, rain/bow, foot/ball, play/ground, sun/flower","starting (not compound), carpet (not compound)",Easiest pattern - students already know both words,"1.1,1.2,1.3,1.4,1.5,8.0","","identify_word_boundaries:true","",1,"wrong division point","Find two complete words","Known words combined"
21.2,Retriever Words - Prefix/Suffix,syllabication,prefix/suffix,structural,affix boundaries,after prefix or before suffix,5-10,prefix+root or root+suffix,"Circle prefix/suffix, divide after prefix or before suffix","re/turn, un/tie, jump/ing, tall/er, pre/view, hope/ful","return (if prefix not recognized), jumping (if suffix not recognized)",Requires knowledge of common prefixes/suffixes,"1.1,1.2,1.3,1.4,1.5,22.0,23.1","","identify_affix_boundaries:true","",2,"dividing through affix","Keep prefix/suffix as unit","Morpheme boundaries"
21.3,Lion Words - Vowel/Vowel,syllabication,V/V,structural,two vowels meet,between adjacent vowels,5-10,vowel|vowel,Two vowels next to each other divide between them (makes first vowel open),"li/on, di/al, cre/ate, po/em, gi/ant, flu/id","rain (vowel team), boat (vowel team)",Not vowel teams - separate vowel sounds,"1.1,1.2,1.3,1.4,1.5,18.1","","verify_separate_vowels:true","",3,"treating as vowel team","Not a team - separate sounds","First vowel is long"
21.4,Rabbit Words - VC/CV,syllabication,VC/CV,structural,two consonants between vowels,between the consonants,3-12,vowel-CC-vowel,"Two consonants between vowels, divide between consonants","rab/bit, nap/kin, win/ter, hap/pen, sud/den, les/son","letter (double = one sound), pickle (ck = one unit)",Most common pattern in English,"1.1,1.2,1.3,1.4,1.5,4.1,5.1,6.1","","verify_two_consonants:true","",1,"not dividing between","Divide between consonants","Most common pattern"
21.5,Ostrich Words - VCC/CV,syllabication,VCC/CV,structural,"When there are 3 consonants, we have to decide which 2 stay together (keep blends together). Finding syllable patterns and sounding them out helps to decide where to split.",keep blends/digraphs together,3-12,vowel-CC|C-vowel,"Three consonants, keep blends/digraphs together, divide before blend","ost/rich, sand/wich, ath/lete, pump/kin","hamster (divide differently), monster (divide differently)",ost/rich because when we split there it keeps the st blend together.,"21.4,5.1,5.2,5.3","","keep_blends_together:true","",3,"splitting blend/digraph","Keep blends together","Blend stays as unit"
21.6,Hamster Words - VC/CCV,syllabication,VC/CCV,structural,"When there are 3 consonants, we have to decide which 2 stay together (keep blends together). Finding syllable patterns and sounding them out helps to decide where to split.",keep blends/digraphs together,3-12,vowel-C|CC-vowel,"Three consonants, keep blends/digraphs together, divide before blend",ham/ster because when we split there it keeps the st blend together.,,ham/ster because when we split there it keeps the st blend together.,"21.4,5.1,5.2,5.3","","keep_blends_together:true","",3,"splitting blend/digraph","Keep blends together","Blend stays as unit"
21.7,Tiger Words - V/CV,syllabication,V/CV,structural,Find the consonant between the vowels and divide the word after the first vowel. The first syllable will be open.,after first vowel,3-12,vowel|C-vowel,One consonant between vowels divide after first vowel (open syllable),"ti/ger, pa/per, mu/sic, ro/bot, ba/by, e/ven","camel (divide differently), never (divide differently)",First syllable is open (long vowel),"1.1,1.2,1.3,1.4,1.5","","verify_open_syllable:true","",2,"dividing after consonant","Try V/CV first","First vowel is long"
21.8,Camel Words - VC/V,syllabication,VC/V,structural,Find the consonant between the vowels. Divide these words after the consonant and before the second vowel. The first syllable is closed.,after consonant,3-12,vowel-C|vowel,One consonant between vowels divide after consonant (closed syllable),"cam/el, nev/er, lim/it, vis/it, rob/in, sev/en","tiger (divide differently), paper (divide differently)",First syllable is closed (short vowel),"1.1,1.2,1.3,1.4,1.5","","verify_closed_syllable:true","",2,"dividing before consonant","If V/CV doesn't work, try VC/V","First vowel is short"
21.9,Turtle Words - C+le,syllabication,Cle,structural,consonant + le ending,count back 3,3-12,final Cle syllable,Word ends in consonant + le count back 3 letters to divide,"tur/tle, ap/ple, ta/ble, sim/ple, mid/dle, puz/zle","battle (if not recognized), let (not C+le ending)",Final syllable is always consonant+le,"1.1,1.2,1.3,1.4,1.5","","count_back_three:true","",1,"wrong division point","Count back 3 from end","C+le is a syllable"
22.0,Common prefixes,morphology,"un-, re-, pre-, dis-, mis-",various,word beginnings,initial,5-15,any,Added to beginning of base word,"undo, replay, preview, disagree, mistake",,Meaning-changing units at word beginning,"1.1,1.2,1.3,1.4,1.5","un:initial, re:initial, pre:initial, dis:initial, mis:initial","identify_base_word:true","",1,"not recognizing prefix","Prefix changes meaning","Beginning of word"
23.1,Common suffixes,morphology,"-ing, -ed, -er, -est, -ly",various,word endings,final,5-15,any,Added to end of base word,"jumping, walked, faster, happily, teacher",,Grammatical units at word ending,"1.1,1.2,1.3,1.4,1.5","ing:final, ed:final, er:final, est:final, ly:final","identify_base_word:true","",1,"not recognizing suffix","Suffix changes grammar","End of word"
23.2,Irregular suffixes,morphology,"-tion, -sion, -ture",various,special suffixes,final,5-15,any,Change pronunciation of base,"nation, vision, picture, action, mission",,Special pronunciation patterns,"23.1","tion:final, sion:final, ture:final","verify_pronunciation_change:true","",2,"mispronouncing","tion says /shun/","Sound changes"
24.0,1-1-1 doubling,spelling rule,double final consonant,various,double before suffix,any position,3-12,any,"When the base word has one syllable, one vowel before the final consonant, one final consonant, then you double the final consonant.","hop/hopping, sit/sitting, run/running, swim/swimming",,CVC pattern doubles final consonant when adding ing,"1.1,1.2,1.3,1.4,1.5,23.1","","check_1_1_1_pattern:true","",1,"not doubling, doubling wrong letter","1 syllable, 1 vowel, 1 final consonant","Protects short vowel"
25.0,E-dropping rule,spelling rule,drop e before vowel suffix,various,drop silent e,any position,5-12,any,Drop e before adding vowel suffix,"make/making, hope/hoping, care/caring, take/taking, bake/baking",,Drop silent e before vowel suffix,"11.0,23.1","","drop_e_before_vowel_suffix:true","",1,"keeping the e","Drop e before vowel suffix","Prevents double vowel"
26.0,Change y to i,spelling rule,y to i,various,y changes to i,any position,5-12,any,Change y to i before suffix (except -ing),"happy/happier, cry/cried, fly/flies, baby/babies, funnier",,Spelling change before suffixes,"14.1,14.2,23.1","","y_to_i_except_ing:true","",1,"not changing y, changing before -ing","Change y to i (except -ing)","Keep y before -ing"
27.1,Plural -s,plural rule,-s,S or Z,regular plural,any position,3-12,any,Add -s to most nouns,"cats, dogs, books, cars, toys",,Regular plural formation,"1.1,1.2,1.3,1.4,1.5","s:final","regular_plural:true","",1,"using -es","Most words just add -s","Default plural"
27.2,Plural -es,plural rule,-es,IH Z,"plural after s,x,z,ch,sh",any position,4-12,any,Add -es after sibilant sounds,"boxes, wishes, churches, bushes, glasses",,Plural after sibilant sounds,"4.1,4.2,27.1","es:final","after_sibilant:true","",1,"using just -s","Hissing sounds need -es","Extra syllable added"
27.3,f to v plural,plural rule,f/fe to ves,V Z,knife → knives,any position,4-12,any,Change f/fe to v and add -es,"knives, wolves, leaves, lives, shelves",,Special spelling change for plural,"27.1","ves:final","f_to_v_change:true","",2,"just adding -s","f changes to v","Special group"
27.5,Irregular plurals,plural rule,various,various,man → men,any position,3-12,any,Must be memorized,"children, men, feet, mice, geese",,No pattern - must memorize,"27.1","","memorize_forms:true","",2,"adding -s","No pattern - memorize","Special forms"
28.0,2-1-1 doubling,spelling rule,double in 2-syllable words,various,begin → beginning,any position,6-15,any,Stress on second syllable,"beginning, forgetting, occurring, referring",,Like 24.0 but for 2-syllable words,"24.0,21.1","","check_stress_pattern:true","",3,"not checking stress","Stress on 2nd syllable","Advanced doubling"
29.0,Silent e not plural,spelling rule,VCe,various,distinguish from plural,any position,4-10,any,Distinguish rose (flower) vs. rows (plural),"rose, hope, care, love, home",,Distinguishing homophones,"11.0,27.1","","distinguish_from_plural:true","",3,"confusing with plural","Not all final -e is plural","Context matters"
30.0,Contractions,punctuation,apostrophe,various,"can't, won't, it's",any position,3-10,any,Apostrophe shows missing letters,"can't, won't, it's, I'll, they're",,Two words combined with apostrophe,"1.1,1.2,1.3,1.4,1.5","","identify_missing_letters:true","",1,"confusing with possessive","Apostrophe = missing letters","Two words combined"