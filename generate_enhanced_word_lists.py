#!/usr/bin/env python3
"""
Enhanced Word List Generator using the Complete Enhanced CSV
Generates decodable word lists with full position awareness and prerequisite checking
"""

import nltk
import pandas as pd
import os
import re
from collections import defaultdict
from datetime import datetime
import json

class EnhancedWordListGenerator:
    def __init__(self, csv_path='enhanced_phonics_master_rules_complete.csv'):
        print("Initializing Enhanced Word List Generator...")
        print("=" * 60)
        
        # Load the enhanced CSV with skill_id as string to prevent float conversion
        self.rules_df = pd.read_csv(csv_path, dtype={'skill_id': str})
        print(f"✓ Loaded {len(self.rules_df)} phonics rules from enhanced CSV")
        
        # Load CMU dictionary for phoneme checking
        try:
            self.cmu_dict = nltk.corpus.cmudict.dict()
            print(f"✓ Loaded CMU dictionary with {len(self.cmu_dict)} words")
        except:
            print("❌ CMU dictionary not found. Running download...")
            nltk.download('cmudict')
            self.cmu_dict = nltk.corpus.cmudict.dict()
        
        # Load frequency data
        try:
            self.freq_dist = self._load_frequency_data()
            print(f"✓ Loaded frequency data")
        except:
            print("❌ Brown corpus not found. Running download...")
            nltk.download('brown')
            self.freq_dist = self._load_frequency_data()
        
        # Parse skill prerequisites
        self.prerequisites = self._parse_prerequisites()
        print(f"✓ Parsed prerequisite chains")
        
        # Load heart words
        self.heart_words = self._load_heart_words()
        print(f"✓ Loaded heart words")
        
        # Load inappropriate words filter
        self.inappropriate_words = self._load_inappropriate_words_filter()
        print(f"✓ Loaded {len(self.inappropriate_words)} inappropriate words to filter")
        
        # Create output directory
        self.output_dir = 'generated_word_lists'
        os.makedirs(self.output_dir, exist_ok=True)
        
        print("\nInitialization complete!")
        print("=" * 60)
    
    def _load_frequency_data(self):
        """Load word frequency from Brown corpus"""
        brown_words = nltk.corpus.brown.words()
        return nltk.FreqDist(word.lower() for word in brown_words)
    
    def _parse_prerequisites(self):
        """Parse prerequisite skills from CSV"""
        prerequisites = {}
        for _, row in self.rules_df.iterrows():
            skill_id = str(row['skill_id'])
            prereq_str = str(row['prerequisite_skills'])
            
            if pd.notna(prereq_str) and prereq_str.strip():
                prerequisites[skill_id] = [p.strip() for p in prereq_str.split(',')]
            else:
                prerequisites[skill_id] = []
        
        return prerequisites
    
    def _load_heart_words(self):
        """Load heart words by skill from CSV"""
        heart_words = defaultdict(set)
        
        for _, row in self.rules_df.iterrows():
            skill_id = str(row['skill_id'])
            triggers = str(row['heart_word_triggers'])
            
            if pd.notna(triggers) and triggers.strip():
                # Parse heart words, handling parenthetical notes
                words = re.findall(r'(\w+)(?:\s*\([^)]*\))?', triggers)
                heart_words[skill_id].update(word.lower() for word in words if word)
        
        return heart_words
    
    def _load_inappropriate_words_filter(self):
        """Load list of inappropriate words to filter out"""
        inappropriate = set()
        filter_path = 'inappropriate_words_filter.txt'
        
        if os.path.exists(filter_path):
            with open(filter_path, 'r') as f:
                for line in f:
                    line = line.strip().lower()
                    # Skip comments and empty lines
                    if line and not line.startswith('#'):
                        inappropriate.add(line)
        else:
            print(f"⚠️  Warning: {filter_path} not found. No content filtering will be applied.")
        
        return inappropriate
    
    def get_available_patterns_at_skill(self, target_skill_id):
        """Get all patterns available up to and including a skill"""
        available_patterns = set()
        available_skills = set()
        
        # Build skill order from prerequisites
        def add_skill_and_prereqs(skill_id):
            if skill_id in available_skills:
                return
            
            available_skills.add(skill_id)
            
            # Add prerequisites first
            for prereq in self.prerequisites.get(skill_id, []):
                add_skill_and_prereqs(prereq)
        
        # Add target skill and all its prerequisites
        add_skill_and_prereqs(target_skill_id)
        
        # Collect patterns from all available skills
        pattern_details = []
        for skill_id in available_skills:
            skill_row = self.rules_df[self.rules_df['skill_id'] == skill_id]
            if not skill_row.empty:
                row = skill_row.iloc[0]
                graphemes = str(row['graphemes'])
                if pd.notna(graphemes):
                    pattern_details.append({
                        'skill_id': skill_id,
                        'graphemes': graphemes,
                        'position_constraints': str(row['position_constraints']),
                        'position_by_grapheme': str(row['position_by_grapheme'])
                    })
        
        return available_skills, pattern_details
    
    def check_word_position_constraints(self, word, pattern, position_constraint, position_by_grapheme=None):
        """Check if a pattern appears in the required position in a word"""
        word_lower = word.lower()
        
        # If we have specific position for this grapheme
        if position_by_grapheme and pd.notna(position_by_grapheme):
            positions = {}
            for mapping in position_by_grapheme.split(', '):
                if ':' in mapping:
                    grapheme, pos = mapping.split(':')
                    positions[grapheme.strip()] = pos.strip()
            
            if pattern in positions:
                position_constraint = positions[pattern]
        
        # Check position
        if position_constraint == 'initial' or position_constraint == 'initial only':
            return word_lower.startswith(pattern)
        elif position_constraint == 'final' or position_constraint == 'final only':
            return word_lower.endswith(pattern)
        elif position_constraint == 'medial':
            return pattern in word_lower and not word_lower.startswith(pattern) and not word_lower.endswith(pattern)
        elif position_constraint == 'initial or final':
            return word_lower.startswith(pattern) or word_lower.endswith(pattern)
        elif position_constraint == 'medial or final':
            return pattern in word_lower and not word_lower.startswith(pattern)
        elif position_constraint == 'initial_or_final':
            return word_lower.startswith(pattern) or word_lower.endswith(pattern)
        else:
            # For 'any', 'various', or other unspecified positions
            return pattern in word_lower
    
    def generate_words_for_skill(self, skill_id, max_words=50):
        """Generate words for a specific skill"""
        skill_row = self.rules_df[self.rules_df['skill_id'] == skill_id]
        if skill_row.empty:
            print(f"Skill {skill_id} not found!")
            return {
                'skill_id': skill_id,
                'skill_name': f'Unknown skill {skill_id}',
                'regular_words': [],
                'heart_words': []
            }
        
        row = skill_row.iloc[0]
        skill_name = row['skill_name']
        pattern_type = row['pattern_type']
        
        print(f"\nGenerating words for Skill {skill_id}: {skill_name}")
        print("-" * 50)
        
        # Get available patterns (respecting prerequisites)
        available_skills, pattern_details = self.get_available_patterns_at_skill(skill_id)
        print(f"Available skills: {sorted(available_skills)}")
        
        # Get target patterns for this skill
        target_graphemes = str(row['graphemes']).split(', ') if pd.notna(row['graphemes']) else []
        position_constraint = str(row['position_constraints'])
        position_by_grapheme = str(row['position_by_grapheme'])
        word_length = str(row['word_length'])
        special_rules = str(row['special_rules'])
        
        # Parse word length constraints
        min_length, max_length = 2, 15  # defaults
        if pd.notna(word_length):
            if '-' in word_length:
                parts = word_length.split('-')
                min_length = int(parts[0])
                max_length = int(parts[1])
            else:
                try:
                    min_length = max_length = int(word_length)
                except:
                    pass
        
        print(f"Target patterns: {target_graphemes}")
        print(f"Position constraints: {position_constraint}")
        print(f"Word length: {min_length}-{max_length}")
        
        # Collect candidate words
        candidates = []
        
        # Check if this skill has specific words only
        if 'Limited to specific words:' in special_rules:
            # Extract specific words from special rules
            match = re.search(r'Limited to specific words: (.+)', special_rules)
            if match:
                specific_words = [w.strip() for w in match.group(1).split(',')]
                for word in specific_words:
                    if min_length <= len(word) <= max_length and word.lower() not in self.inappropriate_words:
                        candidates.append({
                            'word': word,
                            'frequency': self.freq_dist.get(word.lower(), 1),
                            'pattern': 'specific',
                            'is_heart_word': False
                        })
        else:
            # Search CMU dictionary for pattern matches
            for word, pronunciations in self.cmu_dict.items():
                # Skip if wrong length
                if not (min_length <= len(word) <= max_length):
                    continue
                
                # Skip if contains apostrophe (unless contractions skill)
                if "'" in word and skill_id != '30.0':
                    continue
                
                # Skip if word is inappropriate
                if word.lower() in self.inappropriate_words:
                    continue
                
                # Check if word contains target pattern with correct position
                word_matches = False
                matched_pattern = None
                
                for pattern in target_graphemes:
                    if self.check_word_position_constraints(word, pattern, position_constraint, position_by_grapheme):
                        word_matches = True
                        matched_pattern = pattern
                        break
                
                if word_matches:
                    # Additional checks based on pattern type
                    if pattern_type == 'CVC' and not self._is_cvc_word(word):
                        continue
                    
                    if 'after short vowel' in special_rules and not self._has_short_vowel_before_pattern(word, matched_pattern):
                        continue
                    
                    if 'one-syllable' in special_rules and self._count_syllables(pronunciations[0]) > 1:
                        continue
                    
                    # Check if it's a heart word at current level
                    is_heart = False
                    for skill in available_skills:
                        if word.lower() in self.heart_words.get(skill, set()):
                            is_heart = True
                            break
                    
                    candidates.append({
                        'word': word.lower(),
                        'frequency': self.freq_dist.get(word.lower(), 0),
                        'pattern': matched_pattern,
                        'is_heart_word': is_heart
                    })
        
        # Sort by frequency and limit
        candidates.sort(key=lambda x: x['frequency'], reverse=True)
        
        # Apply generation constraints
        generation_constraints = str(row['generation_constraints'])
        if pd.notna(generation_constraints) and 'prefer_high_frequency:true' in generation_constraints:
            # Already sorted by frequency
            pass
        
        # Separate regular words and heart words
        regular_words = [c for c in candidates if not c['is_heart_word']][:max_words]
        heart_words_found = [c for c in candidates if c['is_heart_word']][:10]
        
        print(f"\nFound {len(regular_words)} decodable words")
        if heart_words_found:
            print(f"Found {len(heart_words_found)} heart words at this level")
        
        return {
            'skill_id': skill_id,
            'skill_name': skill_name,
            'regular_words': regular_words,
            'heart_words': heart_words_found
        }
    
    def _is_cvc_word(self, word):
        """Check if word follows CVC pattern"""
        if len(word) != 3:
            return False
        
        vowels = 'aeiou'
        return (word[0] not in vowels and 
                word[1] in vowels and 
                word[2] not in vowels)
    
    def _has_short_vowel_before_pattern(self, word, pattern):
        """Check if pattern is preceded by short vowel"""
        idx = word.find(pattern)
        if idx > 0:
            # This is simplified - ideally check phonemes
            return word[idx-1] in 'aeiou'
        return False
    
    def _count_syllables(self, phonemes):
        """Count syllables based on phoneme list"""
        vowel_phonemes = ['AA', 'AE', 'AH', 'AO', 'AW', 'AY', 'EH', 'ER', 
                         'EY', 'IH', 'IY', 'OW', 'OY', 'UH', 'UW']
        count = 0
        for p in phonemes:
            if any(v in p for v in vowel_phonemes):
                count += 1
        return max(1, count)
    
    def generate_all_word_lists(self):
        """Generate word lists for all skills"""
        print("\n" + "=" * 60)
        print("GENERATING WORD LISTS FOR ALL SKILLS")
        print("=" * 60)
        
        all_results = {}
        summary_data = []
        
        # Group by skill level for organized output
        skill_groups = defaultdict(list)
        for _, row in self.rules_df.iterrows():
            skill_id = str(row['skill_id'])
            level = skill_id.split('.')[0]
            skill_groups[level].append(skill_id)
        
        # Generate for each skill group
        for level in sorted(skill_groups.keys(), key=lambda x: float(x) if x.replace('.','').isdigit() else 100):
            print(f"\n{'='*60}")
            print(f"LEVEL {level} SKILLS")
            print(f"{'='*60}")
            
            for skill_id in sorted(skill_groups[level]):
                result = self.generate_words_for_skill(skill_id)
                all_results[skill_id] = result
                
                # Save individual skill file
                self._save_skill_word_list(result)
                
                # Add to summary
                summary_data.append({
                    'skill_id': skill_id,
                    'skill_name': result['skill_name'],
                    'regular_words': len(result['regular_words']),
                    'heart_words': len(result['heart_words']),
                    'total_words': len(result['regular_words']) + len(result['heart_words'])
                })
        
        # Save summary
        summary_df = pd.DataFrame(summary_data)
        summary_df.to_csv(os.path.join(self.output_dir, 'generation_summary.csv'), index=False)
        
        print("\n" + "=" * 60)
        print("GENERATION COMPLETE!")
        print(f"Generated word lists for {len(all_results)} skills")
        print(f"Output directory: {self.output_dir}")
        print("=" * 60)
        
        return all_results
    
    def _save_skill_word_list(self, result):
        """Save word list for a single skill"""
        skill_id = result['skill_id']
        skill_name = result['skill_name']
        
        # Clean filename
        safe_name = re.sub(r'[^\w\s-]', '', skill_name)
        safe_name = re.sub(r'[-\s]+', '_', safe_name)
        
        filename = f"skill_{skill_id}_{safe_name}.csv"
        filepath = os.path.join(self.output_dir, filename)
        
        # Prepare data for CSV
        data = []
        
        # Add regular words
        for item in result['regular_words']:
            data.append({
                'word': item['word'],
                'type': 'decodable',
                'pattern': item['pattern'],
                'frequency_rank': item['frequency']
            })
        
        # Add heart words
        for item in result['heart_words']:
            data.append({
                'word': item['word'],
                'type': 'heart_word',
                'pattern': item['pattern'],
                'frequency_rank': item['frequency']
            })
        
        # Save to CSV
        if data:
            df = pd.DataFrame(data)
            df.to_csv(filepath, index=False)
            print(f"  Saved: {filename}")
    
    def generate_progressive_lists(self):
        """Generate progressive word lists showing skill building"""
        print("\n" + "=" * 60)
        print("GENERATING PROGRESSIVE WORD LISTS")
        print("=" * 60)
        
        progressive_data = []
        cumulative_words = set()
        
        # Process skills in order
        skill_order = []
        for _, row in self.rules_df.iterrows():
            skill_id = str(row['skill_id'])
            # Ensure proper ordering by padding numbers
            parts = skill_id.split('.')
            if len(parts) == 2:
                ordered_id = f"{int(parts[0]):03d}.{int(parts[1]):02d}"
            else:
                ordered_id = f"{int(parts[0]):03d}.00"
            skill_order.append((ordered_id, skill_id))
        
        skill_order.sort()
        
        for _, skill_id in skill_order:
            available_skills, _ = self.get_available_patterns_at_skill(skill_id)
            
            # Generate words for this skill
            result = self.generate_words_for_skill(skill_id)
            
            # Get new words introduced at this level
            current_words = {item['word'] for item in result['regular_words']}
            new_words = current_words - cumulative_words
            cumulative_words.update(current_words)
            
            progressive_data.append({
                'skill_id': skill_id,
                'skill_name': result['skill_name'],
                'total_available_skills': len(available_skills),
                'new_words_introduced': len(new_words),
                'cumulative_words': len(cumulative_words),
                'sample_new_words': ', '.join(list(new_words)[:5])
            })
        
        # Save progressive analysis
        prog_df = pd.DataFrame(progressive_data)
        prog_df.to_csv(os.path.join(self.output_dir, 'progressive_word_analysis.csv'), index=False)
        
        print(f"\nProgressive analysis saved!")
        print(f"Total cumulative words: {len(cumulative_words)}")

def main():
    """Main execution"""
    generator = EnhancedWordListGenerator('enhanced_phonics_master_rules_complete.csv')
    
    # Generate all word lists
    generator.generate_all_word_lists()
    
    # Generate progressive analysis
    generator.generate_progressive_lists()
    
    print("\n✅ All word lists generated successfully!")
    print(f"Check the '{generator.output_dir}' directory for results.")

if __name__ == "__main__":
    main()
