#!/usr/bin/env python3
"""
Example of how to use the configuration files in your word generator
"""

import pandas as pd
from skill_config import SKILL_CONFIGS, get_skill_config, get_phoneme_requirement

def show_configuration_usage():
    """Demonstrate how to use the configuration files"""
    
    print("HOW TO USE SKILL CONFIGURATIONS IN YOUR WORD GENERATOR")
    print("=" * 70)
    
    # Method 1: Using the CSV file
    print("\nMETHOD 1: Using the CSV file")
    print("-" * 40)
    
    # Load the CSV
    df = pd.read_csv('og_skill_pattern_configurations.csv')
    
    # Get configuration for skill 18.5
    skill_18_5 = df[df['skill_id'] == '18.5'].iloc[0]
    
    print(f"Skill {skill_18_5['skill_id']}: {skill_18_5['skill_name']}")
    print(f"Graphemes: {skill_18_5['grapheme_patterns']}")
    print(f"Phoneme required: {skill_18_5['phoneme_requirement']}")
    print(f"Word length: {skill_18_5['word_length']}")
    
    # Method 2: Using the Python module
    print("\n\nMETHOD 2: Using the Python module")
    print("-" * 40)
    
    config = get_skill_config('18.5')
    print(f"Skill 18.5: {config['name']}")
    print(f"Patterns: {config['patterns']['vowel_teams']}")
    print(f"Required phoneme: {config['phoneme']}")
    print(f"Word length: {config['word_length']['min']}-{config['word_length']['max']}")
    print(f"Needs phoneme check: {config.get('phoneme_check', False)}")
    
    # Show how to use in word generation
    print("\n\nIN YOUR WORD GENERATOR:")
    print("-" * 40)
    print("""
def generate_words_for_skill(skill_id):
    # Get configuration
    config = get_skill_config(skill_id)
    
    # Extract requirements
    patterns = config['patterns'].get('vowel_teams', [])
    phoneme_req = config.get('phoneme')
    word_length = config.get('word_length', {'min': 3, 'max': 10})
    
    # Use these to filter words
    for word in word_candidates:
        # Check word length
        if not (word_length['min'] <= len(word) <= word_length['max']):
            continue
            
        # Check pattern
        if not any(pattern in word for pattern in patterns):
            continue
            
        # Check phoneme (if required)
        if config.get('phoneme_check'):
            if not check_phoneme(word, phoneme_req):
                continue
                
        # Word passes all checks!
        valid_words.append(word)
    """)
    
    # Show the vowel team reference
    print("\n\nVOWEL TEAM QUICK REFERENCE:")
    print("-" * 40)
    
    vt_df = pd.read_csv('vowel_team_phoneme_requirements.csv')
    
    for _, row in vt_df.iterrows():
        if row['skill_id'] == '18.5':
            print(f"\nSkill {row['skill_id']}: {row['target_sound']}")
            print(f"Patterns: {row['patterns']}")
            print(f"CMU Phoneme: {row['phoneme']}")
            print(f"✓ Correct examples: {row['examples_correct']}")
            print(f"✗ Wrong examples: {row['examples_wrong']}")

def main():
    show_configuration_usage()
    
    print("\n\nKEY BENEFITS:")
    print("=" * 70)
    print("1. All skill requirements in one place")
    print("2. Easy to modify without changing code")
    print("3. Clear phoneme requirements prevent errors")
    print("4. Word length constraints built in")
    print("5. Can be used by multiple generators")
    
    print("\n\nTO MODIFY REQUIREMENTS:")
    print("-" * 40)
    print("1. Edit og_skill_pattern_configurations.csv")
    print("2. Or edit skill_config.py")
    print("3. Changes automatically apply to your generator")

if __name__ == "__main__":
    main()
