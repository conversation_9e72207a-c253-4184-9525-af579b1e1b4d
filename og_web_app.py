# File: og_web_app.py
"""
Streamlit Web Interface for Orton-Gillingham Word List Generator
Run with: streamlit run og_web_app.py
"""

import streamlit as st
import pandas as pd
import os
import json
import zipfile
from io import BytesIO
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime
import random

# Import the generator class from the main script
from orton_gillingham_generator import OrtonGillinghamCompleteGenerator
from decodable_passage_generator import DecodablePassageGenerator
from word_search_generator import WordSearchGenerator

# Page configuration
st.set_page_config(
    page_title="Orton-Gillingham Word List Generator",
    page_icon="📚",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #1e3d59;
        text-align: center;
        margin-bottom: 2rem;
    }
    .sub-header {
        font-size: 1.5rem;
        color: #3e5c76;
        margin-bottom: 1rem;
    }
    .skill-card {
        background-color: #f5f5f5;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
        border-left: 4px solid #1e3d59;
    }
    .stats-card {
        background-color: #e8f4f8;
        padding: 1.5rem;
        border-radius: 8px;
        text-align: center;
    }
    .download-button {
        background-color: #28a745;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        text-decoration: none;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'generator' not in st.session_state:
    with st.spinner("Initializing Orton-Gillingham Generator..."):
        st.session_state.generator = OrtonGillinghamCompleteGenerator()

if 'generated_words' not in st.session_state:
    st.session_state.generated_words = {}

if 'custom_words' not in st.session_state:
    st.session_state.custom_words = {}

if 'passage_generator' not in st.session_state:
    st.session_state.passage_generator = None

if 'puzzle_generator' not in st.session_state:
    st.session_state.puzzle_generator = WordSearchGenerator()

# Helper functions
def get_skill_categories():
    """Group skills by category"""
    categories = {
        "Foundation (1-5)": ["1.1", "1.2", "1.3", "1.4", "1.5", "2.1", "2.2", "2.3", "3.1", "3.2", 
                           "4.1", "4.2", "4.3", "4.4", "4.5", "5.1", "5.2", "5.3", "5.4"],
        "Blends & Rules (6-10)": ["6.1", "6.2", "6.3", "6.4", "6.5", "6.6", "6.7", "6.8", "6.9", 
                                "6.10", "6.11", "6.12", "6.13", "6.14", "6.15", "7.0", "8.0", 
                                "9.0", "10.0"],
        "Intermediate (11-15)": ["11.0", "12.1", "13.1", "13.2", "14.1", "15.1", "15.2", "15.3"],
        "Advanced Patterns (16-20)": ["16.0", "17.0", "18.1", "18.2", "18.3", "18.4", "18.5", 
                                    "18.6", "18.7", "18.8", "18.9", "18.10", "18.11", "18.12", 
                                    "19.0", "20.1", "20.2", "20.3", "20.4", "20.5"],
        "Syllabication & Morphology (21-30)": ["21.1", "21.2", "21.3", "21.4", "21.5", "21.6", 
                                              "21.7", "21.8", "22.0", "23.1", "23.2", "24.0", 
                                              "25.0", "26.0", "27.1", "27.2", "27.3", "27.4", 
                                              "27.5", "28.0", "29.0", "30.0"]
    }
    return categories

def generate_words_for_skill(skill_id, max_words=50, strict=True):
    """Generate words for a specific skill"""
    generator = st.session_state.generator
    
    with st.spinner(f"Generating words for skill {skill_id}..."):
        words = generator.generate_for_skill(skill_id)
        if words:
            words = generator.filter_words(
                words, 
                skill_id=skill_id,
                max_words=max_words,
                strict_appropriate=strict
            )
        return words

def create_download_link(df, filename, link_text="Download CSV"):
    """Create a download link for a dataframe"""
    csv = df.to_csv(index=False)
    b64 = BytesIO(csv.encode()).read()
    return f'<a href="data:file/csv;base64,{b64.decode()}" download="{filename}" class="download-button">{link_text}</a>'

def create_zip_download(dataframes_dict):
    """Create a zip file containing multiple CSV files"""
    zip_buffer = BytesIO()
    
    with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
        for filename, df in dataframes_dict.items():
            csv_buffer = BytesIO()
            df.to_csv(csv_buffer, index=False)
            zip_file.writestr(filename, csv_buffer.getvalue())
    
    return zip_buffer.getvalue()

# Main App
st.markdown('<h1 class="main-header">📚 Orton-Gillingham Word List Generator</h1>', unsafe_allow_html=True)

# Sidebar
with st.sidebar:
    st.markdown("## ⚙️ Settings")
    
    # Generation settings
    st.markdown("### Generation Options")
    max_words = st.slider("Maximum words per skill", 10, 100, 50)
    strict_filter = st.checkbox("Strict inappropriate word filter", value=True)
    exclude_rare = st.checkbox("Exclude rare words", value=True)
    
    # Display settings
    st.markdown("### Display Options")
    show_stats = st.checkbox("Show statistics", value=True)
    show_examples = st.checkbox("Show example sentences", value=False)
    
    # Info
    st.markdown("---")
    st.markdown("### 📖 About")
    st.markdown("""
    This tool generates word lists for all 30 Orton-Gillingham skills with:
    - Age-appropriate word length limits
    - Inappropriate word filtering  
    - High-frequency word identification
    - Phonetic pattern matching
    """)

# Main content tabs
tab1, tab2, tab3, tab4, tab5, tab6, tab7 = st.tabs(["🎯 Generate Words", "📊 View & Edit", "📈 Analytics", "⚡ Batch Operations", "📋 Reports", "📝 Passages", "🔤 Word Search"])

# Tab 1: Generate Words
with tab1:
    st.markdown('<h2 class="sub-header">Generate Word Lists by Skill</h2>', unsafe_allow_html=True)
    
    # Skill selection
    categories = get_skill_categories()
    selected_category = st.selectbox("Select Category", list(categories.keys()))
    
    col1, col2 = st.columns([3, 1])
    
    with col1:
        skills_in_category = categories[selected_category]
        skill_options = {}
        
        for skill_id in skills_in_category:
            if skill_id in st.session_state.generator.skill_mappings:
                skill_name = st.session_state.generator.skill_mappings[skill_id]['name']
                skill_options[f"{skill_id}: {skill_name}"] = skill_id
        
        selected_skill_display = st.selectbox("Select Skill", list(skill_options.keys()))
        selected_skill = skill_options[selected_skill_display]
    
    with col2:
        st.markdown("<br>", unsafe_allow_html=True)
        generate_btn = st.button("🔄 Generate Words", type="primary")
    
    if generate_btn:
        words = generate_words_for_skill(selected_skill, max_words, strict_filter)
        
        if words:
            st.session_state.generated_words[selected_skill] = pd.DataFrame(words)
            st.success(f"✅ Generated {len(words)} words for {selected_skill_display}")
            
            # Display the generated words
            st.markdown("### Generated Words")
            df = pd.DataFrame(words)
            
            # Show word length constraints if applicable
            if selected_skill in st.session_state.generator.word_length_limits:
                limits = st.session_state.generator.word_length_limits[selected_skill]
                st.info(f"📏 Word length constraints: {limits['min']}-{limits['max']} letters")
            
            # Display words in a nice format
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.markdown("**Target Words**")
                target_words = df[df['word_type'] == 'target']['word'].tolist()
                st.write(", ".join(target_words[:20]))
                if len(target_words) > 20:
                    st.write(f"... and {len(target_words) - 20} more")
            
            with col2:
                st.markdown("**High-Frequency Words**")
                hf_words = df[df['is_HF'] == True]['word'].tolist()
                if hf_words:
                    st.write(", ".join(hf_words))
                else:
                    st.write("None identified")
            
            with col3:
                st.markdown("**Download**")
                csv = df.to_csv(index=False)
                st.download_button(
                    label="📥 Download CSV",
                    data=csv,
                    file_name=f"skill_{selected_skill}_words.csv",
                    mime="text/csv"
                )
        else:
            st.error("❌ No words generated for this skill")

# Tab 2: View & Edit
with tab2:
    st.markdown('<h2 class="sub-header">View and Edit Generated Words</h2>', unsafe_allow_html=True)
    
    if st.session_state.generated_words:
        skill_to_view = st.selectbox(
            "Select skill to view/edit",
            list(st.session_state.generated_words.keys())
        )
        
        if skill_to_view:
            df = st.session_state.generated_words[skill_to_view]
            
            # Filters
            col1, col2, col3 = st.columns(3)
            
            with col1:
                pattern_filter = st.multiselect(
                    "Filter by pattern",
                    df['primary_pattern'].unique()
                )
            
            with col2:
                position_filter = st.multiselect(
                    "Filter by position",
                    df['pattern_position'].unique()
                )
            
            with col3:
                hf_filter = st.checkbox("Show only high-frequency words")
            
            # Apply filters
            filtered_df = df.copy()
            if pattern_filter:
                filtered_df = filtered_df[filtered_df['primary_pattern'].isin(pattern_filter)]
            if position_filter:
                filtered_df = filtered_df[filtered_df['pattern_position'].isin(position_filter)]
            if hf_filter:
                filtered_df = filtered_df[filtered_df['is_HF'] == True]
            
            # Editable dataframe
            st.markdown("### Edit Words")
            edited_df = st.data_editor(
                filtered_df,
                num_rows="dynamic",
                column_config={
                    "word": st.column_config.TextColumn("Word", width="medium"),
                    "primary_pattern": st.column_config.TextColumn("Pattern", width="small"),
                    "pattern_position": st.column_config.SelectboxColumn(
                        "Position",
                        options=["initial", "medial", "final", "multiple"],
                        width="small"
                    ),
                    "is_HF": st.column_config.CheckboxColumn("High Freq", width="small"),
                    "is_heart": st.column_config.CheckboxColumn("Heart Word", width="small"),
                    "notes": st.column_config.TextColumn("Notes", width="large")
                }
            )
            
            # Save changes
            if st.button("💾 Save Changes"):
                st.session_state.generated_words[skill_to_view] = edited_df
                st.success("Changes saved!")
            
            # Add custom words
            st.markdown("### Add Custom Words")
            with st.form("add_custom_word"):
                col1, col2, col3, col4 = st.columns(4)
                
                with col1:
                    new_word = st.text_input("Word")
                with col2:
                    new_pattern = st.text_input("Primary Pattern")
                with col3:
                    new_position = st.selectbox("Position", ["initial", "medial", "final"])
                with col4:
                    new_is_hf = st.checkbox("High Frequency")
                
                if st.form_submit_button("➕ Add Word"):
                    new_row = {
                        'skill_id': skill_to_view,
                        'word': new_word,
                        'primary_pattern': new_pattern,
                        'pattern_position': new_position,
                        'is_HF': new_is_hf,
                        'is_heart': False,
                        'irregular_part': '',
                        'irregular_sound': '',
                        'syllable_breaks': new_word,
                        'word_type': 'custom',
                        'notes': 'User added'
                    }
                    
                    updated_df = pd.concat([edited_df, pd.DataFrame([new_row])], ignore_index=True)
                    st.session_state.generated_words[skill_to_view] = updated_df
                    st.success(f"Added '{new_word}' to word list!")
                    st.rerun()
    else:
        st.info("📝 No words generated yet. Go to the Generate Words tab to start.")

# Tab 3: Analytics
with tab3:
    st.markdown('<h2 class="sub-header">Word List Analytics</h2>', unsafe_allow_html=True)
    
    if st.session_state.generated_words:
        # Overall statistics
        col1, col2, col3, col4 = st.columns(4)
        
        total_words = sum(len(df) for df in st.session_state.generated_words.values())
        total_skills = len(st.session_state.generated_words)
        total_hf = sum(df['is_HF'].sum() for df in st.session_state.generated_words.values())
        avg_words = total_words / total_skills if total_skills > 0 else 0
        
        with col1:
            st.metric("Total Words", total_words)
        with col2:
            st.metric("Skills Generated", f"{total_skills}/30")
        with col3:
            st.metric("High-Frequency Words", total_hf)
        with col4:
            st.metric("Avg Words/Skill", f"{avg_words:.1f}")
        
        # Word length distribution
        st.markdown("### Word Length Distribution")
        
        all_words = pd.concat(st.session_state.generated_words.values(), ignore_index=True)
        all_words['word_length'] = all_words['word'].str.len()
        
        fig_length = px.histogram(
            all_words, 
            x='word_length',
            title="Distribution of Word Lengths",
            labels={'word_length': 'Word Length', 'count': 'Number of Words'},
            color_discrete_sequence=['#1e3d59']
        )
        st.plotly_chart(fig_length, use_container_width=True)
        
        # Words per skill
        st.markdown("### Words Generated per Skill")
        
        skill_counts = pd.DataFrame([
            {
                'Skill': f"{skill_id}: {st.session_state.generator.skill_mappings[skill_id]['name']}",
                'Word Count': len(df),
                'HF Words': df['is_HF'].sum()
            }
            for skill_id, df in st.session_state.generated_words.items()
        ])
        
        fig_skills = px.bar(
            skill_counts,
            x='Skill',
            y=['Word Count', 'HF Words'],
            title="Word Count by Skill",
            barmode='group',
            color_discrete_map={'Word Count': '#1e3d59', 'HF Words': '#f5b700'}
        )
        fig_skills.update_layout(xaxis_tickangle=-45)
        st.plotly_chart(fig_skills, use_container_width=True)
        
        # Pattern frequency
        st.markdown("### Most Common Patterns")
        
        pattern_counts = all_words['primary_pattern'].value_counts().head(20)
        fig_patterns = px.bar(
            x=pattern_counts.values,
            y=pattern_counts.index,
            orientation='h',
            title="Top 20 Most Common Patterns",
            labels={'x': 'Frequency', 'y': 'Pattern'},
            color_discrete_sequence=['#3e5c76']
        )
        st.plotly_chart(fig_patterns, use_container_width=True)
        
    else:
        st.info("📊 No data to analyze yet. Generate some word lists first!")

# Tab 4: Batch Operations
with tab4:
    st.markdown('<h2 class="sub-header">Batch Word Generation</h2>', unsafe_allow_html=True)
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        # Category selection for batch generation
        st.markdown("### Select Skills to Generate")
        
        categories = get_skill_categories()
        selected_categories = st.multiselect(
            "Select categories",
            list(categories.keys()),
            default=["Foundation (1-5)"]
        )
        
        # Get all skills in selected categories
        skills_to_generate = []
        for category in selected_categories:
            skills_to_generate.extend(categories[category])
        
        st.info(f"📋 Will generate words for {len(skills_to_generate)} skills")
        
    with col2:
        st.markdown("### Generation Settings")
        batch_max_words = st.number_input("Words per skill", 10, 100, 40)
        batch_strict = st.checkbox("Strict filtering", value=True)
        
        if st.button("🚀 Generate All Selected", type="primary"):
            progress_bar = st.progress(0)
            status_text = st.empty()
            
            results = {}
            for i, skill_id in enumerate(skills_to_generate):
                if skill_id in st.session_state.generator.skill_mappings:
                    skill_name = st.session_state.generator.skill_mappings[skill_id]['name']
                    status_text.text(f"Generating {skill_id}: {skill_name}...")
                    
                    words = generate_words_for_skill(skill_id, batch_max_words, batch_strict)
                    if words:
                        df = pd.DataFrame(words)
                        st.session_state.generated_words[skill_id] = df
                        results[f"skill_{skill_id}_{skill_name.replace(' ', '_')}.csv"] = df
                    
                    progress_bar.progress((i + 1) / len(skills_to_generate))
            
            status_text.text("✅ Batch generation complete!")
            
            # Create zip download
            if results:
                zip_data = create_zip_download(results)
                st.download_button(
                    label="📦 Download All as ZIP",
                    data=zip_data,
                    file_name=f"og_word_lists_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip",
                    mime="application/zip"
                )
    
    # Quick actions
    st.markdown("### Quick Actions")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("🔄 Generate All Foundation Skills"):
            foundation_skills = categories["Foundation (1-5)"]
            for skill_id in foundation_skills:
                if skill_id in st.session_state.generator.skill_mappings:
                    words = generate_words_for_skill(skill_id, max_words, strict_filter)
                    if words:
                        st.session_state.generated_words[skill_id] = pd.DataFrame(words)
            st.success("✅ Generated all foundation skills!")
    
    with col2:
        if st.button("🗑️ Clear All Generated Words"):
            st.session_state.generated_words = {}
            st.success("✅ Cleared all generated words")
            st.rerun()
    
    with col3:
        if st.button("📊 Generate Sample Report"):
            # This would generate a sample decodable passage
            st.info("Feature coming soon: Generate sample decodable passages!")

# Tab 5: Reports
with tab5:
    st.markdown('<h2 class="sub-header">Reports and Documentation</h2>', unsafe_allow_html=True)
    
    # Word length limits report
    st.markdown("### Word Length Limits by Skill")
    
    length_report = []
    for skill_id in sorted(st.session_state.generator.skill_mappings.keys()):
        skill_name = st.session_state.generator.skill_mappings[skill_id]['name']
        limits = st.session_state.generator.word_length_limits.get(
            skill_id, 
            {'min': 'N/A', 'max': 'N/A'}
        )
        
        length_report.append({
            'Skill ID': skill_id,
            'Skill Name': skill_name,
            'Min Length': limits.get('min', 'N/A'),
            'Max Length': limits.get('max', 'N/A')
        })
    
    length_df = pd.DataFrame(length_report)
    
    # Display as a nice table with filtering
    search_term = st.text_input("🔍 Search skills")
    if search_term:
        filtered_length_df = length_df[
            length_df['Skill Name'].str.contains(search_term, case=False) |
            length_df['Skill ID'].str.contains(search_term, case=False)
        ]
    else:
        filtered_length_df = length_df
    
    st.dataframe(filtered_length_df, use_container_width=True, height=400)
    
    # Download reports
    st.markdown("### Download Reports")
    
    col1, col2 = st.columns(2)
    
    with col1:
        csv = length_df.to_csv(index=False)
        st.download_button(
            label="📥 Download Length Limits Report",
            data=csv,
            file_name="word_length_limits_report.csv",
            mime="text/csv"
        )
    
    with col2:
        # Create inappropriate words report
        report_text = f"""INAPPROPRIATE WORDS FILTER REPORT
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

INAPPROPRIATE WORDS LIST:
{chr(10).join(f'  - {word}' for word in sorted(list(st.session_state.generator.inappropriate_words)[:50]))}

MILD CONCERNS LIST:
{chr(10).join(f'  - {word}' for word in sorted(st.session_state.generator.mild_concerns))}

SUBSTRING FILTERS:
  - sex (except: sixteen, sixty)
  - ass (except: class, pass, etc.)
  - tit (except: title, competition, etc.)
"""
        
        st.download_button(
            label="📥 Download Filter Report",
            data=report_text,
            file_name="inappropriate_words_filter_report.txt",
            mime="text/plain"
        )
    
    # Summary statistics
    if st.session_state.generated_words:
        st.markdown("### Generation Summary")
        
        summary_data = []
        for skill_id, df in st.session_state.generated_words.items():
            summary_data.append({
                'Skill ID': skill_id,
                'Skill Name': st.session_state.generator.skill_mappings[skill_id]['name'],
                'Total Words': len(df),
                'HF Words': df['is_HF'].sum(),
                'Heart Words': df['is_heart'].sum() if 'is_heart' in df.columns else 0,
                'Unique Patterns': df['primary_pattern'].nunique()
            })
        
        summary_df = pd.DataFrame(summary_data)
        st.dataframe(summary_df, use_container_width=True)
        
        # Download summary
        csv = summary_df.to_csv(index=False)
        st.download_button(
            label="📥 Download Generation Summary",
            data=csv,
            file_name="generation_summary.csv",
            mime="text/csv"
        )

# Tab 6: Passage Generation
with tab6:
    st.markdown('<h2 class="sub-header">Generate Decodable Passages</h2>', 
                unsafe_allow_html=True)
    
    if st.session_state.generated_words:
        col1, col2 = st.columns([2, 1])
        
        with col1:
            # Skill selection for passage
            passage_skill = st.selectbox(
                "Select skill for passage",
                list(st.session_state.generated_words.keys()),
                format_func=lambda x: f"{x}: {st.session_state.generator.skill_mappings[x]['name']}"
            )
            
            # Passage type
            passage_type = st.radio(
                "Passage type",
                ["Mini-passage (single skill)", "Full reader (combined skills)"]
            )
            
            if passage_type.startswith("Full"):
                # Multi-skill selection
                available_skills = list(st.session_state.generated_words.keys())
                selected_skills = st.multiselect(
                    "Select skills to combine",
                    available_skills,
                    default=[passage_skill]
                )
            else:
                selected_skills = [passage_skill]
        
        with col2:
            st.markdown("### Settings")
            word_count = st.slider("Target word count", 20, 500, 40)
            target_density = st.slider("Target pattern density", 0.3, 0.8, 0.65)
            decodability = st.slider("Minimum decodability", 0.7, 1.0, 0.90)
            
            generate_passage_btn = st.button("📝 Generate Passage", type="primary")
        
        if generate_passage_btn:
            # Initialize generator if needed
            if 'passage_generator' not in st.session_state or st.session_state.passage_generator is None:
                st.session_state.passage_generator = DecodablePassageGenerator(
                    st.session_state.generated_words,
                    st.session_state.generator.skill_mappings
                )
            
            # Generate passage
            with st.spinner("Generating passage..."):
                if passage_type.startswith("Mini"):
                    result = st.session_state.passage_generator.generate_passage(
                        selected_skills[0],
                        passage_type='mini',
                        word_count=word_count,
                        target_density=target_density,
                        strict_decodability=decodability
                    )
                else:
                    result = st.session_state.passage_generator.generate_full_reader(
                        selected_skills,
                        title="My Decodable Reader",
                        word_count=word_count
                    )
            
            # Display results
            if 'error' not in result:
                st.success("✅ Passage generated successfully!")
                
                # Display passage
                st.markdown("### Generated Passage")
                
                if result.get('type') == 'chapter_book':
                    for chapter in result['chapters']:
                        st.markdown(f"**{chapter['title']}**")
                        st.write(chapter['content'])
                        with st.expander("Chapter Statistics"):
                            st.json(chapter['statistics'])
                else:
                    st.write(result.get('content', result.get('passage')))
                    
                    # Display statistics
                    with st.expander("Passage Statistics"):
                        st.json(result.get('statistics'))
                
                # Download options
                col1, col2 = st.columns(2)
                with col1:
                    st.download_button(
                        "📥 Download as Text",
                        result.get('content', result.get('passage')),
                        f"decodable_passage_{passage_skill}.txt",
                        mime="text/plain"
                    )
            else:
                st.error(result['error'])
    else:
        st.info("📝 Generate word lists first before creating passages.")

# Tab 7: Word Search
with tab7:
    st.markdown('<h2 class="sub-header">Create Word Search Puzzles</h2>', 
                unsafe_allow_html=True)
    
    if st.session_state.generated_words:
        col1, col2 = st.columns([2, 1])
        
        with col1:
            # Skill selection for puzzle
            puzzle_skill = st.selectbox(
                "Select skill for word search",
                list(st.session_state.generated_words.keys()),
                format_func=lambda x: f"{x}: {st.session_state.generator.skill_mappings[x]['name']}"
            )
            
            # Preview selected words
            df = st.session_state.generated_words[puzzle_skill]
            available_words = df['word'].tolist()
            
            st.markdown("### Available Words")
            st.write(f"Total words: {len(available_words)}")
            
            # Word selection
            max_words = st.slider("Maximum words in puzzle", 5, 20, 12)
            selected_words = st.multiselect(
                "Select specific words (or leave empty for random selection)",
                available_words,
                default=[]
            )
        
        with col2:
            st.markdown("### Puzzle Settings")
            puzzle_size = st.slider("Grid size", 10, 20, 15)
            difficulty = st.select_slider(
                "Difficulty",
                ["easy", "medium", "hard"],
                value="medium"
            )
            uppercase = st.checkbox("Convert to uppercase", value=True)
            
            generate_puzzle_btn = st.button("🔤 Generate Puzzle", type="primary")
        
        if generate_puzzle_btn:
            import random
            
            # Select words if not specified
            if not selected_words:
                selected_words = random.sample(
                    available_words,
                    min(max_words, len(available_words))
                )
            
            # Generate puzzle
            puzzle = st.session_state.puzzle_generator.generate_puzzle(
                selected_words[:max_words],
                size=puzzle_size,
                difficulty=difficulty,
                title=f"{st.session_state.generator.skill_mappings[puzzle_skill]['name']} Word Search",
                uppercase=uppercase
            )
            
            st.success(f"✅ Puzzle created with {puzzle['total_words']} words!")
            
            # Display puzzle
            col1, col2 = st.columns([3, 2])
            
            with col1:
                st.markdown("### Puzzle Grid")
                st.code(puzzle['display_grid'], language=None)
            
            with col2:
                st.markdown("### Find These Words")
                for word in sorted(puzzle['words']):
                    st.write(f"- {word}")
            
            # Solution
            with st.expander("View Solution"):
                st.code(puzzle['solution_grid'], language=None)
            
            # Download options
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.download_button(
                    "📥 Download as Text",
                    f"{puzzle['title']}\n\n{puzzle['display_grid']}\n\nWords:\n" + 
                    '\n'.join(puzzle['words']),
                    f"word_search_{puzzle_skill}.txt",
                    mime="text/plain"
                )
            
            with col2:
                st.download_button(
                    "📥 Download as HTML",
                    puzzle['html'],
                    f"word_search_{puzzle_skill}.html",
                    mime="text/html"
                )
    else:
        st.info("🔤 Generate word lists first before creating puzzles.")

# Footer
st.markdown("---")
st.markdown("""
<div style='text-align: center; color: #666;'>
    <p>Orton-Gillingham Word List Generator v1.0 | 
    Made with ❤️ for educators | 
    <a href='#'>Documentation</a> | 
    <a href='#'>Report Issues</a></p>
</div>
""", unsafe_allow_html=True)