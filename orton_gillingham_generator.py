#!/usr/bin/env python3
"""
Adapter for Enhanced CSV-Based Orton-Gillingham Word List Generator
This maintains compatibility with the existing Streamlit interface
while using the enhanced CSV with all the new features.
"""

import pandas as pd
import os
from datetime import datetime
from collections import defaultdict

class OrtonGillinghamCompleteGenerator:
    """Adapter class that works with the Streamlit interface using enhanced CSV"""
    
    def __init__(self):
        print("Initializing Orton-Gillingham Word Generator (Enhanced CSV)...")
        
        # Try multiple CSV paths in order of preference
        csv_paths = [
            'enhanced_phonics_master_rules_complete.csv',  # New enhanced version
            'updated_phonics_master_rules_complete.csv',    # Original version
            'phonics_master_rules.csv'                      # Fallback
        ]
        
        self.csv_path = None
        for path in csv_paths:
            if os.path.exists(path):
                self.csv_path = path
                print(f"Using CSV: {path}")
                break
        
        if not self.csv_path:
            raise FileNotFoundError(f"No CSV file found. Tried: {csv_paths}")
        
        # Load the CSV
        self.rules_df = pd.read_csv(self.csv_path)
        
        # Import NLTK resources
        import nltk
        try:
            self.cmu_dict = nltk.corpus.cmudict.dict()
        except:
            print("Downloading CMU dictionary...")
            nltk.download('cmudict')
            self.cmu_dict = nltk.corpus.cmudict.dict()
        
        try:
            brown_words = nltk.corpus.brown.words()
            self.freq_dist = nltk.FreqDist(word.lower() for word in brown_words)
        except:
            print("Downloading Brown corpus...")
            nltk.download('brown')
            brown_words = nltk.corpus.brown.words()
            self.freq_dist = nltk.FreqDist(word.lower() for word in brown_words)
        
        # Build skill mappings for compatibility
        self.skill_mappings = self._create_skill_mappings()
        self.rules_by_skill = self._build_rules_by_skill()
        
        print("Initialization complete!")
    
    def _create_skill_mappings(self):
        """Create mappings from various skill formats"""
        mappings = {}
        
        for _, row in self.rules_df.iterrows():
            skill_id = str(row['skill_id'])
            skill_name = row['skill_name']
            
            # Map various formats
            mappings[skill_id] = skill_id
            mappings[skill_id.replace('.', '_')] = skill_id
            mappings[f"skill_{skill_id}"] = skill_id
            mappings[skill_name.lower()] = skill_id
            mappings[skill_name.lower().replace(' ', '_')] = skill_id
        
        return mappings
    
    def _build_rules_by_skill(self):
        """Build rules dictionary from CSV"""
        rules = {}
        
        for _, row in self.rules_df.iterrows():
            skill_id = str(row['skill_id'])
            
            rules[skill_id] = {
                'name': row['skill_name'],
                'pattern_type': row['pattern_type'],
                'graphemes': str(row['graphemes']).split(', ') if pd.notna(row['graphemes']) else [],
                'phonemes': str(row['required_phonemes']).split(' or ') if pd.notna(row['required_phonemes']) else [],
                'position_constraints': row['position_constraints'],
                'position_by_grapheme': row.get('position_by_grapheme', ''),
                'word_length': row['word_length'],
                'special_rules': row['special_rules'],
                'examples': str(row['examples']).split(', ') if pd.notna(row['examples']) else [],
                'prerequisites': str(row.get('prerequisite_skills', '')).split(',') if pd.notna(row.get('prerequisite_skills', '')) else [],
                'heart_word_triggers': str(row.get('heart_word_triggers', '')).split(', ') if pd.notna(row.get('heart_word_triggers', '')) else []
            }
        
        return rules
    
    def generate_words_for_skill(self, skill_id, max_words=50):
        """Generate words for a specific skill (compatible with Streamlit interface)"""
        # Normalize skill_id
        skill_id = self.skill_mappings.get(skill_id, skill_id)
        
        if skill_id not in self.rules_by_skill:
            print(f"Warning: Skill {skill_id} not found")
            return []
        
        rule = self.rules_by_skill[skill_id]
        skill_name = rule['name']
        
        print(f"\nGenerating words for {skill_id}: {skill_name}")
        
        # Get patterns and constraints
        graphemes = rule['graphemes']
        position = rule['position_constraints']
        position_by_grapheme = rule['position_by_grapheme']
        
        # Parse word length
        word_length_str = str(rule['word_length'])
        min_length, max_length = 2, 15
        if pd.notna(word_length_str) and word_length_str:
            if '-' in word_length_str:
                parts = word_length_str.split('-')
                try:
                    min_length = int(parts[0])
                    max_length = int(parts[1])
                except:
                    pass
        
        # Check for specific word lists
        if 'Limited to specific words:' in str(rule['special_rules']):
            # Use examples as the word list
            words = []
            for word in rule['examples']:
                words.append({
                    'word': word.lower(),
                    'pattern': 'specific',
                    'position': 'N/A',
                    'frequency_score': self.freq_dist.get(word.lower(), 0),
                    'is_high_frequency': word.lower() in self.freq_dist.most_common(1000),
                    'is_heart_word': False,
                    'has_homophone': False,
                    'syllable_breaks': word,
                    'notes': 'Specific word list'
                })
            return words[:max_words]
        
        # Generate words based on patterns
        candidates = []
        
        for word, pronunciations in self.cmu_dict.items():
            # Skip if wrong length
            if not (min_length <= len(word) <= max_length):
                continue
            
            # Check if word contains any target pattern
            word_lower = word.lower()
            matched = False
            matched_pattern = None
            
            for pattern in graphemes:
                if self._check_pattern_position(word_lower, pattern, position, position_by_grapheme):
                    matched = True
                    matched_pattern = pattern
                    break
            
            if matched:
                # Check additional constraints
                if rule['pattern_type'] == 'CVC' and len(word) == 3:
                    # Verify CVC structure
                    if not (word[0] not in 'aeiou' and word[1] in 'aeiou' and word[2] not in 'aeiou'):
                        continue
                
                freq_score = self.freq_dist.get(word_lower, 0)
                
                candidates.append({
                    'word': word_lower,
                    'pattern': matched_pattern,
                    'position': self._get_pattern_position_in_word(word_lower, matched_pattern),
                    'frequency_score': freq_score,
                    'is_high_frequency': freq_score > 10,
                    'is_heart_word': word_lower in rule['heart_word_triggers'],
                    'has_homophone': False,  # Would need homophone checking
                    'syllable_breaks': word_lower,  # Would need syllabification
                    'notes': ''
                })
        
        # Sort by frequency
        candidates.sort(key=lambda x: x['frequency_score'], reverse=True)
        
        # Return top words
        return candidates[:max_words]
    
    def _check_pattern_position(self, word, pattern, position_constraint, position_by_grapheme):
        """Check if pattern appears in correct position"""
        # Check if pattern exists in word first
        if pattern not in word:
            return False
        
        # Handle position_by_grapheme if available
        if position_by_grapheme and pd.notna(position_by_grapheme) and position_by_grapheme.strip():
            # Parse position_by_grapheme: "ai:medial, ay:final"
            positions = {}
            for mapping in position_by_grapheme.split(', '):
                if ':' in mapping:
                    grapheme, pos = mapping.split(':')
                    positions[grapheme.strip()] = pos.strip()
            
            if pattern in positions:
                position_constraint = positions[pattern]
        
        # Check position
        if 'initial' in str(position_constraint):
            return word.startswith(pattern)
        elif 'final' in str(position_constraint):
            return word.endswith(pattern)
        elif position_constraint == 'medial':
            return pattern in word and not word.startswith(pattern) and not word.endswith(pattern)
        else:
            return True  # 'any' or unspecified
    
    def _get_pattern_position_in_word(self, word, pattern):
        """Get position of pattern in word"""
        if word.startswith(pattern):
            return 'initial'
        elif word.endswith(pattern):
            return 'final'
        else:
            return 'medial'
    
    def generate_all_skills(self, progress_callback=None):
        """Generate words for all skills"""
        all_results = {}
        
        total_skills = len(self.rules_by_skill)
        
        for i, (skill_id, rule) in enumerate(self.rules_by_skill.items()):
            if progress_callback:
                progress_callback(i / total_skills, f"Generating {skill_id}: {rule['name']}")
            
            words = self.generate_words_for_skill(skill_id)
            all_results[skill_id] = {
                'skill_name': rule['name'],
                'words': words,
                'word_count': len(words)
            }
        
        return all_results
    
    def export_to_csv(self, results, output_dir='streamlit_output'):
        """Export results to CSV files"""
        os.makedirs(output_dir, exist_ok=True)
        
        for skill_id, data in results.items():
            if data['words']:
                df = pd.DataFrame(data['words'])
                
                # Clean filename
                skill_name = data['skill_name'].replace('/', '_').replace(' ', '_')
                filename = f"{output_dir}/skill_{skill_id}_{skill_name}.csv"
                
                df.to_csv(filename, index=False)
                print(f"Exported {filename}")
    
    def export_summary(self, results, output_dir='streamlit_output'):
        """Export summary of all skills"""
        summary_data = []
        
        for skill_id, data in results.items():
            summary_data.append({
                'skill_id': skill_id,
                'skill_name': data['skill_name'],
                'word_count': data['word_count'],
                'prerequisites': ', '.join(self.rules_by_skill[skill_id].get('prerequisites', [])),
                'position_constraints': self.rules_by_skill[skill_id].get('position_constraints', ''),
                'heart_words': ', '.join(self.rules_by_skill[skill_id].get('heart_word_triggers', []))
            })
        
        summary_df = pd.DataFrame(summary_data)
        summary_df.to_csv(f"{output_dir}/skills_summary.csv", index=False)
        
        return summary_df

# For backward compatibility
if __name__ == "__main__":
    generator = OrtonGillinghamCompleteGenerator()
    print("Generator ready for use!")
