#!/usr/bin/env python3
"""
Adapter for CSV-Based Orton-Gillingham Word List Generator
This maintains compatibility with the existing Streamlit interface
while using the new CSV-based generator internally.
"""

import pandas as pd
import os
from datetime import datetime
from csv_based_phoneme_generator import CSVBasedPhonemeGenerator

class OrtonGillinghamCompleteGenerator:
    """Adapter class that maintains the original interface while using CSV-based logic"""
    
    def __init__(self):
        print("Initializing Orton-Gillingham Word Generator (CSV-Based)...")
        
        # Initialize the CSV-based generator
        csv_path = 'updated_phonics_master_rules_complete.csv'
        if not os.path.exists(csv_path):
            raise FileNotFoundError(f"CSV file not found: {csv_path}")
            
        self.generator = CSVBasedPhonemeGenerator(csv_path)
        
        # Map old skill IDs to new format if needed
        self.skill_mappings = self._create_skill_mappings()
        
        print("Initialization complete!")
    
    def _create_skill_mappings(self):
        """Create mappings from old skill format to new CSV format"""
        mappings = {}
        
        # Get all skills from the CSV
        for skill_id, rule in self.generator.rules_by_skill.items():
            # Create various possible formats
            skill_str = str(skill_id)
            
            # Handle different formats (1.1, 1_1, skill_1.1, etc.)
            mappings[skill_str] = skill_str
            mappings[skill_str.replace('.', '_')] = skill_str
            mappings[f"skill_{skill_str}"] = skill_str
            mappings[f"skill_{skill_str.replace('.', '_')}"] = skill_str
            
            # Map skill names too
            name = rule['name'].lower()
            mappings[name] = skill_str
            mappings[name.replace(' ', '_')] = skill_str
        
        return mappings
    
    def generate_skill_words(self, skill_identifier, count=50):
        """Generate words for a specific skill (maintains original interface)"""
        
        # Convert skill identifier to the format used in CSV
        skill_id = self._normalize_skill_id(skill_identifier)
        
        if not skill_id:
            print(f"Warning: Unknown skill identifier: {skill_identifier}")
            return []
        
        # Generate words using the CSV-based generator
        word_data = self.generator.generate_words_for_skill(skill_id, max_words=count)
        
        # Convert to the format expected by the Streamlit app
        results = []
        for item in word_data:
            results.append({
                'word': item['word'],
                'is_HF': self.generator.freq_dist.get(item['word'], 0) > 100,
                'is_heart': False,  # Would need to check heart words
                'skill_id': skill_id,
                'skill_name': self.generator.rules_by_skill[skill_id]['name']
            })
        
        return results
    
    def _normalize_skill_id(self, identifier):
        """Convert various skill identifiers to standard format"""
        identifier_str = str(identifier).strip().lower()
        
        # Direct lookup
        if identifier_str in self.skill_mappings:
            return self.skill_mappings[identifier_str]
        
        # Try without 'skill_' prefix
        if identifier_str.startswith('skill_'):
            without_prefix = identifier_str[6:]
            if without_prefix in self.skill_mappings:
                return self.skill_mappings[without_prefix]
        
        # Try direct skill ID
        if identifier_str in self.generator.rules_by_skill:
            return identifier_str
        
        # Try with dots instead of underscores
        with_dots = identifier_str.replace('_', '.')
        if with_dots in self.generator.rules_by_skill:
            return with_dots
        
        return None
    
    def get_all_skills(self):
        """Get list of all available skills"""
        skills = []
        
        for skill_id, rule in self.generator.rules_by_skill.items():
            skills.append({
                'id': skill_id,
                'name': rule['name'],
                'category': self._categorize_skill(skill_id),
                'description': rule.get('phoneme_desc', '')
            })
        
        return skills
    
    def _categorize_skill(self, skill_id):
        """Categorize skills for the UI"""
        skill_num = float(skill_id.split('.')[0])
        
        if skill_num <= 2:
            return "Basic Phonemes"
        elif skill_num <= 6:
            return "Blends & Digraphs"
        elif skill_num <= 11:
            return "Special Rules"
        elif skill_num <= 18:
            return "Advanced Patterns"
        elif skill_num <= 20:
            return "R-Controlled & Vowel Teams"
        elif skill_num <= 23:
            return "Syllabication & Morphology"
        else:
            return "Spelling Rules"
    
    def export_to_csv(self, words, filename):
        """Export words to CSV format expected by the app"""
        if not words:
            return
        
        # Create DataFrame
        df = pd.DataFrame(words)
        
        # Add any missing columns
        if 'syllable_breaks' not in df.columns:
            df['syllable_breaks'] = df['word']
        if 'notes' not in df.columns:
            df['notes'] = ''
        
        # Save to CSV
        df.to_csv(filename, index=False)
        print(f"Exported {len(words)} words to {filename}")
    
    def generate_multiple_skills(self, skill_list, words_per_skill=50):
        """Generate words for multiple skills"""
        all_words = []
        
        for skill in skill_list:
            words = self.generate_skill_words(skill, words_per_skill)
            all_words.extend(words)
        
        return all_words
    
    def get_skill_info(self, skill_identifier):
        """Get detailed information about a skill"""
        skill_id = self._normalize_skill_id(skill_identifier)
        
        if skill_id and skill_id in self.generator.rules_by_skill:
            rule = self.generator.rules_by_skill[skill_id]
            return {
                'id': skill_id,
                'name': rule['name'],
                'pattern_type': rule['pattern_type'],
                'graphemes': rule['graphemes'],
                'phonemes': rule['phonemes'],
                'examples': rule.get('examples', ''),
                'special_rules': rule.get('special_rules', '')
            }
        
        return None

# For backward compatibility
if __name__ == "__main__":
    # Test the adapter
    generator = OrtonGillinghamCompleteGenerator()
    
    # Test generating words for a skill
    words = generator.generate_skill_words('1.1', count=5)
    print(f"\nGenerated {len(words)} words for skill 1.1:")
    for w in words:
        print(f"  - {w['word']}")
    
    # Test getting all skills
    skills = generator.get_all_skills()
    print(f"\nTotal skills available: {len(skills)}")