#!/usr/bin/env python3
"""
Position-Aware Word List Demo
Shows how the enhanced CSV handles position constraints
"""

import pandas as pd
from quick_word_list_builder import <PERSON><PERSON>ordListBuilder

def demonstrate_position_awareness():
    """Show how position constraints work in practice"""
    
    print("🎯 POSITION-AWARE WORD GENERATION DEMO")
    print("=" * 60)
    
    # Load the enhanced CSV to show position details
    df = pd.read_csv('enhanced_phonics_master_rules_complete.csv')
    builder = QuickWordListBuilder()
    
    # Examples of position-sensitive skills
    position_demos = [
        {
            'skill_id': '4.4',
            'name': 'wh digraph',
            'note': 'Only appears at the BEGINNING of words'
        },
        {
            'skill_id': '4.5', 
            'name': 'ck digraph',
            'note': 'Only appears at the END of words after short vowels'
        },
        {
            'skill_id': '18.1',
            'name': 'Long a teams',
            'note': 'Different patterns have different positions: ai (middle), ay (end)'
        },
        {
            'skill_id': '18.11',
            'name': 'oy diphthong teams',
            'note': 'oi appears in middle, oy appears at end'
        }
    ]
    
    for demo in position_demos:
        skill_id = demo['skill_id']
        skill_row = df[df['skill_id'] == skill_id].iloc[0]
        
        print(f"\n{'='*60}")
        print(f"Skill {skill_id}: {demo['name']}")
        print(f"Position Rule: {demo['note']}")
        print(f"CSV Position: {skill_row['position_constraints']}")
        
        if pd.notna(skill_row['position_by_grapheme']):
            print(f"Detailed Positions: {skill_row['position_by_grapheme']}")
        
        print(f"\nGenerating position-aware words:")
        words = builder.generate_for_skill(skill_id, num_words=10)
        
        # For vowel teams, show position grouping
        if skill_id in ['18.1', '18.11']:
            print("\nGrouped by pattern position:")
            if skill_id == '18.1':
                ai_words = [w for w in words if 'ai' in w and not w.endswith('ai')]
                ay_words = [w for w in words if w.endswith('ay')]
                print(f"  'ai' words (middle): {', '.join(ai_words[:5])}")
                print(f"  'ay' words (end): {', '.join(ay_words[:5])}")
            elif skill_id == '18.11':
                oi_words = [w for w in words if 'oi' in w and not w.endswith('oi')]
                oy_words = [w for w in words if w.endswith('oy')]
                print(f"  'oi' words (middle): {', '.join(oi_words[:5])}")
                print(f"  'oy' words (end): {', '.join(oy_words[:5])}")
    
    # Show initial_or_final example if any skills use it
    print(f"\n{'='*60}")
    print("SPECIAL POSITION CONSTRAINTS")
    print("-" * 60)
    
    print("\nPosition Types in Your CSV:")
    position_types = df['position_constraints'].value_counts()
    for position, count in position_types.items():
        if pd.notna(position):
            print(f"  {position}: {count} skills")
    
    # Show how position_by_grapheme works
    print("\nSkills with Pattern-Specific Positions:")
    skills_with_detailed_positions = df[df['position_by_grapheme'].notna()]
    for _, row in skills_with_detailed_positions.head(5).iterrows():
        print(f"\n  Skill {row['skill_id']}: {row['skill_name']}")
        print(f"  Patterns: {row['position_by_grapheme']}")
    
    print("\n" + "="*60)
    print("✅ Position awareness ensures:")
    print("   - 'wh' only appears at word beginnings (when, what)")
    print("   - 'ck' only appears at word endings (back, duck)")
    print("   - 'ay' is used at word end (play, day)")
    print("   - 'ai' is used in word middle (rain, train)")
    print("\nThis matches how English spelling actually works!")

if __name__ == "__main__":
    demonstrate_position_awareness()
