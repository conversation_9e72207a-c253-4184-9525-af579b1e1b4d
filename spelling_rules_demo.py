#!/usr/bin/env python3
"""
Demonstration of how spelling transformation rules work
Shows how these differ from phoneme-based word generation
"""

import pandas as pd
import re
from typing import List, Tuple, Dict

class SpellingRuleProcessor:
    """Process spelling transformation rules (skills 24-30)"""
    
    def __init__(self):
        # Load base words from existing word lists
        self.base_words = self.load_base_words()
        
    def load_base_words(self) -> Dict[str, List[str]]:
        """Load appropriate base words for each rule"""
        return {
            # CVC words for 1-1-1 doubling
            'cvc_words': ['hop', 'sit', 'run', 'swim', 'cut', 'get', 'hit', 'put', 'win', 'bet'],
            
            # VCe words for e-dropping
            'vce_words': ['make', 'hope', 'care', 'take', 'bake', 'like', 'use', 'come', 'save', 'ride'],
            
            # Words ending in y for y-to-i rule
            'y_ending_words': ['happy', 'cry', 'fly', 'baby', 'funny', 'carry', 'party', 'city', 'busy', 'easy'],
            
            # Words for plural formation
            'regular_plural': ['cat', 'dog', 'book', 'car', 'toy', 'desk', 'pen', 'hat'],
            'sibilant_ending': ['box', 'wish', 'church', 'bush', 'glass', 'buzz', 'kiss', 'watch'],
            'f_ending': ['knife', 'wolf', 'leaf', 'life', 'shelf', 'calf', 'half', 'wife'],
            
            # Two-syllable words with stress on second syllable
            'two_syllable_stressed': ['begin', 'forget', 'occur', 'refer', 'admit', 'commit', 'permit', 'submit'],
            
            # Words for contractions
            'contraction_pairs': [
                ('can', 'not'), ('will', 'not'), ('it', 'is'), 
                ('I', 'will'), ('they', 'are'), ('we', 'are'),
                ('you', 'are'), ('do', 'not'), ('is', 'not')
            ]
        }
    
    def apply_1_1_1_doubling(self, word: str, suffix: str) -> str:
        """
        Skill 24: 1-1-1 doubling rule
        One syllable, one vowel, one final consonant → double before vowel suffix
        """
        # Check if word matches 1-1-1 pattern
        if (len(word) == 3 and  # one syllable (simple check)
            word[0] not in 'aeiou' and  # starts with consonant
            word[1] in 'aeiou' and  # one vowel in middle
            word[2] not in 'aeiou' and  # ends with one consonant
            suffix[0] in 'aeiou'):  # suffix starts with vowel
            
            return word + word[-1] + suffix  # double final consonant
        return word + suffix
    
    def apply_e_dropping(self, word: str, suffix: str) -> str:
        """
        Skill 25: E-dropping rule
        Drop silent e before adding vowel suffix
        """
        if word.endswith('e') and suffix[0] in 'aeiou':
            return word[:-1] + suffix
        return word + suffix
    
    def apply_y_to_i(self, word: str, suffix: str) -> str:
        """
        Skill 26: Change y to i
        Change y to i before suffix (except -ing)
        """
        if word.endswith('y') and word[-2] not in 'aeiou' and suffix != 'ing':
            return word[:-1] + 'i' + suffix
        return word + suffix
    
    def apply_plural_s(self, word: str) -> str:
        """
        Skill 27.1: Regular plural -s
        Add -s to most nouns
        """
        return word + 's'
    
    def apply_plural_es(self, word: str) -> str:
        """
        Skill 27.2: Plural -es
        Add -es after s, x, z, ch, sh (sibilant sounds)
        """
        if (word.endswith(('s', 'x', 'z')) or 
            word.endswith(('ch', 'sh', 'tch'))):
            return word + 'es'
        return word + 's'
    
    def apply_f_to_v_plural(self, word: str) -> str:
        """
        Skill 27.3: f to v plural
        Change f/fe to ves
        """
        if word.endswith('f'):
            return word[:-1] + 'ves'
        elif word.endswith('fe'):
            return word[:-2] + 'ves'
        return word + 's'
    
    def apply_2_1_1_doubling(self, word: str, suffix: str) -> str:
        """
        Skill 28: 2-1-1 doubling
        Two syllables, stress on second, one final consonant → double
        """
        # Simple check: words ending in single consonant after single vowel
        if (len(word) > 3 and 
            word[-1] not in 'aeiou' and 
            word[-2] in 'aeiou' and
            suffix[0] in 'aeiou'):
            
            # List of words with second syllable stress (would need full list)
            stressed_second = ['begin', 'forget', 'occur', 'refer', 'admit', 'commit', 'permit', 'submit']
            if word in stressed_second:
                return word + word[-1] + suffix
        return word + suffix
    
    def create_contraction(self, word1: str, word2: str) -> str:
        """
        Skill 30: Contractions
        Combine two words with apostrophe showing missing letters
        """
        contractions = {
            ('can', 'not'): "can't",
            ('will', 'not'): "won't",
            ('it', 'is'): "it's",
            ('I', 'will'): "I'll",
            ('they', 'are'): "they're",
            ('we', 'are'): "we're",
            ('you', 'are'): "you're",
            ('do', 'not'): "don't",
            ('is', 'not'): "isn't"
        }
        
        return contractions.get((word1, word2), f"{word1}'{word2}")
    
    def demonstrate_all_rules(self):
        """Demonstrate all spelling rules with examples"""
        
        print("SPELLING TRANSFORMATION RULES DEMONSTRATION")
        print("=" * 60)
        
        # Skill 24: 1-1-1 doubling
        print("\nSkill 24: 1-1-1 Doubling Rule")
        print("-" * 40)
        for word in self.base_words['cvc_words'][:5]:
            result = self.apply_1_1_1_doubling(word, 'ing')
            print(f"{word} + ing → {result}")
        
        # Skill 25: E-dropping
        print("\nSkill 25: E-dropping Rule")
        print("-" * 40)
        for word in self.base_words['vce_words'][:5]:
            result = self.apply_e_dropping(word, 'ing')
            print(f"{word} + ing → {result}")
        
        # Skill 26: Y to i
        print("\nSkill 26: Change Y to I")
        print("-" * 40)
        for word in self.base_words['y_ending_words'][:5]:
            result_er = self.apply_y_to_i(word, 'er')
            result_ing = self.apply_y_to_i(word, 'ing')
            print(f"{word} + er → {result_er}")
            print(f"{word} + ing → {result_ing} (exception: y stays)")
        
        # Skill 27.1: Regular plurals
        print("\nSkill 27.1: Regular Plural -s")
        print("-" * 40)
        for word in self.base_words['regular_plural'][:5]:
            result = self.apply_plural_s(word)
            print(f"{word} → {result}")
        
        # Skill 27.2: Plural -es
        print("\nSkill 27.2: Plural -es (after sibilants)")
        print("-" * 40)
        for word in self.base_words['sibilant_ending'][:5]:
            result = self.apply_plural_es(word)
            print(f"{word} → {result}")
        
        # Skill 27.3: f to v plural
        print("\nSkill 27.3: F to V Plural")
        print("-" * 40)
        for word in self.base_words['f_ending'][:5]:
            result = self.apply_f_to_v_plural(word)
            print(f"{word} → {result}")
        
        # Skill 28: 2-1-1 doubling
        print("\nSkill 28: 2-1-1 Doubling (2-syllable words)")
        print("-" * 40)
        for word in self.base_words['two_syllable_stressed'][:5]:
            result = self.apply_2_1_1_doubling(word, 'ing')
            print(f"{word} + ing → {result}")
        
        # Skill 30: Contractions
        print("\nSkill 30: Contractions")
        print("-" * 40)
        for word1, word2 in self.base_words['contraction_pairs'][:5]:
            result = self.create_contraction(word1, word2)
            print(f"{word1} + {word2} → {result}")

def integrate_with_word_generator():
    """Show how to integrate spelling rules with existing word generator"""
    
    print("\n\nINTEGRATION WITH WORD GENERATOR")
    print("=" * 60)
    
    print("""
    To integrate spelling rules with the phoneme-based generator:
    
    1. IDENTIFY RULE TYPE:
       - Phoneme-based: Generate words from scratch using grapheme-phoneme mappings
       - Transformation-based: Apply rules to existing base words
       - Structural: Identify patterns for syllable division
    
    2. FOR TRANSFORMATION RULES (24-30):
       
       def generate_words_for_spelling_rule(skill_id, rule_info):
           # Get appropriate base words
           if skill_id == '24':  # 1-1-1 doubling
               base_words = get_cvc_words()  # From skills 1.1-1.5
               suffix = 'ing'
               return [apply_1_1_1_doubling(w, suffix) for w in base_words]
           
           elif skill_id == '25':  # E-dropping
               base_words = get_vce_words()  # From skill 11.0
               suffixes = ['ing', 'ed', 'er']
               return [apply_e_dropping(w, s) for w in base_words for s in suffixes]
           
           # etc. for other rules
    
    3. VALIDATION:
       - Check if transformed word exists in dictionary
       - Verify it follows the expected pattern
       - Ensure prerequisites are met (e.g., student knows base word)
    
    4. WORD SELECTION CRITERIA:
       - Frequency (common words first)
       - Follows rule consistently (no exceptions initially)
       - Age-appropriate meaning
       - Clear example of the pattern
    """)

def main():
    """Run demonstrations"""
    processor = SpellingRuleProcessor()
    processor.demonstrate_all_rules()
    integrate_with_word_generator()

if __name__ == "__main__":
    main()