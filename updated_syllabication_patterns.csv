skill_id,skill_name,pattern_type,graphemes,required_phonemes,phoneme_description,position_constraints,word_structure,special_rules,examples,non_examples,notes
21.1,Starfish Words - Compound,syllabication,compound,structural,two complete words joined,between word boundaries,word+word,Find the two words in the compound word and divide between them,"star/fish, cup/cake, rain/bow, foot/ball, play/ground, sun/flower","starting (not compound), carpet (not compound)",Easiest pattern - students already know both words
21.2,Retriever Words - Prefix/Suffix,syllabication,prefix/suffix,structural,affix boundaries,after prefix or before suffix,prefix+root or root+suffix,"Circle prefix/suffix, divide after prefix or before suffix","re/turn, un/tie, jump/ing, tall/er, pre/view, hope/ful","return (if prefix not recognized), jumping (if suffix not recognized)",Requires knowledge of common prefixes/suffixes
21.3,Lion Words - Vowel/Vowel,syllabication,V/V,structural,two vowels meet,between adjacent vowels,vowel|vowel,Two vowels next to each other divide between them (makes first vowel open),"li/on, di/al, cre/ate, po/em, gi/ant, flu/id","rain (vowel team), boat (vowel team)",Not vowel teams - separate vowel sounds
21.4,<PERSON> Words - VC/CV,syllabication,VC/CV,structural,two consonants between vowels,between the consonants,vowel-CC-vowel,"Two consonants between vowels, divide between consonants","rab/bit, nap/kin, win/ter, hap/pen, sud/den, les/son","letter (double = one sound), pickle (ck = one unit)",Most common pattern in English
21.5,Ostrich Words - VCC/CV,syllabication,VCC/CV,structural,"When there are 3 consonants, we have to decide which 2 stay together (keep blends together). Finding syllable patterns and sounding them out helps to decide where to split.",keep blends/digraphs together,vowel-CC|C-vowel,"Three consonants, keep blends/digraphs together, divide before blend","ost/rich, sand/wich, ath/lete, pump/kin","hamster (divide differently), monster (divide differently)",ost/rich because when we split there it keeps the st blend together. 
21.6,Hamster Words - VC/CCV,syllabication,VC/CCV,structural,"When there are 3 consonants, we have to decide which 2 stay together (keep blends together). Finding syllable patterns and sounding them out helps to decide where to split.",keep blends/digraphs together,vowel-C|CC-vowel,"Three consonants, keep blends/digraphs together, divide before blend",ham/ster because when we split there it keeps the st blend together. ,,ham/ster because when we split there it keeps the st blend together. 
21.7,Tiger Words - V/CV,syllabication,V/CV,structural,Find the consonant between the vowels and divide the word after the first vowel. The first syllable will be open.,after first vowel,vowel|C-vowel,One consonant between vowels divide after first vowel (open syllable),"ti/ger, pa/per, mu/sic, ro/bot, ba/by, e/ven","camel (divide differently), never (divide differently)",First syllable is open (long vowel)
21.8,Camel Words - VC/V,syllabication,VC/V,structural,Find the consonant between the vowels. Divide these words after the consonant and before the second vowel. The first syllable is closed.,after consonant,vowel-C|vowel,One consonant between vowels divide after consonant (closed syllable),"cam/el, nev/er, lim/it, vis/it, rob/in, sev/en","tiger (divide differently), paper (divide differently)",First syllable is closed (short vowel)
21.9,Turtle Words - C+le,syllabication,Cle,structural,consonant + le ending,count back 3,final Cle syllable,Word ends in consonant + le count back 3 letters to divide,"tur/tle, ap/ple, ta/ble, sim/ple, mid/dle, puz/zle","battle (if not recognized), let (not C+le ending)",Final syllable is always consonant+le