# Word Bank v2 - Project Status and Next Steps

## Project Context
We discovered that the current word generator (v1) only checks if target spelling patterns appear in words, but doesn't verify that ALL sounds in a word can be decoded with learned patterns. For example, it would include "chat" for someone learning the "ch" digraph, even if they haven't learned the short "a" sound yet.

## Current Status (as of 2025-07-03)

### What We've Built
Located in `/Users/<USER>/word-generator/v2/`, we've created the foundation for a dynamic phoneme-based word filtering system:

1. **Core Architecture Completed:**
   - `core/phoneme_grapheme.py` - Maps graphemes (spellings) to phonemes (sounds)
   - `core/learner_profile.py` - Tracks individual learner's known patterns
   - `core/word_analyzer.py` - Breaks words into grapheme-phoneme components
   - `core/decodability.py` - Checks if words are decodable given known patterns
   - `example_usage.py` - Demonstrates the system in action

2. **Key Features Working:**
   - Tracks learner's mastered grapheme-phoneme mappings (e.g., "ch"→/CH/, "a"→/AE/)
   - Analyzes words into constituent parts (e.g., "chat" → [("ch", "CH"), ("a", "AE"), ("t", "T")])
   - Calculates decodability percentage for any word
   - Identifies unknown patterns that prevent full decodability

3. **Example Output:**
   ```
   After learning Skill 1.1 (CVC short a):
   cat: ✗ 67% decodable (Unknown: a→AE1)
   chat: ✗ 33% decodable (Unknown: ch→CH, a→AE1)
   ```

### Issues Discovered

1. **CMU Dictionary Format**: Uses stress markers (AE0, AE1, AE2) that need normalization
2. **Grapheme Segmentation**: Current algorithm struggles with complex words (e.g., "chain")
3. **Missing Mappings**: Only basic mappings loaded; needs comprehensive phonics curriculum data
4. **Heart Words**: System recognizes but doesn't yet handle exception words properly

## Immediate Next Steps

### 1. Fix CMU Phoneme Format (Priority: HIGH)
The CMU dictionary adds stress numbers (0,1,2) to vowel phonemes. We need to:
- Strip stress markers for matching: "AE1" → "AE"
- Preserve stress for advanced features later
- Update `word_analyzer.py` normalization

### 2. Load Comprehensive Mappings (Priority: HIGH)
Extract all grapheme-phoneme mappings from `enhanced_phonics_master_rules_complete.csv`:
- Parse the CSV to get graphemes, phonemes, and position constraints
- Create a data loading script in `scripts/load_curriculum_mappings.py`
- Handle multi-phoneme patterns (e.g., "tion"→/SH AH N/)

### 3. Improve Word Analysis Algorithm (Priority: HIGH)
Current segmentation is naive. Need to:
- Implement dynamic programming for optimal grapheme segmentation
- Handle ambiguous cases (e.g., "ea" can be /EE/ or /EH/)
- Use phoneme sequence to guide segmentation

### 4. Build Word Bank Database (Priority: MEDIUM)
Create pre-analyzed word corpus:
- Process common English words through the analyzer
- Store results in efficient database (SQLite)
- Include frequency rankings
- Cache for performance

### 5. Handle Heart Words (Priority: MEDIUM)
Implement proper exception word handling:
- Load heart words from existing CSV
- Mark as "teachable despite non-decodability"
- Track separately in learner profiles

### 6. Create API Layer (Priority: MEDIUM)
Build REST API for integration:
```python
# Example endpoints needed:
POST /api/learners/create
POST /api/learners/{id}/add-skill
GET /api/learners/{id}/decodable-words?count=50
GET /api/words/{word}/analyze?learner_id={id}
```

### 7. Optimize Performance (Priority: LOW)
- Add caching layer for word analyses
- Batch processing for word lists
- Index common queries

## Code Snippets to Address First

### Fix 1: Normalize CMU Phonemes
```python
# In word_analyzer.py
def normalize_phoneme(phoneme: str) -> str:
    """Remove stress markers from CMU phonemes"""
    return ''.join(c for c in phoneme if not c.isdigit())
```

### Fix 2: Load Curriculum Mappings
```python
# New file: scripts/load_curriculum_mappings.py
import pandas as pd
from core.phoneme_grapheme import PhonemeMappingEngine

def load_curriculum_mappings(csv_path, engine):
    df = pd.read_csv(csv_path)
    for _, row in df.iterrows():
        if pd.notna(row['graphemes']) and pd.notna(row['required_phonemes']):
            graphemes = str(row['graphemes']).split(', ')
            phonemes = str(row['required_phonemes']).split(', ')
            # Add mapping logic here
```

## Testing Checklist
- [ ] Verify all CVC words are 0% decodable before teaching any skills
- [ ] Confirm words become 100% decodable only after ALL patterns are taught
- [ ] Test multi-syllable word analysis
- [ ] Validate heart word handling
- [ ] Check performance with 10,000+ word corpus

## Questions to Resolve
1. Should we use IPA notation instead of CMU for better linguistic accuracy?
2. How to handle regional pronunciation differences?
3. Should decodability be binary (yes/no) or allow partial credit?
4. How to weight word frequency vs. decodability in selection?

## To Resume Development
Copy this entire file as your initial prompt, then focus on the immediate next steps in order. The foundation is solid; we need to refine the implementation and load real curriculum data.