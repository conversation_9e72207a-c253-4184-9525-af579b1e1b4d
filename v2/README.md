# Word Bank v2: Dynamic Phoneme-Based Word Filtering System

## Overview
A personalized word bank system that dynamically filters words based on learners' mastered phoneme-grapheme mappings, growing with them throughout their reading journey.

## Key Features
- **Phoneme-level decodability checking**: Ensures learners only see words they can decode
- **Personalized learner profiles**: Track individual progress and word exposure
- **Dynamic filtering**: Real-time word selection based on learned patterns
- **Progressive word bank**: Grows automatically as new patterns are mastered

## Architecture

### Core Components
1. **Phoneme-Grapheme Engine** (`core/phoneme_grapheme.py`)
   - Maps graphemes to phonemes
   - Analyzes words into constituent parts
   - Handles multiple valid pronunciations

2. **Learner Profile System** (`core/learner_profile.py`)
   - Tracks mastered skills and patterns
   - Records word exposure history
   - Supports multiple learners

3. **Decodability Checker** (`core/decodability.py`)
   - Determines if a word is decodable given known patterns
   - Calculates partial decodability scores
   - Handles exception words (heart words)

4. **Word Bank** (`core/word_bank.py`)
   - Pre-processed word database
   - Efficient filtering algorithms
   - Frequency and difficulty weighting

## Directory Structure
```
v2/
├── core/           # Core engine components
├── data/           # Data files and mappings
├── api/            # API layer for integration
├── tests/          # Unit and integration tests
├── scripts/        # Utility and setup scripts
└── config/         # Configuration files
```

## Getting Started
[Installation and usage instructions to be added]

## Development Status
- [ ] Phase 1: Foundation
- [ ] Phase 2: Core Engine
- [ ] Phase 3: Data Pipeline
- [ ] Phase 4: API Layer
- [ ] Phase 5: Integration & Testing