"""
Decodability Checker
Determines if words are decodable given a learner's known patterns
"""

from typing import List, Dict, Set, Tuple, Optional
from dataclasses import dataclass
from .learner_profile import LearnerProfile
from .word_analyzer import WordAnalyzer, WordAnalysis
from .phoneme_grapheme import PhonemeMappingEngine

@dataclass
class DecodabilityResult:
    """Result of checking if a word is decodable"""
    word: str
    is_fully_decodable: bool
    decodability_score: float  # 0.0 to 1.0
    known_mappings: List[Tuple[str, str]]
    unknown_mappings: List[Tuple[str, str]]
    is_heart_word: bool = False
    notes: List[str] = None
    
    def __post_init__(self):
        if self.notes is None:
            self.notes = []

class DecodabilityChecker:
    """Check if words are decodable for a specific learner"""
    
    def __init__(self, word_analyzer: WordAnalyzer, 
                 heart_words: Optional[Dict[str, Set[str]]] = None):
        self.word_analyzer = word_analyzer
        self.heart_words = heart_words or {}
    
    def check_word(self, word: str, learner: LearnerProfile,
                  allow_partial: bool = False) -> Optional[DecodabilityResult]:
        """
        Check if a word is decodable for a learner
        
        Args:
            word: Word to check
            learner: Learner profile with known patterns
            allow_partial: Whether to return partially decodable words
            
        Returns:
            DecodabilityResult or None if word can't be analyzed
        """
        # First, analyze the word
        analysis = self.word_analyzer.analyze_word(word)
        if not analysis:
            return None
        
        # Check if it's a heart word for any of the learner's skills
        is_heart_word = False
        for skill_id in learner.mastered_skills:
            if word.lower() in self.heart_words.get(skill_id, set()):
                is_heart_word = True
                break
        
        # Check each grapheme-phoneme mapping
        known_mappings = []
        unknown_mappings = []
        
        for grapheme, phoneme in analysis.mappings:
            if learner.knows_mapping(grapheme, phoneme):
                known_mappings.append((grapheme, phoneme))
            else:
                unknown_mappings.append((grapheme, phoneme))
        
        # Calculate decodability score
        total_mappings = len(analysis.mappings)
        if total_mappings == 0:
            decodability_score = 0.0
        else:
            decodability_score = len(known_mappings) / total_mappings
        
        is_fully_decodable = len(unknown_mappings) == 0
        
        # Build result
        result = DecodabilityResult(
            word=word,
            is_fully_decodable=is_fully_decodable,
            decodability_score=decodability_score,
            known_mappings=known_mappings,
            unknown_mappings=unknown_mappings,
            is_heart_word=is_heart_word
        )
        
        # Add notes
        if is_heart_word:
            result.notes.append("Heart word (exception word)")
        
        if unknown_mappings:
            unknown_str = ", ".join([f"{g}→{p}" for g, p in unknown_mappings[:3]])
            if len(unknown_mappings) > 3:
                unknown_str += f" and {len(unknown_mappings)-3} more"
            result.notes.append(f"Unknown patterns: {unknown_str}")
        
        # Only return if fully decodable or partial allowed
        if is_fully_decodable or allow_partial or is_heart_word:
            return result
        
        return None
    
    def check_word_list(self, words: List[str], learner: LearnerProfile,
                       min_decodability: float = 1.0) -> List[DecodabilityResult]:
        """
        Check decodability for a list of words
        
        Args:
            words: List of words to check
            learner: Learner profile
            min_decodability: Minimum decodability score (0.0 to 1.0)
            
        Returns:
            List of DecodabilityResult for words meeting criteria
        """
        results = []
        allow_partial = min_decodability < 1.0
        
        for word in words:
            result = self.check_word(word, learner, allow_partial=allow_partial)
            if result and result.decodability_score >= min_decodability:
                results.append(result)
        
        return results
    
    def find_teaching_examples(self, grapheme: str, phoneme: str,
                             learner: LearnerProfile,
                             candidate_words: List[str],
                             max_results: int = 10) -> List[DecodabilityResult]:
        """
        Find words that would be good for teaching a new grapheme-phoneme mapping
        
        These should be words where:
        1. The target mapping is the ONLY unknown pattern
        2. The word is otherwise fully decodable
        3. The word clearly demonstrates the pattern
        
        Args:
            grapheme: Target grapheme to teach
            phoneme: Target phoneme to teach  
            learner: Current learner profile
            candidate_words: Pool of words to search
            max_results: Maximum examples to return
            
        Returns:
            List of good teaching examples
        """
        teaching_examples = []
        
        for word in candidate_words:
            result = self.check_word(word, learner, allow_partial=True)
            if not result:
                continue
            
            # Check if this word contains our target mapping
            has_target = False
            for g, p in result.unknown_mappings:
                if g == grapheme and p == phoneme:
                    has_target = True
                    break
            
            if not has_target:
                continue
            
            # Check if target mapping is the ONLY unknown
            other_unknowns = [(g, p) for g, p in result.unknown_mappings 
                            if g != grapheme or p != phoneme]
            
            if len(other_unknowns) == 0:
                # This is a good teaching example!
                teaching_examples.append(result)
                
                if len(teaching_examples) >= max_results:
                    break
        
        # Sort by word frequency or other criteria
        return teaching_examples
    
    def get_decodability_stats(self, words: List[str], 
                              learner: LearnerProfile) -> Dict[str, any]:
        """Get statistics about decodability for a word list"""
        results = self.check_word_list(words, learner, min_decodability=0.0)
        
        fully_decodable = sum(1 for r in results if r.is_fully_decodable)
        partially_decodable = sum(1 for r in results if 0 < r.decodability_score < 1)
        not_decodable = len(words) - len(results)
        
        avg_score = sum(r.decodability_score for r in results) / len(results) if results else 0
        
        # Find most common unknown patterns
        unknown_pattern_counts = {}
        for result in results:
            for pattern in result.unknown_mappings:
                pattern_str = f"{pattern[0]}→{pattern[1]}"
                unknown_pattern_counts[pattern_str] = unknown_pattern_counts.get(pattern_str, 0) + 1
        
        most_common_unknowns = sorted(unknown_pattern_counts.items(), 
                                     key=lambda x: x[1], reverse=True)[:10]
        
        return {
            'total_words': len(words),
            'fully_decodable': fully_decodable,
            'partially_decodable': partially_decodable, 
            'not_analyzable': not_decodable,
            'average_decodability': avg_score,
            'most_common_unknown_patterns': most_common_unknowns
        }