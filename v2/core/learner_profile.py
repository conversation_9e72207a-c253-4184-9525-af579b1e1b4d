"""
Learner Profile System
Tracks individual learner progress and mastered patterns
"""

from dataclasses import dataclass, field
from typing import Set, Dict, List, Optional, Tuple
from datetime import datetime
import json
from pathlib import Path

@dataclass
class WordExposure:
    """Record of a learner's interaction with a word"""
    word: str
    first_seen: datetime
    last_seen: datetime
    times_seen: int = 1
    times_correct: int = 0
    mastered: bool = False

@dataclass 
class SkillProgress:
    """Progress tracking for a specific skill"""
    skill_id: str
    started: datetime
    completed: Optional[datetime] = None
    proficiency_score: float = 0.0  # 0.0 to 1.0
    words_practiced: int = 0

class LearnerProfile:
    """Individual learner's progress and capabilities"""
    
    def __init__(self, learner_id: str, name: str = ""):
        self.learner_id = learner_id
        self.name = name
        self.created = datetime.now()
        
        # Core tracking data
        self.mastered_skills: Set[str] = set()
        self.mastered_graphemes: Set[str] = set()
        self.mastered_phonemes: Set[str] = set()
        self.known_mappings: Set[Tuple[str, str]] = set()  # (grapheme, phoneme) pairs
        
        # Progress tracking
        self.skill_progress: Dict[str, SkillProgress] = {}
        self.word_history: Dict[str, WordExposure] = {}
        
        # Learning preferences
        self.preferences = {
            'min_word_frequency': 0,  # Minimum frequency rank
            'max_word_length': 10,
            'prefer_high_frequency': True,
            'include_proper_nouns': False
        }
    
    def add_skill(self, skill_id: str, grapheme_phoneme_pairs: List[Tuple[str, str]]):
        """
        Mark a skill as mastered and add its grapheme-phoneme mappings
        
        Args:
            skill_id: Skill identifier (e.g., "1.1")
            grapheme_phoneme_pairs: List of (grapheme, phoneme) tuples taught in this skill
        """
        self.mastered_skills.add(skill_id)
        
        # Add all grapheme-phoneme pairs from this skill
        for grapheme, phoneme in grapheme_phoneme_pairs:
            self.known_mappings.add((grapheme, phoneme))
            self.mastered_graphemes.add(grapheme)
            self.mastered_phonemes.add(phoneme)
        
        # Update skill progress
        if skill_id not in self.skill_progress:
            self.skill_progress[skill_id] = SkillProgress(
                skill_id=skill_id,
                started=datetime.now()
            )
        self.skill_progress[skill_id].completed = datetime.now()
        self.skill_progress[skill_id].proficiency_score = 1.0
    
    def record_word_exposure(self, word: str, correct: bool = True):
        """Record that the learner has seen/practiced a word"""
        word_lower = word.lower()
        
        if word_lower not in self.word_history:
            self.word_history[word_lower] = WordExposure(
                word=word_lower,
                first_seen=datetime.now(),
                last_seen=datetime.now()
            )
        else:
            exposure = self.word_history[word_lower]
            exposure.last_seen = datetime.now()
            exposure.times_seen += 1
            
        if correct:
            self.word_history[word_lower].times_correct += 1
            
        # Check for mastery (e.g., 3+ correct in a row)
        exposure = self.word_history[word_lower]
        if exposure.times_correct >= 3:
            exposure.mastered = True
    
    def knows_mapping(self, grapheme: str, phoneme: str) -> bool:
        """Check if learner knows a specific grapheme-phoneme mapping"""
        return (grapheme, phoneme) in self.known_mappings
    
    def get_mastery_level(self) -> Dict[str, int]:
        """Get summary statistics about learner's progress"""
        return {
            'total_skills': len(self.mastered_skills),
            'total_graphemes': len(self.mastered_graphemes),
            'total_phonemes': len(self.mastered_phonemes),
            'total_mappings': len(self.known_mappings),
            'words_seen': len(self.word_history),
            'words_mastered': sum(1 for w in self.word_history.values() if w.mastered)
        }
    
    def save_to_file(self, filepath: Path):
        """Save learner profile to JSON file"""
        data = {
            'learner_id': self.learner_id,
            'name': self.name,
            'created': self.created.isoformat(),
            'mastered_skills': list(self.mastered_skills),
            'mastered_graphemes': list(self.mastered_graphemes),
            'mastered_phonemes': list(self.mastered_phonemes),
            'known_mappings': [list(pair) for pair in self.known_mappings],
            'preferences': self.preferences,
            'word_history': {
                word: {
                    'first_seen': exp.first_seen.isoformat(),
                    'last_seen': exp.last_seen.isoformat(),
                    'times_seen': exp.times_seen,
                    'times_correct': exp.times_correct,
                    'mastered': exp.mastered
                }
                for word, exp in self.word_history.items()
            }
        }
        
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2)
    
    @classmethod
    def load_from_file(cls, filepath: Path) -> 'LearnerProfile':
        """Load learner profile from JSON file"""
        with open(filepath, 'r') as f:
            data = json.load(f)
        
        profile = cls(data['learner_id'], data['name'])
        profile.created = datetime.fromisoformat(data['created'])
        profile.mastered_skills = set(data['mastered_skills'])
        profile.mastered_graphemes = set(data['mastered_graphemes'])
        profile.mastered_phonemes = set(data['mastered_phonemes'])
        profile.known_mappings = {tuple(pair) for pair in data['known_mappings']}
        profile.preferences = data['preferences']
        
        # Restore word history
        for word, exp_data in data.get('word_history', {}).items():
            profile.word_history[word] = WordExposure(
                word=word,
                first_seen=datetime.fromisoformat(exp_data['first_seen']),
                last_seen=datetime.fromisoformat(exp_data['last_seen']),
                times_seen=exp_data['times_seen'],
                times_correct=exp_data['times_correct'],
                mastered=exp_data['mastered']
            )
        
        return profile