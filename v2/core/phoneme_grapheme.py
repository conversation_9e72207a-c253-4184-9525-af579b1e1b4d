"""
Phoneme-Grapheme Mapping Engine
Handles the relationship between written graphemes and spoken phonemes
"""

from dataclasses import dataclass
from typing import List, Dict, Set, Tuple, Optional
from enum import Enum
import json

class PhonemeNotation(Enum):
    """Supported phoneme notation systems"""
    CMU = "cmu"  # CMU Pronouncing Dictionary format
    IPA = "ipa"  # International Phonetic Alphabet
    
@dataclass
class GraphemePhoneme:
    """Represents a grapheme-phoneme correspondence"""
    grapheme: str  # Written form (e.g., "ch", "a", "igh")
    phoneme: str   # Sound representation (e.g., "CH", "AE", "AY")
    position_constraints: Optional[str] = None  # "initial", "medial", "final", "any"
    examples: List[str] = None
    skill_id: Optional[str] = None  # Reference to curriculum skill
    
    def __post_init__(self):
        if self.examples is None:
            self.examples = []

@dataclass
class WordAnalysis:
    """Complete phoneme-grapheme breakdown of a word"""
    word: str
    graphemes: List[str]
    phonemes: List[str]
    mappings: List[Tuple[str, str]]  # (grapheme, phoneme) pairs
    syllable_count: int
    
    @property
    def grapheme_set(self) -> Set[str]:
        """Unique graphemes in the word"""
        return set(self.graphemes)
    
    @property
    def phoneme_set(self) -> Set[str]:
        """Unique phonemes in the word"""
        return set(self.phonemes)

class PhonemeMappingEngine:
    """Main engine for phoneme-grapheme analysis"""
    
    def __init__(self):
        self.mappings: Dict[str, List[GraphemePhoneme]] = {}
        self.reverse_mappings: Dict[str, List[GraphemePhoneme]] = {}
        self._load_base_mappings()
    
    def _load_base_mappings(self):
        """Load fundamental phoneme-grapheme mappings"""
        # This will be expanded with comprehensive mappings
        # For now, adding basic examples
        
        # Short vowels
        self._add_mapping("a", "AE", examples=["cat", "hat", "mat"])
        self._add_mapping("e", "EH", examples=["bed", "red", "pet"])
        self._add_mapping("i", "IH", examples=["sit", "hit", "big"])
        self._add_mapping("o", "AA", examples=["hot", "pot", "dog"])
        self._add_mapping("u", "AH", examples=["cup", "run", "sun"])
        
        # Common consonants
        self._add_mapping("b", "B", examples=["bat", "cab"])
        self._add_mapping("c", "K", position_constraints="before a,o,u", examples=["cat", "cot", "cut"])
        self._add_mapping("d", "D", examples=["dog", "bad"])
        self._add_mapping("f", "F", examples=["fun", "off"])
        self._add_mapping("g", "G", position_constraints="before a,o,u", examples=["got", "bag"])
        
        # Digraphs
        self._add_mapping("ch", "CH", examples=["chat", "much"])
        self._add_mapping("sh", "SH", examples=["ship", "fish"])
        self._add_mapping("th", "TH", examples=["thin", "math"])
        self._add_mapping("th", "DH", examples=["this", "with"])
        self._add_mapping("ck", "K", position_constraints="final", examples=["back", "duck"])
        
        # Long vowel teams
        self._add_mapping("ai", "EY", examples=["rain", "train"])
        self._add_mapping("ay", "EY", position_constraints="final", examples=["day", "play"])
        self._add_mapping("ee", "IY", examples=["see", "tree"])
        self._add_mapping("ea", "IY", examples=["read", "team"])
        self._add_mapping("ea", "EH", examples=["bread", "head"])
        
    def _add_mapping(self, grapheme: str, phoneme: str, 
                    position_constraints: Optional[str] = None,
                    examples: List[str] = None,
                    skill_id: Optional[str] = None):
        """Add a grapheme-phoneme mapping"""
        mapping = GraphemePhoneme(
            grapheme=grapheme,
            phoneme=phoneme,
            position_constraints=position_constraints,
            examples=examples or [],
            skill_id=skill_id
        )
        
        # Add to forward mapping (grapheme -> phoneme)
        if grapheme not in self.mappings:
            self.mappings[grapheme] = []
        self.mappings[grapheme].append(mapping)
        
        # Add to reverse mapping (phoneme -> grapheme)
        if phoneme not in self.reverse_mappings:
            self.reverse_mappings[phoneme] = []
        self.reverse_mappings[phoneme].append(mapping)
    
    def analyze_word(self, word: str, pronunciation: List[str]) -> Optional[WordAnalysis]:
        """
        Analyze a word into its grapheme-phoneme components
        
        Args:
            word: The written word
            pronunciation: List of phonemes (CMU dict style)
            
        Returns:
            WordAnalysis object or None if analysis fails
        """
        # This is a placeholder - full implementation will use
        # dynamic programming to find optimal grapheme-phoneme alignment
        # For now, return a simple example
        
        # TODO: Implement full word analysis algorithm
        return None
    
    def get_graphemes_for_phoneme(self, phoneme: str) -> List[GraphemePhoneme]:
        """Get all graphemes that can represent a phoneme"""
        return self.reverse_mappings.get(phoneme, [])
    
    def get_phonemes_for_grapheme(self, grapheme: str) -> List[GraphemePhoneme]:
        """Get all phonemes that a grapheme can represent"""
        return self.mappings.get(grapheme, [])
    
    def load_from_file(self, filepath: str):
        """Load mappings from a JSON file"""
        with open(filepath, 'r') as f:
            data = json.load(f)
            for mapping_data in data:
                self._add_mapping(**mapping_data)
    
    def knows_mapping(self, grapheme: str, phoneme: str) -> bool:
        """Check if a specific grapheme-phoneme mapping exists"""
        if grapheme in self.mappings:
            for mapping in self.mappings[grapheme]:
                if mapping.phoneme == phoneme:
                    return True
        return False
    
    def save_to_file(self, filepath: str):
        """Save mappings to a JSON file"""
        all_mappings = []
        for grapheme_mappings in self.mappings.values():
            for mapping in grapheme_mappings:
                all_mappings.append({
                    'grapheme': mapping.grapheme,
                    'phoneme': mapping.phoneme,
                    'position_constraints': mapping.position_constraints,
                    'examples': mapping.examples,
                    'skill_id': mapping.skill_id
                })
        
        with open(filepath, 'w') as f:
            json.dump(all_mappings, f, indent=2)