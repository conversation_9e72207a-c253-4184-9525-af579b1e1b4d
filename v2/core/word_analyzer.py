"""
Word Analysis Engine
Breaks words into grapheme-phoneme components using CMU dict and rules
"""

import nltk
from typing import List, Dict, Optional, Tuple, Set
from dataclasses import dataclass
import re
from .phoneme_grapheme import WordAnalysis, PhonemeMappingEngine

class WordAnalyzer:
    """Analyzes words into their grapheme-phoneme components"""
    
    def __init__(self, mapping_engine: PhonemeMappingEngine):
        self.mapping_engine = mapping_engine
        self._load_cmu_dict()
        
        # Common grapheme patterns (ordered by length for matching)
        self.grapheme_patterns = [
            # 4-letter patterns
            'eigh', 'ough', 'augh',
            # 3-letter patterns  
            'tch', 'dge', 'igh', 'ear', 'air', 'are', 'ore',
            # 2-letter patterns
            'ch', 'sh', 'th', 'wh', 'ph', 'ck', 'ng', 'nk',
            'ai', 'ay', 'ea', 'ee', 'ey', 'ie', 'oa', 'oe', 
            'oi', 'oy', 'oo', 'ou', 'ow', 'ue', 'ui', 'ew',
            'ar', 'er', 'ir', 'or', 'ur', 'aw', 'au', 'al',
            'kn', 'wr', 'gn', 'mb', 'mn'
        ]
    
    def _load_cmu_dict(self):
        """Load CMU pronunciation dictionary"""
        try:
            self.cmu_dict = nltk.corpus.cmudict.dict()
        except:
            print("Downloading CMU dictionary...")
            nltk.download('cmudict')
            self.cmu_dict = nltk.corpus.cmudict.dict()
    
    def get_pronunciations(self, word: str) -> List[List[str]]:
        """Get all possible pronunciations for a word from CMU dict"""
        return self.cmu_dict.get(word.lower(), [])
    
    def segment_into_graphemes(self, word: str) -> List[List[str]]:
        """
        Segment a word into possible grapheme sequences
        Returns multiple possibilities when ambiguous
        """
        word_lower = word.lower()
        possible_segmentations = []
        
        # Use dynamic programming to find all valid segmentations
        def segment_recursive(pos: int, current_segments: List[str]):
            if pos == len(word_lower):
                possible_segmentations.append(current_segments[:])
                return
            
            # Try single letter
            if pos < len(word_lower):
                current_segments.append(word_lower[pos])
                segment_recursive(pos + 1, current_segments)
                current_segments.pop()
            
            # Try multi-letter graphemes
            for pattern in self.grapheme_patterns:
                if word_lower[pos:].startswith(pattern):
                    current_segments.append(pattern)
                    segment_recursive(pos + len(pattern), current_segments)
                    current_segments.pop()
        
        segment_recursive(0, [])
        
        # If no valid segmentations found, fall back to letter-by-letter
        if not possible_segmentations:
            possible_segmentations.append(list(word_lower))
        
        return possible_segmentations
    
    def align_graphemes_phonemes(self, graphemes: List[str], 
                                phonemes: List[str]) -> Optional[List[Tuple[str, str]]]:
        """
        Align a grapheme sequence with a phoneme sequence
        Returns list of (grapheme, phoneme) pairs or None if alignment fails
        """
        # This is a simplified version - full implementation would use
        # probabilistic alignment based on known mappings
        
        # For now, implement a basic heuristic alignment
        alignments = []
        g_idx = 0
        p_idx = 0
        
        while g_idx < len(graphemes) and p_idx < len(phonemes):
            grapheme = graphemes[g_idx]
            
            # Check if this grapheme has known phoneme mappings
            possible_phonemes = self.mapping_engine.get_phonemes_for_grapheme(grapheme)
            
            if possible_phonemes:
                # Try to match with current phoneme
                matched = False
                for mapping in possible_phonemes:
                    if p_idx < len(phonemes) and phonemes[p_idx] == mapping.phoneme:
                        alignments.append((grapheme, phonemes[p_idx]))
                        g_idx += 1
                        p_idx += 1
                        matched = True
                        break
                
                if not matched:
                    # No known mapping matches - this is a problem
                    # For now, create a mapping anyway
                    alignments.append((grapheme, phonemes[p_idx] if p_idx < len(phonemes) else 'X'))
                    g_idx += 1
                    p_idx += 1
            else:
                # Unknown grapheme - create mapping
                alignments.append((grapheme, phonemes[p_idx] if p_idx < len(phonemes) else 'X'))
                g_idx += 1
                p_idx += 1
        
        # Handle any remaining graphemes or phonemes
        while g_idx < len(graphemes):
            alignments.append((graphemes[g_idx], 'X'))  # X = no phoneme
            g_idx += 1
        
        return alignments
    
    def analyze_word(self, word: str, 
                    pronunciation: Optional[List[str]] = None) -> Optional[WordAnalysis]:
        """
        Fully analyze a word into its components
        
        Args:
            word: The word to analyze
            pronunciation: Optional specific pronunciation to use
            
        Returns:
            WordAnalysis object or None if analysis fails
        """
        # Get pronunciation(s)
        if pronunciation is None:
            pronunciations = self.get_pronunciations(word)
            if not pronunciations:
                return None
            pronunciation = pronunciations[0]  # Use first pronunciation
        
        # Get possible grapheme segmentations
        segmentations = self.segment_into_graphemes(word)
        
        # Try to align with pronunciation
        best_alignment = None
        best_score = -1
        
        for graphemes in segmentations:
            alignment = self.align_graphemes_phonemes(graphemes, pronunciation)
            if alignment:
                # Score based on how many mappings are known
                score = sum(1 for g, p in alignment 
                          if self.mapping_engine.knows_mapping(g, p))
                if score > best_score:
                    best_score = score
                    best_alignment = (graphemes, alignment)
        
        if best_alignment:
            graphemes, mappings = best_alignment
            phonemes = [p for _, p in mappings]
            
            # Count syllables (number of vowel sounds)
            vowel_phonemes = {'AA', 'AE', 'AH', 'AO', 'AW', 'AY', 'EH', 'ER',
                            'EY', 'IH', 'IY', 'OW', 'OY', 'UH', 'UW'}
            syllable_count = sum(1 for p in phonemes if any(v in p for v in vowel_phonemes))
            
            return WordAnalysis(
                word=word,
                graphemes=graphemes,
                phonemes=phonemes,
                mappings=mappings,
                syllable_count=max(1, syllable_count)
            )
        
        return None
    
    def find_unknown_patterns(self, word_analysis: WordAnalysis) -> List[Tuple[str, str]]:
        """Find grapheme-phoneme pairs that aren't in our mapping engine"""
        unknown = []
        for grapheme, phoneme in word_analysis.mappings:
            if not self.mapping_engine.knows_mapping(grapheme, phoneme):
                unknown.append((grapheme, phoneme))
        return unknown