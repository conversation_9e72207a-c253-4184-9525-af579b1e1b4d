#!/usr/bin/env python3
"""
Example usage of the Word Bank v2 system
Demonstrates how the components work together
"""

from core.phoneme_grapheme import PhonemeMappingEngine
from core.learner_profile import LearnerProfile  
from core.word_analyzer import WordAnalyzer
from core.decodability import Decodability<PERSON><PERSON><PERSON>

def main():
    print("Word Bank v2 - Example Usage")
    print("=" * 50)
    
    # Initialize components
    print("\n1. Initializing components...")
    mapping_engine = PhonemeMappingEngine()
    word_analyzer = WordAnalyzer(mapping_engine)
    decodability_checker = DecodabilityChecker(word_analyzer)
    
    # Create a learner profile
    print("\n2. Creating learner profile...")
    learner = LearnerProfile("student_001", "Emma")
    
    # Simulate learning progression
    print("\n3. Simulating skill progression...")
    
    # Skill 1.1: CVC short a
    print("\n   Learning Skill 1.1 (CVC short a)...")
    learner.add_skill("1.1", [
        ("c", "K"), ("a", "AE"), ("t", "T"),
        ("b", "B"), ("h", "HH"), ("m", "M"),
        ("s", "S"), ("p", "P"), ("n", "N"),
        ("r", "R"), ("d", "D"), ("g", "G")
    ])
    
    # Check some words
    print("\n4. Checking word decodability after Skill 1.1...")
    test_words = ["cat", "hat", "mat", "chat", "chain", "the", "dog", "big"]
    
    for word in test_words:
        result = decodability_checker.check_word(word, learner, allow_partial=True)
        if result:
            status = "✓ Fully decodable" if result.is_fully_decodable else f"✗ {result.decodability_score:.0%} decodable"
            print(f"   {word}: {status}")
            if result.unknown_mappings:
                unknown = ", ".join([f"{g}→{p}" for g, p in result.unknown_mappings])
                print(f"     Unknown: {unknown}")
    
    # Add more skills
    print("\n5. Learning Skill 1.2 (CVC short e)...")
    learner.add_skill("1.2", [
        ("e", "EH"), ("b", "B"), ("d", "D"),
        ("g", "G"), ("t", "T"), ("p", "P")
    ])
    
    print("\n6. Learning Skill 4.1 (ch digraph)...")
    learner.add_skill("4.1", [("ch", "CH")])
    
    # Check words again
    print("\n7. Checking words after learning more skills...")
    for word in ["chat", "chest", "check"]:
        result = decodability_checker.check_word(word, learner, allow_partial=True)
        if result:
            status = "✓ Fully decodable" if result.is_fully_decodable else f"✗ {result.decodability_score:.0%} decodable"
            print(f"   {word}: {status}")
    
    # Show learner progress
    print("\n8. Learner Progress Summary:")
    stats = learner.get_mastery_level()
    for key, value in stats.items():
        print(f"   {key}: {value}")
    
    # Demonstrate word analysis
    print("\n9. Word Analysis Example:")
    word = "chat"
    analysis = word_analyzer.analyze_word(word)
    if analysis:
        print(f"   Word: {word}")
        print(f"   Graphemes: {' - '.join(analysis.graphemes)}")
        print(f"   Phonemes: {' - '.join(analysis.phonemes)}")
        print(f"   Mappings: {analysis.mappings}")
        print(f"   Syllables: {analysis.syllable_count}")

if __name__ == "__main__":
    main()