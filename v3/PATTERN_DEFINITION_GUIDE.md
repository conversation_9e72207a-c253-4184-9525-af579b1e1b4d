# Pattern Definition Guide for Orton-Gillingham Word Generator

## Overview

This guide explains how to define and configure phoneme-based patterns for the Orton-Gillingham word generator. The new system maps graphemes (letter patterns) to phonemes (sounds) to ensure accurate word generation based on linguistic principles rather than just spelling patterns.

## Core Concepts

### 1. Phoneme-Centric Design
Every pattern is defined by its **target phoneme** first, then mapped to the graphemes that can represent that sound. This ensures we're generating words based on sounds, not just letter combinations.

### 2. Pattern Classes
Patterns are organized into classes based on their linguistic function:
- **CVC**: Consonant-Vowel-Consonant patterns
- **VowelTeam**: Two or more vowels making one sound
- **Blend**: Two or more consonants with distinct sounds
- **Digraph**: Two letters making one sound
- **RControlled**: Vowels controlled by 'r'
- **MagicE**: Silent E patterns
- **Diphthong**: Two vowels creating a new sound
- **SpellingRule**: Orthographic patterns (c/k rules, etc.)
- **SilentLetter**: Letters that don't make sounds
- **Syllabication**: Syllable division patterns

## Pattern Definition Fields

### Required Fields

#### `pattern_id`
- **Format**: UPPERCASE_WITH_UNDERSCORES
- **Example**: `SHORT_A_CVC`, `LONG_E_TEAMS`
- **Purpose**: Unique identifier for the pattern

#### `pattern_name`
- **Format**: Human-readable description
- **Example**: "Short A in CVC", "Long E Vowel Teams"
- **Purpose**: Display name for UI and reports

#### `target_phoneme`
- **Format**: CMU Pronouncing Dictionary phoneme codes
- **Example**: `AE` (short a), `EY` (long a), `ER` (r-controlled er)
- **Purpose**: The primary sound this pattern produces
- **Reference**: [CMU Phoneme Set](http://www.speech.cs.cmu.edu/cgi-bin/cmudict)

Common phonemes:
- Vowels: `AE` (cat), `EH` (bed), `IH` (sit), `AA` (hot), `AH` (cup)
- Long vowels: `EY` (cake), `IY` (see), `AY` (bike), `OW` (home), `YUW` (cute)
- R-controlled: `ER` (her), `AA R` (car), `AO R` (for)
- Diphthongs: `AW` (cow), `OY` (boy), `AO` (saw)

#### `grapheme_mappings`
- **Format**: JSON object mapping graphemes to positions
- **Example**: `{"ai": "medial", "ay": "final"}`
- **Purpose**: Defines which letter patterns represent this phoneme and where they can appear
- **Position values**: `initial`, `medial`, `final`, `any`, `split` (for magic e)

### Optional Fields

#### `phoneme_variants`
- **Format**: Comma-separated phoneme codes
- **Example**: `TH,DH` (voiced and unvoiced th)
- **Purpose**: Alternative pronunciations

#### `validation_rules`
- **Format**: JSON array of validation requirements
- **Example**: `["word_length_exact:3", "consonant_vowel_consonant", "no_blends"]`
- **Purpose**: Constraints that must be met for a word to match this pattern

Available validation rules:
- `word_length_exact:N` - Word must be exactly N letters
- `word_length_range:N-M` - Word must be N to M letters
- `consonant_vowel_consonant` - CVC structure required
- `no_blends` - No consonant blends allowed
- `no_digraphs` - No digraphs allowed
- `single_syllable` - One syllable only
- `two_syllables` - Exactly two syllables
- `one_syllable` - Exactly one syllable
- `both_sounds_heard` - For blends, both consonants must be pronounced
- `all_sounds_heard` - For 3-letter blends
- `true_blend` - Not a digraph
- `after_short_vowel` - Pattern must follow short vowel
- `after_long_vowel` - Pattern must follow long vowel
- `specific_words_only` - Limited to examples list
- `verify_pronunciation:true` - Check CMU dict for correct phoneme
- `exclude_exceptions:true` - Skip known exceptions
- `initial_only` - Must be at word beginning
- `final_only` - Must be at word end
- `medial_only` - Must be in middle
- `silent_e` - Requires silent e pattern
- `consonant_between` - For magic e, needs consonant between vowel and e

#### `position_constraints`
- **Format**: `initial`, `medial`, `final`, `any`, `split`, `boundary`
- **Purpose**: Global position rule for the pattern

#### `context_requirements`
- **Format**: Contextual conditions
- **Examples**: `after_short_vowel`, `one_syllable`, `unstressed`
- **Purpose**: Environmental requirements for the pattern

#### `excluded_patterns`
- **Format**: JSON array of pattern IDs
- **Example**: `["MAGIC_E", "VOWEL_TEAM", "R_CONTROLLED"]`
- **Purpose**: Patterns that invalidate this one (e.g., magic e overrides CVC)

#### `frequency_threshold`
- **Format**: Integer (0-100)
- **Purpose**: Minimum frequency score from Brown corpus (higher = more common words only)

## How to Add New Patterns

### Step 1: Identify the Phoneme
What sound are you trying to capture? Use the CMU phoneme set to identify the code.

### Step 2: Map Graphemes
What letter patterns make this sound? Where can they appear in words?

### Step 3: Define Validation Rules
What constraints must be met? Consider:
- Word length
- Syllable count
- Position requirements
- Phonological context
- Excluded patterns

### Step 4: Provide Examples
Give clear examples and non-examples to guide the system.

## Example Pattern Definitions

### Simple CVC Pattern
```csv
pattern_id: SHORT_A_CVC
pattern_name: Short A in CVC
target_phoneme: AE
grapheme_mappings: {"a": "medial"}
pattern_class: CVC
validation_rules: ["word_length_exact:3", "consonant_vowel_consonant", "no_blends"]
excluded_patterns: ["MAGIC_E", "VOWEL_TEAM"]
examples: cat, hat, mat
non_examples: cake, rain, chat
```

### Complex Vowel Team
```csv
pattern_id: LONG_A_TEAMS
pattern_name: Long A Vowel Teams
target_phoneme: EY
grapheme_mappings: {"ai": "medial", "ay": "final", "eigh": "medial", "ey": "final"}
pattern_class: VowelTeam
validation_rules: ["verify_pronunciation:true", "min_5_per_pattern:true"]
position_constraints: any
examples: rain, play, eight, they
non_examples: said, again
```

### Spelling Rule Pattern
```csv
pattern_id: TCH_CATCH_RULE
pattern_name: TCH after short vowel
target_phoneme: CH
grapheme_mappings: {"tch": "final"}
pattern_class: SpellingRule
validation_rules: ["after_short_vowel", "one_syllable", "ch_sound"]
context_requirements: after_short_vowel
examples: catch, match, pitch
non_examples: much, such, which
```

## Testing Your Patterns

1. **Phoneme Verification**: Does the pattern correctly identify words with the target sound?
2. **Position Testing**: Are graphemes appearing in the correct positions?
3. **Exclusion Testing**: Are conflicting patterns properly excluded?
4. **Example Validation**: Do all examples pass the validation rules?
5. **Non-Example Validation**: Are non-examples correctly rejected?

## Common Issues and Solutions

### Issue: Pattern matches incorrect words
**Solution**: Add more specific validation rules or excluded patterns

### Issue: Pattern too restrictive
**Solution**: Reduce frequency threshold or relax validation rules

### Issue: Wrong phoneme matches
**Solution**: Verify CMU phoneme codes and add pronunciation verification

### Issue: Position conflicts
**Solution**: Use grapheme_mappings to specify position per grapheme

## Advanced Configuration

### Multi-Phoneme Patterns
For patterns with multiple valid pronunciations:
```csv
phoneme_variants: TH,DH  # Voiced and unvoiced TH
```

### Context-Sensitive Patterns
For patterns that change based on context:
```csv
context_requirements: after_short_vowel
validation_rules: ["verify_context:true"]
```

### Hierarchical Patterns
For patterns that build on others:
```csv
excluded_patterns: ["MAGIC_E"]  # Magic E overrides this pattern
```

## Integration with Skills

Skills reference patterns through the `pattern_ids` field:
```csv
skill_id: 1.1
pattern_ids: SHORT_A_CVC
```

Skills can reference multiple patterns:
```csv
skill_id: 18.1
pattern_ids: LONG_A_AI,LONG_A_AY,LONG_A_EIGH
```

## Performance Considerations

1. **Frequency Thresholds**: Higher values = faster but fewer words
2. **Validation Rules**: More rules = slower but more accurate
3. **Pattern Complexity**: Simpler patterns generate faster

## Future Enhancements

### Planned Features
1. **Morpheme patterns**: Prefix/suffix handling
2. **Syllable stress patterns**: For schwa identification
3. **Dialect variations**: Regional pronunciation differences
4. **Exception lists**: Per-pattern exception handling

### Contributing
When adding new patterns:
1. Follow the naming conventions
2. Provide comprehensive examples
3. Test against real word lists
4. Document any special cases

## Resources

- [CMU Pronouncing Dictionary](http://www.speech.cs.cmu.edu/cgi-bin/cmudict)
- [IPA to CMU Conversion](https://en.wikipedia.org/wiki/CMU_Pronouncing_Dictionary)
- [Orton-Gillingham Scope and Sequence](https://www.orton-gillingham.com)