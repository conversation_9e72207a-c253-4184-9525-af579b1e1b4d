# V3: Phoneme-Centric <PERSON>tern Architecture

This directory contains the new phoneme-based pattern system for the Orton-Gillingham word generator.

## Overview

V3 represents a fundamental shift from grapheme-based (spelling) pattern matching to phoneme-based (sound) pattern matching. This ensures words are generated based on their actual pronunciation, not just their spelling patterns.

## Files

### Data Files
- **`phoneme_patterns_master.csv`** - Master list of all phoneme patterns with validation rules
- **`skill_pattern_mapping.csv`** - Maps O-G skills to their relevant patterns

### Python Modules
- **`pattern_definitions.py`** - Pattern class definitions with phoneme validation logic
- **`pattern_registry.py`** - Pattern loading and management system

### Documentation
- **`PATTERN_DEFINITION_GUIDE.md`** - Comprehensive guide for defining and configuring patterns

## Key Improvements

1. **Phoneme-First Design**: Patterns defined by target sounds (using CMU phoneme codes)
2. **True Linguistic Validation**: Validates actual phonological properties
3. **Modular Pattern Classes**: Specialized validation for each pattern type
4. **Context-Aware Filtering**: Patterns can require specific phonological contexts
5. **Exclusion Rules**: Patterns can exclude conflicting patterns (e.g., Magic E overrides CVC)

## Usage

```python
from pattern_registry import get_registry

# Load the pattern registry
registry = get_registry()

# Get patterns for a skill
patterns = registry.get_patterns_for_skill('1.1')

# Validate a word against a pattern
is_valid = registry.validate_word('cat', 'SHORT_A_CVC', pronunciation, cmu_dict)
```

## Pattern Types

- **CVC**: True consonant-vowel-consonant validation
- **VowelTeam**: Phoneme-based vowel team verification
- **Blend**: Ensures all consonant sounds are pronounced
- **Digraph**: Two letters making one sound
- **RControlled**: R-controlled vowel patterns
- **MagicE**: Silent E patterns with long vowel verification
- **SpellingRule**: Orthographic patterns (c/k rules, doubling, etc.)
- **Syllabication**: Syllable division patterns

## Next Steps

To integrate with the existing system:
1. Update the main generator to use the pattern registry
2. Implement the enhanced filtering system
3. Add phoneme validation to word generation
4. Test against existing word lists

## Testing

```python
# Run the pattern registry test
python pattern_registry.py
```

This will display statistics about loaded patterns and test basic functionality.