#!/usr/bin/env python3
"""
Pattern Definitions for Phoneme-Based Word Generation

This module defines the base classes and specific pattern implementations
for the Orton-Gillingham word generator. Each pattern class validates
words based on phonological properties rather than just spelling patterns.
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Tuple, Optional, Set
import json
import re


class PatternDefinition(ABC):
    """Base class for all phonics patterns"""
    
    def __init__(self, pattern_id: str, config: Dict):
        self.pattern_id = pattern_id
        self.pattern_name = config.get('pattern_name', '')
        self.target_phoneme = config.get('target_phoneme', '')
        self.phoneme_variants = self._parse_phoneme_variants(config.get('phoneme_variants', ''))
        self.grapheme_mappings = self._parse_grapheme_mappings(config.get('grapheme_mappings', '{}'))
        self.validation_rules = self._parse_validation_rules(config.get('validation_rules', '[]'))
        self.position_constraints = config.get('position_constraints', 'any')
        self.context_requirements = config.get('context_requirements', '')
        self.excluded_patterns = self._parse_excluded_patterns(config.get('excluded_patterns', '[]'))
        self.frequency_threshold = int(config.get('frequency_threshold', 0))
        self.examples = [ex.strip() for ex in config.get('examples', '').split(',') if ex.strip()]
        self.non_examples = [ex.strip() for ex in config.get('non_examples', '').split(',') if ex.strip()]
    
    def _parse_phoneme_variants(self, variants_str: str) -> List[str]:
        """Parse phoneme variants from string"""
        if not variants_str:
            return []
        return [v.strip() for v in variants_str.split(',')]
    
    def _parse_grapheme_mappings(self, mappings_str: str) -> Dict[str, str]:
        """Parse grapheme mappings from JSON string"""
        try:
            return json.loads(mappings_str)
        except:
            return {}
    
    def _parse_validation_rules(self, rules_str: str) -> List[str]:
        """Parse validation rules from JSON string"""
        try:
            return json.loads(rules_str)
        except:
            return []
    
    def _parse_excluded_patterns(self, excluded_str: str) -> List[str]:
        """Parse excluded patterns from JSON string"""
        try:
            return json.loads(excluded_str)
        except:
            return []
    
    @abstractmethod
    def validate(self, word: str, pronunciation: List[str], cmu_dict: Dict) -> bool:
        """Check if word truly matches this pattern"""
        pass
    
    def find_graphemes(self, word: str) -> List[Tuple[str, int, str]]:
        """Find all instances of this pattern's graphemes in word
        Returns: List of (grapheme, position_index, position_type)
        """
        matches = []
        word_lower = word.lower()
        
        for grapheme, expected_position in self.grapheme_mappings.items():
            # Handle split patterns (like magic e)
            if expected_position == 'split' and '_' in grapheme:
                parts = grapheme.split('_')
                if len(parts) == 2 and parts[0] in word_lower and word_lower.endswith(parts[1]):
                    # Check if there's exactly one consonant between
                    vowel_pos = word_lower.find(parts[0])
                    if vowel_pos >= 0 and vowel_pos + 2 < len(word_lower):
                        if word_lower[vowel_pos + 2:].startswith(parts[1]):
                            matches.append((grapheme, vowel_pos, 'split'))
            else:
                # Regular grapheme search
                pos = word_lower.find(grapheme)
                while pos >= 0:
                    position_type = self._get_position_type(word_lower, grapheme, pos)
                    if self._matches_position_constraint(position_type, expected_position):
                        matches.append((grapheme, pos, position_type))
                    pos = word_lower.find(grapheme, pos + 1)
        
        return matches
    
    def _get_position_type(self, word: str, grapheme: str, position: int) -> str:
        """Determine position type of grapheme in word"""
        if position == 0:
            return 'initial'
        elif position + len(grapheme) == len(word):
            return 'final'
        else:
            return 'medial'
    
    def _matches_position_constraint(self, actual_position: str, expected_position: str) -> bool:
        """Check if actual position matches expected position"""
        if expected_position == 'any':
            return True
        return actual_position == expected_position
    
    def check_frequency(self, frequency_score: int) -> bool:
        """Check if word meets frequency threshold"""
        return frequency_score >= self.frequency_threshold
    
    def check_excluded_patterns(self, other_patterns: Set[str]) -> bool:
        """Check if any excluded patterns are present"""
        return not any(excluded in other_patterns for excluded in self.excluded_patterns)


class CVCPattern(PatternDefinition):
    """Pattern for Consonant-Vowel-Consonant words"""
    
    def validate(self, word: str, pronunciation: List[str], cmu_dict: Dict) -> bool:
        """Validate CVC pattern with correct vowel sound"""
        word_lower = word.lower()
        
        # Check basic CVC structure
        if len(word_lower) != 3:
            return False
        
        # Check consonant-vowel-consonant pattern
        if (word_lower[0] in 'aeiou' or 
            word_lower[1] not in 'aeiou' or 
            word_lower[2] in 'aeiou'):
            return False
        
        # Verify no blends or digraphs (if specified in rules)
        if 'no_blends' in self.validation_rules:
            # Check for common digraphs
            digraphs = ['ch', 'sh', 'th', 'wh', 'ck', 'ph', 'gh']
            if any(dg in word_lower for dg in digraphs):
                return False
        
        # Check phoneme if we have pronunciation
        if pronunciation and len(pronunciation) == 3:
            middle_phoneme = pronunciation[1].rstrip('0123456789')  # Remove stress markers
            if self.target_phoneme:
                return middle_phoneme == self.target_phoneme
        
        return True


class VowelTeamPattern(PatternDefinition):
    """Pattern for vowel teams (two vowels making one sound)"""
    
    def validate(self, word: str, pronunciation: List[str], cmu_dict: Dict) -> bool:
        """Validate vowel team makes expected sound"""
        word_lower = word.lower()
        
        # Find graphemes
        graphemes = self.find_graphemes(word_lower)
        if not graphemes:
            return False
        
        # Check pronunciation if available
        if pronunciation and self.target_phoneme:
            # Convert pronunciation to string for searching
            pron_str = ' '.join(pronunciation)
            # Check if target phoneme is present
            if self.target_phoneme not in pron_str:
                # Check variants
                if self.phoneme_variants:
                    return any(variant in pron_str for variant in self.phoneme_variants)
                return False
        
        return True


class BlendPattern(PatternDefinition):
    """Pattern for consonant blends"""
    
    def validate(self, word: str, pronunciation: List[str], cmu_dict: Dict) -> bool:
        """Validate that both/all consonant sounds are pronounced"""
        word_lower = word.lower()
        
        # Find blend graphemes
        graphemes = self.find_graphemes(word_lower)
        if not graphemes:
            return False
        
        # For blends, we need to verify both sounds are present
        if 'both_sounds_heard' in self.validation_rules and pronunciation:
            # This is a simplified check - in reality we'd need to verify
            # that both consonant phonemes appear in sequence
            return len(pronunciation) >= len(word_lower)
        
        return True


class DigraphPattern(PatternDefinition):
    """Pattern for digraphs (two letters, one sound)"""
    
    def validate(self, word: str, pronunciation: List[str], cmu_dict: Dict) -> bool:
        """Validate digraph makes single sound"""
        word_lower = word.lower()
        
        # Find digraph graphemes
        graphemes = self.find_graphemes(word_lower)
        if not graphemes:
            return False
        
        # Check for split digraphs (excluded)
        if 'exclude_split_digraphs' in self.validation_rules:
            for grapheme, pos, _ in graphemes:
                # Simple check - would need morpheme boundaries for full validation
                if grapheme == 'th' and word_lower == 'lighthouse':
                    return False
        
        # Verify position constraints
        if 'final_only' in self.validation_rules:
            for _, _, pos_type in graphemes:
                if pos_type != 'final':
                    return False
        
        return True


class RControlledPattern(PatternDefinition):
    """Pattern for r-controlled vowels"""
    
    def validate(self, word: str, pronunciation: List[str], cmu_dict: Dict) -> bool:
        """Validate r-controlled vowel pattern"""
        word_lower = word.lower()
        
        # Find r-controlled graphemes
        graphemes = self.find_graphemes(word_lower)
        if not graphemes:
            return False
        
        # Check for w-influenced changes
        if 'w_changes_sound' in self.validation_rules:
            # Verify word starts with 'w'
            if not word_lower.startswith('w'):
                return False
        
        # Verify pronunciation if available
        if pronunciation and self.target_phoneme:
            pron_str = ' '.join(pronunciation)
            return self.target_phoneme in pron_str
        
        return True


class MagicEPattern(PatternDefinition):
    """Pattern for silent E (VCe)"""
    
    def validate(self, word: str, pronunciation: List[str], cmu_dict: Dict) -> bool:
        """Validate magic E pattern"""
        word_lower = word.lower()
        
        # Must end with 'e'
        if not word_lower.endswith('e'):
            return False
        
        # Find split graphemes (a_e, i_e, etc.)
        graphemes = self.find_graphemes(word_lower)
        if not graphemes:
            return False
        
        # Check for exceptions
        if 'exclude_exceptions' in self.validation_rules:
            exceptions = ['have', 'give', 'come', 'some', 'love', 'done']
            if word_lower in exceptions:
                return False
        
        # Verify long vowel sound if pronunciation available
        if pronunciation and self.target_phoneme:
            pron_str = ' '.join(pronunciation)
            return self.target_phoneme in pron_str
        
        return True


class SpellingRulePattern(PatternDefinition):
    """Pattern for spelling rules (c/k rules, doubling, etc.)"""
    
    def validate(self, word: str, pronunciation: List[str], cmu_dict: Dict) -> bool:
        """Validate spelling rule pattern"""
        word_lower = word.lower()
        
        # Find rule graphemes
        graphemes = self.find_graphemes(word_lower)
        if not graphemes:
            return False
        
        # Check specific rule constraints
        if 'after_short_vowel' in self.validation_rules:
            # This would need full phoneme analysis
            # Simplified: check if preceded by single vowel
            for grapheme, pos, _ in graphemes:
                if pos > 0 and word_lower[pos-1] in 'aeiou':
                    # Check it's not a vowel team
                    if pos > 1 and word_lower[pos-2] in 'aeiou':
                        return False
        
        if 'one_syllable' in self.validation_rules:
            # Simplified syllable count
            vowel_count = sum(1 for c in word_lower if c in 'aeiou')
            if vowel_count > 2:  # Rough approximation
                return False
        
        return True


class SyllabicationPattern(PatternDefinition):
    """Pattern for syllable division"""
    
    def validate(self, word: str, pronunciation: List[str], cmu_dict: Dict) -> bool:
        """Validate syllabication pattern"""
        # Syllabication patterns are structural, not phonemic
        # They would need different validation logic
        return True


class SchwaPattern(PatternDefinition):
    """Pattern for schwa (unstressed vowel) sounds"""
    
    def validate(self, word: str, pronunciation: List[str], cmu_dict: Dict) -> bool:
        """Validate schwa pattern in unstressed syllables"""
        word_lower = word.lower()
        
        # Find potential schwa graphemes
        graphemes = self.find_graphemes(word_lower)
        if not graphemes:
            return False
        
        # Would need stress pattern analysis from CMU dict
        # Simplified check for now
        if pronunciation and self.target_phoneme == 'AH':
            pron_str = ' '.join(pronunciation)
            return 'AH0' in pron_str  # Unstressed schwa
        
        return True


# Pattern class mapping
PATTERN_CLASSES = {
    'CVC': CVCPattern,
    'VowelTeam': VowelTeamPattern,
    'Blend': BlendPattern,
    'FinalBlend': BlendPattern,
    'Digraph': DigraphPattern,
    'RControlled': RControlledPattern,
    'MagicE': MagicEPattern,
    'SpellingRule': SpellingRulePattern,
    'Syllabication': SyllabicationPattern,
    'Schwa': SchwaPattern,
    'OpenSyllable': PatternDefinition,  # Uses base class
    'YAsVowel': PatternDefinition,
    'SoftSound': PatternDefinition,
    'SilentLetter': PatternDefinition,
    'EDEnding': PatternDefinition,
    'Diphthong': VowelTeamPattern,  # Similar to vowel teams
}


def create_pattern(pattern_id: str, config: Dict) -> PatternDefinition:
    """Factory function to create pattern instances"""
    pattern_class_name = config.get('pattern_class', 'PatternDefinition')
    pattern_class = PATTERN_CLASSES.get(pattern_class_name, PatternDefinition)
    return pattern_class(pattern_id, config)