#!/usr/bin/env python3
"""
Pattern Registry for Phoneme-Based Word Generation

This module loads pattern definitions from CSV and provides a registry
for looking up patterns by ID or phoneme. It also manages the relationship
between skills and patterns.
"""

import pandas as pd
import os
from typing import Dict, List, Set, Optional, Tuple
from collections import defaultdict
import logging

from pattern_definitions import create_pattern, PatternDefinition


class PatternRegistry:
    """Registry for managing phoneme patterns and their relationships to skills"""
    
    def __init__(self, patterns_csv_path: str = 'phoneme_patterns_master.csv',
                 skills_csv_path: str = 'skill_pattern_mapping.csv'):
        """Initialize the pattern registry
        
        Args:
            patterns_csv_path: Path to the patterns CSV file
            skills_csv_path: Path to the skill-pattern mapping CSV
        """
        self.patterns: Dict[str, PatternDefinition] = {}
        self.patterns_by_phoneme: Dict[str, List[PatternDefinition]] = defaultdict(list)
        self.patterns_by_class: Dict[str, List[PatternDefinition]] = defaultdict(list)
        self.skill_patterns: Dict[str, List[str]] = defaultdict(list)
        self.pattern_skills: Dict[str, List[str]] = defaultdict(list)
        
        # Load patterns
        self._load_patterns(patterns_csv_path)
        
        # Load skill mappings
        self._load_skill_mappings(skills_csv_path)
        
        logging.info(f"Loaded {len(self.patterns)} patterns")
        logging.info(f"Loaded mappings for {len(self.skill_patterns)} skills")
    
    def _load_patterns(self, csv_path: str):
        """Load pattern definitions from CSV"""
        if not os.path.exists(csv_path):
            logging.error(f"Patterns CSV not found: {csv_path}")
            return
        
        df = pd.read_csv(csv_path)
        
        for _, row in df.iterrows():
            pattern_id = row['pattern_id']
            
            # Convert row to dict for pattern creation
            config = row.to_dict()
            
            # Create pattern instance
            pattern = create_pattern(pattern_id, config)
            
            # Store in registry
            self.patterns[pattern_id] = pattern
            
            # Index by phoneme
            if pattern.target_phoneme:
                self.patterns_by_phoneme[pattern.target_phoneme].append(pattern)
            
            # Index by class
            pattern_class = config.get('pattern_class', 'Unknown')
            self.patterns_by_class[pattern_class].append(pattern)
    
    def _load_skill_mappings(self, csv_path: str):
        """Load skill to pattern mappings from CSV"""
        if not os.path.exists(csv_path):
            logging.warning(f"Skill mappings CSV not found: {csv_path}")
            return
        
        df = pd.read_csv(csv_path)
        
        for _, row in df.iterrows():
            skill_id = str(row['skill_id'])
            pattern_ids_str = str(row.get('pattern_ids', ''))
            
            if pattern_ids_str and pattern_ids_str != 'nan':
                # Parse comma-separated pattern IDs
                pattern_ids = [pid.strip() for pid in pattern_ids_str.split(',') if pid.strip()]
                
                # Store bidirectional mappings
                self.skill_patterns[skill_id] = pattern_ids
                for pattern_id in pattern_ids:
                    self.pattern_skills[pattern_id].append(skill_id)
    
    def get_pattern(self, pattern_id: str) -> Optional[PatternDefinition]:
        """Get a pattern by ID"""
        return self.patterns.get(pattern_id)
    
    def get_patterns_for_skill(self, skill_id: str) -> List[PatternDefinition]:
        """Get all patterns associated with a skill"""
        pattern_ids = self.skill_patterns.get(skill_id, [])
        return [self.patterns[pid] for pid in pattern_ids if pid in self.patterns]
    
    def get_patterns_by_phoneme(self, phoneme: str) -> List[PatternDefinition]:
        """Get all patterns that produce a specific phoneme"""
        return self.patterns_by_phoneme.get(phoneme, [])
    
    def get_patterns_by_class(self, pattern_class: str) -> List[PatternDefinition]:
        """Get all patterns of a specific class"""
        return self.patterns_by_class.get(pattern_class, [])
    
    def validate_word(self, word: str, pattern_id: str, 
                     pronunciation: List[str], cmu_dict: Dict) -> bool:
        """Validate if a word matches a specific pattern
        
        Args:
            word: The word to validate
            pattern_id: The pattern to check against
            pronunciation: CMU pronunciation (list of phonemes)
            cmu_dict: CMU dictionary for lookups
            
        Returns:
            True if word matches pattern
        """
        pattern = self.get_pattern(pattern_id)
        if not pattern:
            return False
        
        return pattern.validate(word, pronunciation, cmu_dict)
    
    def find_matching_patterns(self, word: str, pronunciation: List[str], 
                             cmu_dict: Dict) -> List[Tuple[str, PatternDefinition]]:
        """Find all patterns that match a given word
        
        Args:
            word: The word to check
            pronunciation: CMU pronunciation
            cmu_dict: CMU dictionary
            
        Returns:
            List of (pattern_id, pattern) tuples
        """
        matches = []
        
        for pattern_id, pattern in self.patterns.items():
            if pattern.validate(word, pronunciation, cmu_dict):
                matches.append((pattern_id, pattern))
        
        return matches
    
    def get_prerequisite_patterns(self, skill_id: str, all_skills: Dict) -> Set[str]:
        """Get all patterns from prerequisite skills
        
        Args:
            skill_id: The skill to check prerequisites for
            all_skills: Dictionary of all skill data
            
        Returns:
            Set of pattern IDs from all prerequisite skills
        """
        prerequisite_patterns = set()
        
        # Get skill data
        skill_data = all_skills.get(skill_id, {})
        prerequisites = skill_data.get('prerequisite_skills', [])
        
        # Recursively collect patterns from prerequisites
        for prereq_id in prerequisites:
            # Add patterns from this prerequisite
            prereq_patterns = self.skill_patterns.get(prereq_id, [])
            prerequisite_patterns.update(prereq_patterns)
            
            # Recursively get patterns from prereq's prerequisites
            prerequisite_patterns.update(
                self.get_prerequisite_patterns(prereq_id, all_skills)
            )
        
        return prerequisite_patterns
    
    def filter_by_prerequisites(self, words: List[Dict], skill_id: str, 
                              all_skills: Dict) -> List[Dict]:
        """Filter words to only include those using allowed patterns
        
        Args:
            words: List of word dictionaries
            skill_id: Current skill being taught
            all_skills: Dictionary of all skill data
            
        Returns:
            Filtered list of words
        """
        # Get allowed patterns (current skill + prerequisites)
        current_patterns = set(self.skill_patterns.get(skill_id, []))
        prerequisite_patterns = self.get_prerequisite_patterns(skill_id, all_skills)
        allowed_patterns = current_patterns | prerequisite_patterns
        
        # Filter words
        filtered_words = []
        for word_data in words:
            word = word_data.get('word', '')
            
            # Check if word uses only allowed patterns
            # This would need full implementation with CMU dict
            # For now, include all words
            filtered_words.append(word_data)
        
        return filtered_words
    
    def get_pattern_statistics(self) -> Dict:
        """Get statistics about loaded patterns"""
        stats = {
            'total_patterns': len(self.patterns),
            'patterns_by_class': {},
            'patterns_by_phoneme': {},
            'unmapped_patterns': [],
            'skills_without_patterns': []
        }
        
        # Count by class
        for pattern_class, patterns in self.patterns_by_class.items():
            stats['patterns_by_class'][pattern_class] = len(patterns)
        
        # Count by phoneme
        for phoneme, patterns in self.patterns_by_phoneme.items():
            stats['patterns_by_phoneme'][phoneme] = len(patterns)
        
        # Find unmapped patterns
        for pattern_id in self.patterns:
            if pattern_id not in self.pattern_skills:
                stats['unmapped_patterns'].append(pattern_id)
        
        # Find skills without patterns
        # This would need the full skills list
        
        return stats


# Singleton instance
_registry_instance = None


def get_registry(patterns_csv: str = 'phoneme_patterns_master.csv',
                skills_csv: str = 'skill_pattern_mapping.csv') -> PatternRegistry:
    """Get or create the singleton pattern registry"""
    global _registry_instance
    
    if _registry_instance is None:
        _registry_instance = PatternRegistry(patterns_csv, skills_csv)
    
    return _registry_instance


if __name__ == "__main__":
    # Test the registry
    import logging
    logging.basicConfig(level=logging.INFO)
    
    registry = get_registry()
    
    # Print statistics
    stats = registry.get_pattern_statistics()
    print("\nPattern Registry Statistics:")
    print(f"Total patterns: {stats['total_patterns']}")
    print("\nPatterns by class:")
    for pattern_class, count in stats['patterns_by_class'].items():
        print(f"  {pattern_class}: {count}")
    
    # Test pattern lookup
    print("\nTesting pattern lookup:")
    test_pattern = registry.get_pattern('SHORT_A_CVC')
    if test_pattern:
        print(f"Found pattern: {test_pattern.pattern_name}")
        print(f"Target phoneme: {test_pattern.target_phoneme}")
        print(f"Examples: {test_pattern.examples}")
    
    # Test skill mapping
    print("\nTesting skill mapping:")
    skill_patterns = registry.get_patterns_for_skill('1.1')
    print(f"Patterns for skill 1.1: {[p.pattern_id for p in skill_patterns]}")