pattern_id,pattern_name,validation_rules_combo,rule_parameters,expected_matches,expected_non_matches,notes
SHORT_A_CVC,Short A in CVC,"[word_length_exact, position_1_equals, consonant_vowel_consonant, vowel_sound_is, no_blends, no_digraphs, syllable_count]","{'word_length_exact': {'length': 3}, 'position_1_equals': {'value': 'a'}, 'consonant_vowel_consonant': {}, 'vowel_sound_is': {'phoneme': 'AE'}, 'no_blends': {}, 'no_digraphs': {}, 'syllable_count': {'count': 1}}","cat, hat, mat, sat, bat","cake, rain, chat, car, at",strict CVC with short A sound
SHORT_E_CVC,Short E in CVC,"[word_length_exact, position_1_equals, consonant_vowel_consonant, vowel_sound_is, no_blends, no_digraphs, syllable_count]","{'word_length_exact': {'length': 3}, 'position_1_equals': {'value': 'e'}, 'consonant_vowel_consonant': {}, 'vowel_sound_is': {'phoneme': 'EH'}, 'no_blends': {}, 'no_digraphs': {}, 'syllable_count': {'count': 1}}","bed, get, met, set, wet","keep, shed, best, her, be",strict CVC with short E sound
SHORT_I_CVC,Short I in CVC,"[word_length_exact, position_1_equals, consonant_vowel_consonant, vowel_sound_is, no_blends, no_digraphs, syllable_count]","{'word_length_exact': {'length': 3}, 'position_1_equals': {'value': 'i'}, 'consonant_vowel_consonant': {}, 'vowel_sound_is': {'phoneme': 'IH'}, 'no_blends': {}, 'no_digraphs': {}, 'syllable_count': {'count': 1}}","sit, hit, bit, fit, pit","kite, rain, ship, bird, it",strict CVC with short I sound
SHORT_O_CVC,Short O in CVC,"[word_length_exact, position_1_equals, consonant_vowel_consonant, vowel_sound_is, no_blends, no_digraphs, syllable_count]","{'word_length_exact': {'length': 3}, 'position_1_equals': {'value': 'o'}, 'consonant_vowel_consonant': {}, 'vowel_sound_is': {'phoneme': 'AA'}, 'no_blends': {}, 'no_digraphs': {}, 'syllable_count': {'count': 1}}","hot, pot, dot, got, lot","boat, shop, stop, for, go",strict CVC with short O sound
SHORT_U_CVC,Short U in CVC,"[word_length_exact, position_1_equals, consonant_vowel_consonant, vowel_sound_is, no_blends, no_digraphs, syllable_count]","{'word_length_exact': {'length': 3}, 'position_1_equals': {'value': 'u'}, 'consonant_vowel_consonant': {}, 'vowel_sound_is': {'phoneme': 'AH'}, 'no_blends': {}, 'no_digraphs': {}, 'syllable_count': {'count': 1}}","cup, cut, but, nut, hut","cute, out, shut, turn, up",strict CVC with short U sound
LONG_E_CV,Long E Open Syllable,"[word_length_range, ends_with_grapheme, position_n_equals, vowel_sound_is, specific_words_only]","{'word_length_range': {'min': 2, 'max': 3}, 'ends_with_grapheme': {'grapheme': 'e'}, 'position_n_equals': {'position': -1, 'value': 'e'}, 'vowel_sound_is': {'phoneme': 'IY'}, 'specific_words_only': {'words': ['be','he','me','we','she']}}","be, he, me, we, she","bee, sea, bed, the, are",specific open syllable words with long E
LONG_O_CV,Long O Open Syllable,"[word_length_exact, ends_with_grapheme, position_n_equals, vowel_sound_is, specific_words_only]","{'word_length_exact': {'length': 2}, 'ends_with_grapheme': {'grapheme': 'o'}, 'position_n_equals': {'position': -1, 'value': 'o'}, 'vowel_sound_is': {'phoneme': 'OW'}, 'specific_words_only': {'words': ['go','no','so']}}","go, no, so","toe, row, got, to, do",specific CV words with long O
LONG_I_CV,Long I Open Syllable,"[word_length_range, ends_with_grapheme, vowel_sound_is, specific_words_only]","{'word_length_range': {'min': 1, 'max': 2}, 'ends_with_grapheme': {'grapheme': 'i'}, 'vowel_sound_is': {'phoneme': 'AY'}, 'specific_words_only': {'words': ['I','hi']}}","I, hi","pie, my, it, is, in",specific open syllable I words
C_BEFORE_A_O_U,Hard C before a/o/u,"[position_0_equals, position_1_in_set, initial_phoneme_is]","{'position_0_equals': {'letter': 'c'}, 'position_1_in_set': {'letters': ['a','o','u']}, 'initial_phoneme_is': {'phoneme': 'K'}}","cat, cot, cup, can, come","city, cent, chair, chat","initial /k/ sound is spelled with a c when the second letter is a, o or u"
K_BEFORE_E_I,K before e/i,"[position_0_equals, position_1_in_set, initial_phoneme_is]","{'position_0_equals': {'letter': 'k'}, 'position_1_in_set': {'letters': ['e','i','y']}, 'initial_phoneme_is': {'phoneme': 'K'}}","kit, key, kid, kite, keep","city, cent, knee, know","initial /k/ sound is spelled with a k when the second letter is e, i, or y"
CH_DIGRAPH,CH Digraph,"[contains_grapheme, verify_sound, exclude_exceptions]","{'contains_grapheme': {'grapheme': 'ch'}, 'verify_sound': {'sound': '/ch/'}, 'exclude_exceptions': {'exceptions': ['character','chef','school','chaos','chrome']}}","chip, chat, much, lunch, rich","character, chef, school",ch making /ch/ sound
SH_DIGRAPH,SH Digraph,"[contains_grapheme, verify_sound]","{'contains_grapheme': {'grapheme': 'sh'}, 'verify_sound': {'sound': '/sh/'}}","ship, shop, wish, cash, she","mishap, grasshopper",sh digraph as one sound
TH_DIGRAPH,TH Digraph,"[contains_grapheme, verify_sound]","{'contains_grapheme': {'grapheme': 'th'}, 'verify_sound': {'sound': '/th/ or /ð/'}}","this, that, thin, bath, with","lighthouse, boathouse",th digraph voiced or unvoiced
WH_DIGRAPH,WH Digraph,"[starts_with_grapheme, position_0_equals, position_1_equals, initial_phoneme_is]","{'starts_with_grapheme': {'grapheme': 'wh'}, 'position_0_equals': {'letter': 'w'}, 'position_1_equals': {'value': 'h'}, 'initial_phoneme_is': {'phoneme': 'W'}}","when, what, where, why, which","nowhere, somewhat",wh digraph at word start
CK_DIGRAPH,CK Digraph,"[ends_with_grapheme, after_short_vowel, one_syllable_only, preceded_by]","{'ends_with_grapheme': {'grapheme': 'ck'}, 'after_short_vowel': {}, 'one_syllable_only': {}, 'preceded_by': {'grapheme': 'ck', 'preceding': ['a','e','i','o','u']}}","back, pack, sick, lock, duck","bark, make, cracker",ck after short vowel in 1 syllable
L_BLENDS_INITIAL,Initial L-Blends,"[starts_with_grapheme, true_blend, initial_phoneme_is, position_0_in_set, position_1_equals]","{'starts_with_grapheme': {'grapheme': ['bl','cl','fl','gl','pl','sl']}, 'true_blend': {}, 'initial_phoneme_is': {'phoneme': 'CONSONANT+L'}, 'position_0_in_set': {'letters': ['b','c','f','g','p','s']}, 'position_1_equals': {'value': 'l'}}","black, clap, flag, glad, play, slow","help, below, old, felt",initial L-blends only
R_BLENDS_INITIAL,Initial R-Blends,"[starts_with_grapheme, true_blend, initial_phoneme_is, position_0_in_set, position_1_equals]","{'starts_with_grapheme': {'grapheme': ['br','cr','dr','fr','gr','pr','tr']}, 'true_blend': {}, 'initial_phoneme_is': {'phoneme': 'CONSONANT+R'}, 'position_0_in_set': {'letters': ['b','c','d','f','g','p','t']}, 'position_1_equals': {'value': 'r'}}","bring, crab, drip, from, grab, print, trip","car, forth, write, wrap",initial R-blends only
S_BLENDS_INITIAL,Initial S-Blends,"[starts_with_grapheme, true_blend, position_0_equals, position_1_in_set]","{'starts_with_grapheme': {'grapheme': ['sc','sk','sm','sn','sp','st','sw']}, 'true_blend': {}, 'position_0_equals': {'letter': 's'}, 'position_1_in_set': {'letters': ['c','k','m','n','p','t','w']}}","scan, skip, smell, snap, spot, stop, swim","fast, rest, she, ship",initial S-blends only
THREE_LETTER_BLENDS,3-Letter Blends,"[starts_with_grapheme, true_blend]","{'starts_with_grapheme': {'grapheme': ['scr','spr','str','spl','squ','thr']}, 'true_blend': {}}","scrap, spring, string, split, square, three","first, inst, shrimp",3-consonant blends at start
FINAL_NT_ST_BLENDS,Final nt/st/sk Blends,"[ends_with_grapheme, true_blend, position_n_in_set, position_n_equals]","{'ends_with_grapheme': {'grapheme': ['nt','st','sk','ct','pt','xt']}, 'true_blend': {}, 'position_n_in_set': {'position': -2, 'letters': ['n','s','s','c','p','x']}, 'position_n_equals': {'position': -1, 'value': 't'}}","ant, went, best, desk, act, next","and, send, test",final blends with t/k
FINAL_MP_FT_BLENDS,Final mp/ft/lf/sp Blends,"[ends_with_grapheme, true_blend, final_phoneme_is]","{'ends_with_grapheme': {'grapheme': ['mp','ft','lf','sp']}, 'true_blend': {}, 'final_phoneme_is': {'phoneme': ['P','T','F','P']}}","camp, lift, self, wasp","cam, life, was",final blends with p/t/f
FINAL_ND_LM_BLENDS,Final nd/lm/ld Blends,"[ends_with_grapheme, true_blend, position_n_equals]","{'ends_with_grapheme': {'grapheme': ['nd','lm','ld']}, 'true_blend': {}, 'position_n_equals': {'position': -1, 'value': ['d','m','d']}}","hand, film, cold","han, fill, coal",final blends with d/m
FLOSS_RULE,FLOSS Doubling Rule,"[ends_with_grapheme, position_n_equals, after_short_vowel, one_syllable_only]","{'ends_with_grapheme': {'grapheme': ['ff','ll','ss','zz']}, 'position_n_equals': {'position': -2, 'value': ['f','l','s','z']}, 'after_short_vowel': {}, 'one_syllable_only': {}}","stuff, bell, pass, buzz, hill","staff, help, bus, has",f/l/s/z doubled after short vowel
MAGIC_E_A,Magic E with A,"[position_n_equals, contains_grapheme, vowel_sound_is, between_patterns, exclude_exceptions]","{'position_n_equals': {'position': -1, 'value': 'e'}, 'contains_grapheme': {'grapheme': 'a'}, 'vowel_sound_is': {'phoneme': 'EY'}, 'between_patterns': {'before': 'a', 'after': 'e', 'grapheme': 'CONSONANT'}, 'exclude_exceptions': {'exceptions': ['have']}}","make, cake, late, gate, name","have, mat, can",a_e pattern with long A
MAGIC_E_I,Magic E with I,"[position_n_equals, contains_grapheme, vowel_sound_is, between_patterns, exclude_exceptions]","{'position_n_equals': {'position': -1, 'value': 'e'}, 'contains_grapheme': {'grapheme': 'i'}, 'vowel_sound_is': {'phoneme': 'AY'}, 'between_patterns': {'before': 'i', 'after': 'e', 'grapheme': 'CONSONANT'}, 'exclude_exceptions': {'exceptions': ['give','live']}}","bike, like, time, kite, hide","give, live, sit, bid",i_e pattern with long I
MAGIC_E_O,Magic E with O,"[position_n_equals, contains_grapheme, vowel_sound_is, between_patterns, exclude_exceptions]","{'position_n_equals': {'position': -1, 'value': 'e'}, 'contains_grapheme': {'grapheme': 'o'}, 'vowel_sound_is': {'phoneme': 'OW'}, 'between_patterns': {'before': 'o', 'after': 'e', 'grapheme': 'CONSONANT'}, 'exclude_exceptions': {'exceptions': ['come','some','love','done']}}","hope, home, note, rope, bone","come, some, love, hot",o_e pattern with long O
MAGIC_E_U,Magic E with U,"[position_n_equals, contains_grapheme, vowel_sound_is, between_patterns]","{'position_n_equals': {'position': -1, 'value': 'e'}, 'contains_grapheme': {'grapheme': 'u'}, 'vowel_sound_is': {'phoneme': 'YUW'}, 'between_patterns': {'before': 'u', 'after': 'e', 'grapheme': 'CONSONANT'}}","cute, cube, mute, huge, use","cut, but, cup",u_e pattern with long U
LONG_A_TEAMS,Vowel Teams - Long A,"[contains_grapheme, vowel_sound_is, exclude_exceptions]","{'contains_grapheme': {'grapheme': ['ai','ay','ey','eigh','ea','ei']}, 'vowel_sound_is': {'phoneme': 'EY'}, 'exclude_exceptions': {'exceptions': ['said','again','fair']}}","rain, train, plain, play, grey, sleigh, beige","said, again, fair",multiple graphemes making long A
LONG_E_TEAMS,Vowel Teams - Long E,"[contains_grapheme, vowel_sound_is]","{'contains_grapheme': {'grapheme': ['ee', 'ea', 'ie', 'ey', 'ei']}, 'vowel_sound_is': {'phoneme': 'IY'}}","need, see, tree, eat, read, beach, clean, team, eat, read, beach, clean, team","bread, great, bear",multiple graphemes making long E
LONG_I_TEAMS,Vowel Teams - Long I,"[contains_grapheme, vowel_sound_is]","{'contains_grapheme': {'grapheme': ['ie','igh','ei','uy']}, 'vowel_sound_is': {'phoneme': 'AY'}}","pie, heist, guy, light, tie, lie",,multiple graphemes making long I
LONG_O_TEAMS,Vowel Teams - Long O,"[contains_grapheme, vowel_sound_is]","{'contains_grapheme': {'grapheme': ['oa','oe','ow','ou']}, 'vowel_sound_is': {'phoneme': 'OW'}}","boat, toe, snow, soul, coat, foe",,multiple graphemes making long O
LONG_U_TEAMS,Vowel Teams - Long U,"[contains_grapheme, vowel_sound_is]","{'contains_grapheme': {'grapheme': ['ue','ew','eu']}, 'vowel_sound_is': {'phoneme': 'YUW'}}","argue, few, feud, view","cow, now, how",multiple graphemes making long U
OW_DIPHTHONG,OW as in COW,"[contains_grapheme, vowel_sound_is, verify_sound]","{'contains_grapheme': {'grapheme': ['ow','ou']}, 'vowel_sound_is': {'phoneme': 'AW'}, 'verify_sound': {'sound': '/aʊ/'}}","cow, now, out, house, about","low, soul, show",ow/ou making diphthong
OY_DIPHTHONG,OY as in BOY,"[contains_grapheme, vowel_sound_is, grapheme_at_position]","{'contains_grapheme': {'grapheme': ['oi','oy']}, 'vowel_sound_is': {'phoneme': 'OY'}, 'grapheme_at_position': {'position': 'medial_or_final', 'grapheme': ['oi','oy']}}","oil, coin, boy, toy, voice","going, doing",oi/oy diphthong pattern
AW_DIPHTHONG,AW as in SAW,"[contains_grapheme, vowel_sound_is, exclude_exceptions]","{'contains_grapheme': {'grapheme': ['au','aw']}, 'vowel_sound_is': {'phoneme': 'AO'}, 'exclude_exceptions': {'exceptions': ['laugh','aunt']}}","saw, law, haul, cause, draw","laugh, aunt",au/aw making /ɔ/ sound
SHORT_E_TEAMS,Short E with EA,"[contains_grapheme, vowel_sound_is, exclude_pattern]","{'contains_grapheme': {'grapheme': 'ea'}, 'vowel_sound_is': {'phoneme': 'EH'}, 'exclude_pattern': {'pattern': 'LONG_E_EA'}}","bread, head, dead, spread, thread","eat, team, beach",ea making short E sound
LONG_OO,Long OO as in MOON,"[contains_grapheme, vowel_sound_is, exclude_pattern]","{'contains_grapheme': {'grapheme': ['oo','ue','ew','ui','eu','ou']}, 'vowel_sound_is': {'phoneme': 'UW'}, 'exclude_pattern': {'pattern': 'SHORT_OO'}}","moon, food, tooth, spoon, cool, blue, blew, suit, soup","book, good, foot",oo making long /u/ sound
SHORT_OO,Short OO as in BOOK,"[contains_grapheme, vowel_sound_is, exclude_pattern]","{'contains_grapheme': {'grapheme': 'oo'}, 'vowel_sound_is': {'phoneme': 'UH'}, 'exclude_pattern': {'pattern': 'LONG_OO'}}","book, look, good, wood, foot","moon, food, cool",oo making short /ʊ/ sound
R_CONTROLLED_ER,ER Sound,"[contains_grapheme, vowel_sound_is, contains_phoneme]","{'contains_grapheme': {'grapheme': ['er','ir','ur']}, 'vowel_sound_is': {'phoneme': 'ER'}, 'contains_phoneme': {'phoneme': 'ER'}}","her, bird, turn, fern, shirt","here, fire, pure",er/ir/ur making /ɜr/ sound
R_CONTROLLED_OR,OR Sound,"[contains_grapheme, vowel_sound_is, contains_phoneme, exclude_pattern]","{'contains_grapheme': {'grapheme': 'or'}, 'vowel_sound_is': {'phoneme': 'AO R'}, 'contains_phoneme': {'phoneme': 'AO R'}, 'exclude_pattern': {'pattern': 'W_CHANGES_OR'}}","for, corn, sport, north, fork","work, word, worth",or making /ɔr/ sound
R_CONTROLLED_AR,AR Sound,"[contains_grapheme, vowel_sound_is, contains_phoneme, exclude_pattern]","{'contains_grapheme': {'grapheme': 'ar'}, 'vowel_sound_is': {'phoneme': 'AA R'}, 'contains_phoneme': {'phoneme': 'AA R'}, 'exclude_pattern': {'pattern': 'W_CHANGES_AR'}}","car, star, park, hard, farm","war, warm, award",ar making /ɑr/ sound
W_CHANGES_AR,W+AR = OR Sound,"[starts_with_grapheme, contains_grapheme, vowel_sound_is]","{'starts_with_grapheme': {'grapheme': 'w'}, 'contains_grapheme': {'grapheme': 'ar'}, 'vowel_sound_is': {'phoneme': 'AO R'}}","war, warm, warn, wart, ward","car, far, bar",w changes ar to /ɔr/ sound
W_CHANGES_OR,W+OR = ER Sound,"[starts_with_grapheme, contains_grapheme, vowel_sound_is]","{'starts_with_grapheme': {'grapheme': 'w'}, 'contains_grapheme': {'grapheme': 'or'}, 'vowel_sound_is': {'phoneme': 'ER'}}","work, word, world, worm, worth","for, corn, born",w changes or to /ɜr/ sound
Y_AS_LONG_I,Y as Long I,"[ends_with_grapheme, vowel_sound_is, one_syllable_only, position_n_equals]","{'ends_with_grapheme': {'grapheme': 'y'}, 'vowel_sound_is': {'phoneme': 'AY'}, 'one_syllable_only': {}, 'position_n_equals': {'position': -1, 'value': 'y'}}","cry, fly, by, try, sky","happy, baby, very",y as long I in 1-syllable words
Y_AS_LONG_E,Y as Long E,"[ends_with_grapheme, vowel_sound_is, syllable_count, position_n_equals]","{'ends_with_grapheme': {'grapheme': 'y'}, 'vowel_sound_is': {'phoneme': 'IY'}, 'syllable_count': {'count': 2}, 'position_n_equals': {'position': -1, 'value': 'y'}}","baby, candy, happy, silly, tiny","cry, by, gym",y as long E in 2+ syllable words
Y_AS_SHORT_I,Y as Short I,"[contains_grapheme, vowel_sound_is, not_ends_with_grapheme]","{'contains_grapheme': {'grapheme': 'y'}, 'vowel_sound_is': {'phoneme': 'IH'}, 'not_ends_with_grapheme': {'grapheme': 'y'}}","gym, myth, cyst, symbol, system","cry, happy, yes",y as short I medially
SOFT_G_INITIAL,Initial Soft G,"[starts_with_grapheme, grapheme_followed_by, initial_phoneme_is, verify_sound]","{'starts_with_grapheme': {'grapheme': 'g'}, 'grapheme_followed_by': {'grapheme': 'g', 'following': ['e','i','y']}, 'initial_phoneme_is': {'phoneme': 'JH'}, 'verify_sound': {'sound': '/dʒ/'}}","giant, gym, gentle, giraffe, gel","get, give, go, game",g making /j/ sound initially
SOFT_C_INITIAL,Initial Soft C,"[starts_with_grapheme, grapheme_followed_by, initial_phoneme_is, verify_sound]","{'starts_with_grapheme': {'grapheme': 'c'}, 'grapheme_followed_by': {'grapheme': 'c', 'following': ['e','i','y']}, 'initial_phoneme_is': {'phoneme': 'S'}, 'verify_sound': {'sound': '/s/'}}","city, cent, cycle, cell, cinema","cat, come, cup, can",c making /s/ sound initially
SOFT_G_FINAL,Final Soft G,"[ends_with_grapheme, final_phoneme_is, after_short_vowel]","{'ends_with_grapheme': {'grapheme': 'ge'}, 'final_phoneme_is': {'phoneme': 'JH'}, 'after_short_vowel': {}}","cage, age, huge, stage, rage","dog, big, leg",ge making /j/ sound finally
SILENT_KN,Silent K in KN,"[starts_with_grapheme, initial_phoneme_is, silent_letter]","{'starts_with_grapheme': {'grapheme': 'kn'}, 'initial_phoneme_is': {'phoneme': 'N'}, 'silent_letter': {'letter': 'k', 'position': 0}}","know, knee, knife, knit, knot","keep, kind, note",k silent before n
SILENT_WR,Silent W in WR,"[starts_with_grapheme, initial_phoneme_is, silent_letter]","{'starts_with_grapheme': {'grapheme': 'wr'}, 'initial_phoneme_is': {'phoneme': 'R'}, 'silent_letter': {'letter': 'w', 'position': 0}}","write, wrong, wrap, wrist, wreck","wait, work, rest",w silent before r
SILENT_MB,Silent B in MB,"[ends_with_grapheme, final_phoneme_is, silent_letter]","{'ends_with_grapheme': {'grapheme': 'mb'}, 'final_phoneme_is': {'phoneme': 'M'}, 'silent_letter': {'letter': 'b', 'position': -1}}","lamb, thumb, climb, comb, bomb","bump, member, amber",b silent after m finally
ED_AS_T,ED pronounced /t/,"[ends_with_grapheme, final_phoneme_is, preceded_by]","{'ends_with_grapheme': {'grapheme': 'ed'}, 'final_phoneme_is': {'phoneme': 'T'}, 'preceded_by': {'grapheme': 'ed', 'preceding': ['p','k','f','s','sh','ch']}}","jumped, walked, helped, looked, pushed","played, called, ended",ed as /t/ after unvoiced
ED_AS_D,ED pronounced /d/,"[ends_with_grapheme, final_phoneme_is, preceded_by]","{'ends_with_grapheme': {'grapheme': 'ed'}, 'final_phoneme_is': {'phoneme': 'D'}, 'preceded_by': {'grapheme': 'ed', 'preceding': ['b','g','v','z','m','n','l','r']}}","played, called, loved, pulled, turned","jumped, wanted, ended",ed as /d/ after voiced
ED_AS_ED,ED pronounced /ed/,"[ends_with_grapheme, final_phoneme_is, preceded_by, syllable_count]","{'ends_with_grapheme': {'grapheme': 'ed'}, 'final_phoneme_is': {'phoneme': 'EH D'}, 'preceded_by': {'grapheme': 'ed', 'preceding': ['t','d']}, 'syllable_count': {'count': '+1'}}","planted, needed, wanted, started, ended","played, jumped, called",ed as /ɪd/ after t/d
TCH_CATCH_RULE,TCH after short vowel,"[ends_with_grapheme, after_short_vowel, one_syllable_only, not_preceded_by]","{'ends_with_grapheme': {'grapheme': 'tch'}, 'after_short_vowel': {}, 'one_syllable_only': {}, 'not_preceded_by': {'grapheme': 'tch', 'excluded': ['r','l','n']}}","catch, match, pitch, notch, watch","much, such, which, bench",tch after short vowel except after r/l/n
DGE_BRIDGE_RULE,DGE after short vowel,"[ends_with_grapheme, after_short_vowel, one_syllable_only, verify_sound]","{'ends_with_grapheme': {'grapheme': 'dge'}, 'after_short_vowel': {}, 'one_syllable_only': {}, 'verify_sound': {'sound': '/dʒ/'}}","badge, edge, bridge, lodge, fudge","age, huge, large",dge after short vowel rule
SCHWA_O,Schwa O Sound,"[contains_grapheme, vowel_sound_is, syllable_has_schwa]","{'contains_grapheme': {'grapheme': 'o'}, 'vowel_sound_is': {'phoneme': 'AH'}, 'syllable_has_schwa': {'syllable': 'unstressed'}}","come, love, some, done, other","go, hot, home",o making schwa in unstressed
SCHWA_A,Schwa A Sound,"[contains_grapheme, vowel_sound_is, syllable_has_schwa]","{'contains_grapheme': {'grapheme': 'a'}, 'vowel_sound_is': {'phoneme': 'AH'}, 'syllable_has_schwa': {'syllable': 'unstressed'}}","about, ago, banana, sofa, around","cat, make, day",a making schwa in unstressed
SCHWA_E,Schwa E Sound,"[contains_grapheme, vowel_sound_is, syllable_has_schwa]","{'contains_grapheme': {'grapheme': 'e'}, 'vowel_sound_is': {'phoneme': 'AH'}, 'syllable_has_schwa': {'syllable': 'unstressed'}}","camel, item",,e making schwa in unstressed
COMPOUND_WORDS,Compound Words,"[compound_boundary_at, syllable_count, memorize_pattern]","{'compound_boundary_at': {'position': 'varies'}, 'syllable_count': {'count': '2+'}, 'memorize_pattern': {}}","backpack, homework, playground, cupcake, sunshine","packing, working, playing",two complete words joined
PREFIX_SUFFIX,Prefix/Suffix Division,"[morpheme_boundary_at, syllable_pattern, contains_grapheme]","{'morpheme_boundary_at': {'position': 'varies', 'type': 'affix'}, 'syllable_pattern': {'pattern': 'PREFIX+BASE' or 'BASE+SUFFIX'}, 'contains_grapheme': {'grapheme': ['un','re','ing','er','ed']}}","un/tie, re/turn, jump/ing, tall/er, play/ed","unite, return, jumping",affix boundaries kept intact
VCCV_PATTERN,VCCV Syllable Division,"[syllable_pattern, syllable_boundary_at, contains_grapheme]","{'syllable_pattern': {'pattern': 'VCCV'}, 'syllable_boundary_at': {'position': 'between_consonants', 'type': 'VC/CV'}, 'contains_grapheme': {'grapheme': 'VCCV'}}","rab/bit, nap/kin, win/ter, hap/pen, bas/ket","about, open, item",divide between consonants
VCV_OPEN,VCV Open Syllable,"[syllable_pattern, vcv_division_type, syllable_type_is, vowel_in_syllable_n]","{'syllable_pattern': {'pattern': 'VCV'}, 'vcv_division_type': {'type': 'open'}, 'syllable_type_is': {'syllable': 1, 'type': 'open'}, 'vowel_in_syllable_n': {'syllable': 1, 'phoneme': 'LONG'}}","ti/ger, pa/per, mu/sic, ro/bot, ba/by","cam/el, nev/er, lim/it",V/CV with long vowel
VCV_CLOSED,VCV Closed Syllable,"[syllable_pattern, vcv_division_type, syllable_type_is, vowel_in_syllable_n]","{'syllable_pattern': {'pattern': 'VCV'}, 'vcv_division_type': {'type': 'closed'}, 'syllable_type_is': {'syllable': 1, 'type': 'closed'}, 'vowel_in_syllable_n': {'syllable': 1, 'phoneme': 'SHORT'}}","cam/el, nev/er, lim/it, vis/it, sev/en","ti/ger, pa/per, mu/sic",VC/V with short vowel
CLE_SYLLABLE,Consonant+LE Syllable,"[ends_with_grapheme, cle_pattern, syllable_boundary_at, syllable_count]","{'ends_with_grapheme': {'grapheme': 'le'}, 'cle_pattern': {}, 'syllable_boundary_at': {'position': -3, 'type': 'C/LE'}, 'syllable_count': {'count': '2+'}}","tur/tle, ap/ple, ta/ble, sim/ple, puz/zle","smile, while, mile",C+LE as final syllable