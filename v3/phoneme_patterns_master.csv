pattern_id,pattern_name,target_phoneme,phoneme_variants,grapheme_mappings,pattern_class,validation_rules,position_constraints,context_requirements,excluded_patterns,frequency_threshold,examples,non_examples
SHORT_A_CVC,Short A in CVC,AE,,"{""a"": ""medial""}",CVC,"[""word_length_exact:3"", ""consonant_vowel_consonant"", ""no_blends"", ""no_digraphs"", ""single_syllable""]",medial,,"[""MAGIC_E"", ""VOWEL_TEAM"", ""R_CONTROLLED""]",10,"cat, hat, mat, sat, bat","cake, rain, chat, car"
SHORT_E_CVC,Short E in CVC,EH,,"{""e"": ""medial""}",CVC,"[""word_length_exact:3"", ""consonant_vowel_consonant"", ""no_blends"", ""no_digraphs"", ""single_syllable""]",medial,,"[""MAGIC_E"", ""VOWEL_TEAM"", ""R_CONTROLLED""]",10,"bed, get, met, set, wet","keep, shed, best, her"
SHORT_I_CVC,Short I in CVC,IH,,"{""i"": ""medial""}",CVC,"[""word_length_exact:3"", ""consonant_vowel_consonant"", ""no_blends"", ""no_digraphs"", ""single_syllable""]",medial,,"[""MAGIC_E"", ""VOWEL_TEAM"", ""R_CONTROLLED""]",10,"sit, hit, bit, fit, pit","kite, rain, ship, bird"
SHORT_O_CVC,Short O in CVC,AA,,"{""o"": ""medial""}",CVC,"[""word_length_exact:3"", ""consonant_vowel_consonant"", ""no_blends"", ""no_digraphs"", ""single_syllable""]",medial,,"[""MAGIC_E"", ""VOWEL_TEAM"", ""R_CONTROLLED""]",10,"hot, pot, dot, got, lot","boat, shop, stop, for"
SHORT_U_CVC,Short U in CVC,AH,,"{""u"": ""medial""}",CVC,"[""word_length_exact:3"", ""consonant_vowel_consonant"", ""no_blends"", ""no_digraphs"", ""single_syllable""]",medial,,"[""MAGIC_E"", ""VOWEL_TEAM"", ""R_CONTROLLED""]",10,"cup, cut, but, nut, hut","cute, out, shut, turn"
LONG_E_CV,Long E Open Syllable,IY,,"{""e"": ""final""}",OpenSyllable,"[""word_length_range:2-3"", ""consonant_vowel"", ""specific_words_only""]",final,,"[""VOWEL_TEAM"", ""MAGIC_E""]",100,"be, he, me, we, she","bee, sea, bed"
LONG_O_CV,Long O Open Syllable,OW,,"{""o"": ""final""}",OpenSyllable,"[""word_length_exact:2"", ""consonant_vowel"", ""specific_words_only""]",final,,"[""VOWEL_TEAM"", ""MAGIC_E""]",100,"go, no, so","toe, row, got"
LONG_I_CV,Long I Open Syllable,AY,,"{""i"": ""final""}",OpenSyllable,"[""word_length_range:1-2"", ""specific_words_only""]",final,,"[""VOWEL_TEAM"", ""MAGIC_E""]",100,"I, hi","pie, my, it"
HARD_C_RULE,Hard C before a/o/u,K,,"{""ca"": ""initial"", ""co"": ""initial"", ""cu"": ""initial""}",SpellingRule,"[""verify_hard_c_sound"", ""before_a_o_u""]",initial,,"[""SOFT_C""]",5,"cat, cot, cup, can, come","city, cent"
K_BEFORE_E_I,K before e/i/y,K,,"{""ke"": ""initial"", ""ki"": ""initial""}",SpellingRule,"[""verify_k_spelling"", ""before_e_i""]",initial,,"[""SOFT_C""]",5,"kit, key, kid, kite, keep","city, cent"
CH_DIGRAPH,CH Digraph,CH,,"{""ch"": ""any""}",Digraph,"[""two_letters_one_sound"", ""exclude_split_digraphs""]",any,,"[""SEPARATED_C_H""]",10,"chip, chat, much, lunch","character, chef, school"
SH_DIGRAPH,SH Digraph,SH,,"{""sh"": ""any""}",Digraph,"[""two_letters_one_sound"", ""exclude_split_digraphs""]",any,,"[""SEPARATED_S_H""]",10,"ship, shop, wish, cash, she","mishap"
TH_DIGRAPH,TH Digraph,TH,DH,"{""th"": ""any""}",Digraph,"[""two_letters_one_sound"", ""exclude_split_digraphs"", ""voiced_or_unvoiced""]",any,,"[""SEPARATED_T_H""]",10,"this, that, thin, bath, with","lighthouse"
WH_DIGRAPH,WH Digraph,W,HH W,"{""wh"": ""initial""}",Digraph,"[""two_letters_one_sound"", ""initial_only""]",initial,,"[]",10,"when, what, where, why, which","nowhere"
CK_DIGRAPH,CK Digraph,K,,"{""ck"": ""final""}",Digraph,"[""two_letters_one_sound"", ""final_only"", ""after_short_vowel"", ""one_syllable""]",final,after_short_vowel,"[""MAGIC_E"", ""R_CONTROLLED""]",10,"back, pack, sick, lock, duck","bark, make"
L_BLENDS_INITIAL,Initial L-Blends,CONSONANT+L,,"{""bl"": ""initial"", ""cl"": ""initial"", ""fl"": ""initial"", ""gl"": ""initial"", ""pl"": ""initial"", ""sl"": ""initial""}",Blend,"[""both_sounds_heard"", ""initial_only"", ""true_blend""]",initial,,"[""DIGRAPH""]",5,"black, clap, flag, glad, play, slow","help, below"
R_BLENDS_INITIAL,Initial R-Blends,CONSONANT+R,,"{""br"": ""initial"", ""cr"": ""initial"", ""dr"": ""initial"", ""fr"": ""initial"", ""gr"": ""initial"", ""pr"": ""initial"", ""tr"": ""initial""}",Blend,"[""both_sounds_heard"", ""initial_only"", ""true_blend"", ""no_w_sound""]",initial,,"[""DIGRAPH"", ""R_CONTROLLED""]",5,"bring, crab, drip, from, grab, print, trip","car, forth"
S_BLENDS_INITIAL,Initial S-Blends,S+CONSONANT,,"{""sc"": ""initial"", ""sk"": ""initial"", ""sm"": ""initial"", ""sn"": ""initial"", ""sp"": ""initial"", ""st"": ""initial"", ""sw"": ""initial""}",Blend,"[""both_sounds_heard"", ""initial_only"", ""true_blend""]",initial,,"[""SH_DIGRAPH""]",5,"scan, skip, smell, snap, spot, stop, swim","fast, sh"
THREE_LETTER_BLENDS,3-Letter Blends,THREE_CONSONANTS,,"{""scr"": ""initial"", ""spr"": ""initial"", ""str"": ""initial"", ""spl"": ""initial"", ""squ"": ""initial"", ""thr"": ""initial""}",Blend,"[""all_sounds_heard"", ""initial_only"", ""three_consonants""]",initial,,"[""DIGRAPH""]",3,"scrap, spring, string, split, square, three","first, inst"
FINAL_NT_ST_BLENDS,Final nt/st/sk Blends,CONSONANT+T/K,,"{""nt"": ""final"", ""st"": ""final"", ""sk"": ""final"", ""ct"": ""final"", ""pt"": ""final"", ""xt"": ""final""}",FinalBlend,"[""both_sounds_heard"", ""final_only"", ""tongue_tap""]",final,,"[]",5,"ant, went, best, desk, act, next",""
FINAL_MP_FT_BLENDS,Final mp/ft/lf/sp Blends,CONSONANT+P/T/F,,"{""mp"": ""final"", ""ft"": ""final"", ""lf"": ""final"", ""sp"": ""final""}",FinalBlend,"[""both_sounds_heard"", ""final_only"", ""lip_pop""]",final,,"[]",5,"camp, lift, self, wasp",""
FINAL_ND_LM_BLENDS,Final nd/lm/ld Blends,CONSONANT+D/M,,"{""nd"": ""final"", ""lm"": ""final"", ""ld"": ""final""}",FinalBlend,"[""both_sounds_heard"", ""final_only"", ""liquid_blend""]",final,,"[]",5,"hand, film, cold",""
FLOSS_RULE,FLOSS Doubling Rule,CONSONANT_DOUBLED,,"{""ff"": ""final"", ""ll"": ""final"", ""ss"": ""final"", ""zz"": ""final""}",SpellingRule,"[""double_after_short_vowel"", ""one_syllable"", ""final_only""]",final,after_short_vowel,"[]",10,"stuff, bell, pass, buzz",""
MAGIC_E_A,Magic E with A,EY,,"{""a_e"": ""split""}",MagicE,"[""silent_e"", ""consonant_between"", ""long_vowel""]",split,,"[""EXCEPTIONS""]",10,"make, cake, late, gate","have"
MAGIC_E_I,Magic E with I,AY,,"{""i_e"": ""split""}",MagicE,"[""silent_e"", ""consonant_between"", ""long_vowel""]",split,,"[""EXCEPTIONS""]",10,"bike, like, time, kite","give"
MAGIC_E_O,Magic E with O,OW,,"{""o_e"": ""split""}",MagicE,"[""silent_e"", ""consonant_between"", ""long_vowel""]",split,,"[""EXCEPTIONS""]",10,"hope, home, note, rope","come, some, love"
MAGIC_E_U,Magic E with U,YUW,,"{""u_e"": ""split""}",MagicE,"[""silent_e"", ""consonant_between"", ""long_vowel""]",split,,"[""EXCEPTIONS""]",10,"cute, cube, mute, huge",""
LONG_A_AI,Long A with AI,EY,,"{""ai"": ""medial""}",VowelTeam,"[""two_vowels_one_sound"", ""long_a_sound"", ""medial_position""]",medial,,"[""SHORT_VOWEL""]",5,"rain, train, plain, main","said, again"
LONG_A_AY,Long A with AY,EY,,"{""ay"": ""final""}",VowelTeam,"[""two_vowels_one_sound"", ""long_a_sound"", ""final_position""]",final,,"[""SHORT_VOWEL""]",5,"play, day, may, say",""
LONG_A_EIGH,Long A with EIGH,EY,,"{""eigh"": ""medial""}",VowelTeam,"[""four_letters_one_sound"", ""long_a_sound""]",medial,,"[""SHORT_VOWEL""]",2,"eight, neighbor, weigh",""
LONG_E_EE,Long E with EE,IY,,"{""ee"": ""any""}",VowelTeam,"[""two_vowels_one_sound"", ""long_e_sound""]",any,,"[""SHORT_VOWEL""]",5,"see, tree, need, keep",""
LONG_E_EA,Long E with EA,IY,,"{""ea"": ""any""}",VowelTeam,"[""two_vowels_one_sound"", ""long_e_sound""]",any,,"[""SHORT_E_EA"", ""LONG_A_EA""]",5,"eat, read, beach, clean","bread, great"
LONG_E_EY,Long E with EY,IY,,"{""ey"": ""final""}",VowelTeam,"[""two_vowels_one_sound"", ""long_e_sound"", ""final_position""]",final,,"[""LONG_A_EY""]",3,"key, turkey, monkey",""
LONG_O_OA,Long O with OA,OW,,"{""oa"": ""medial""}",VowelTeam,"[""two_vowels_one_sound"", ""long_o_sound"", ""medial_position""]",medial,,"[""SHORT_VOWEL""]",5,"boat, coat, road, soap",""
LONG_O_OE,Long O with OE,OW,,"{""oe"": ""final""}",VowelTeam,"[""two_vowels_one_sound"", ""long_o_sound"", ""final_position""]",final,,"[""SHORT_VOWEL""]",3,"toe, foe, hoe","shoe"
LONG_O_OW,Long O with OW,OW,,"{""ow"": ""final""}",VowelTeam,"[""two_vowels_one_sound"", ""long_o_sound"", ""final_position""]",final,,"[""OW_DIPHTHONG""]",5,"grow, show, know, snow","cow, now"
OW_DIPHTHONG,OW as in COW,AW,,"{""ow"": ""final"", ""ou"": ""medial""}",Diphthong,"[""two_vowels_new_sound"", ""ow_sound""]",any,,"[""LONG_O_OW"", ""LONG_O_OU""]",5,"cow, now, out, house","low, soul"
OY_DIPHTHONG,OY as in BOY,OY,,"{""oi"": ""medial"", ""oy"": ""final""}",Diphthong,"[""two_vowels_new_sound"", ""oy_sound"", ""position_specific""]",any,,"[]",5,"oil, coin, boy, toy",""
AW_DIPHTHONG,AW as in SAW,AO,,"{""au"": ""medial"", ""aw"": ""final""}",Diphthong,"[""two_vowels_new_sound"", ""aw_sound"", ""position_specific""]",any,,"[]",5,"saw, law, haul, cause","laugh"
SHORT_E_EA,Short E with EA,EH,,"{""ea"": ""medial""}",VowelTeam,"[""two_vowels_one_sound"", ""short_e_sound""]",medial,,"[""LONG_E_EA"", ""LONG_A_EA""]",3,"bread, head, dead, spread","eat, great"
LONG_OO,Long OO as in MOON,UW,,"{""oo"": ""medial""}",VowelTeam,"[""two_vowels_one_sound"", ""long_oo_sound""]",medial,,"[""SHORT_OO""]",5,"moon, food, tooth, spoon","book, good"
SHORT_OO,Short OO as in BOOK,UH,,"{""oo"": ""medial""}",VowelTeam,"[""two_vowels_one_sound"", ""short_oo_sound""]",medial,,"[""LONG_OO""]",5,"book, look, good, wood","moon, food"
R_CONTROLLED_ER,ER Sound,ER,,"{""er"": ""any"", ""ir"": ""any"", ""ur"": ""any""}",RControlled,"[""r_controls_vowel"", ""er_sound"", ""three_spellings""]",any,,"[""MAGIC_E""]",5,"her, bird, turn, fern","were, where"
R_CONTROLLED_OR,OR Sound,AO R,,"{""or"": ""any""}",RControlled,"[""r_controls_vowel"", ""or_sound""]",any,,"[""W_CHANGES_OR""]",5,"for, corn, sport, north",""
R_CONTROLLED_AR,AR Sound,AA R,,"{""ar"": ""any""}",RControlled,"[""r_controls_vowel"", ""ar_sound""]",any,,"[""W_CHANGES_AR""]",5,"car, star, park, hard",""
W_CHANGES_AR,W+AR = OR Sound,AO R,,"{""war"": ""initial""}",RControlled,"[""w_changes_sound"", ""ar_to_or""]",initial,,"[]",3,"war, warm, warn, wart",""
W_CHANGES_OR,W+OR = ER Sound,ER,,"{""wor"": ""initial""}",RControlled,"[""w_changes_sound"", ""or_to_er""]",initial,,"[]",3,"work, word, world, worm",""
Y_AS_LONG_I,Y as Long I,AY,,"{""y"": ""final""}",YAsVowel,"[""y_as_vowel"", ""long_i_sound"", ""one_syllable""]",final,one_syllable,"[]",5,"cry, fly, by, try, sky",""
Y_AS_LONG_E,Y as Long E,IY,,"{""y"": ""final""}",YAsVowel,"[""y_as_vowel"", ""long_e_sound"", ""two_syllables""]",final,two_syllables,"[]",5,"baby, candy, happy, silly",""
Y_AS_SHORT_I,Y as Short I,IH,,"{""y"": ""medial""}",YAsVowel,"[""y_as_vowel"", ""short_i_sound"", ""medial_position""]",medial,,"[]",3,"gym, myth, cyst",""
SOFT_G_INITIAL,Initial Soft G,JH,,"{""ge"": ""initial"", ""gi"": ""initial"", ""gy"": ""initial""}",SoftSound,"[""g_before_e_i_y"", ""j_sound""]",initial,,"[""HARD_G""]",3,"giant, gym, gentle",""
SOFT_C_INITIAL,Initial Soft C,S,,"{""ce"": ""initial"", ""ci"": ""initial"", ""cy"": ""initial""}",SoftSound,"[""c_before_e_i_y"", ""s_sound""]",initial,,"[""HARD_C""]",3,"city, cent, cycle",""
SOFT_G_FINAL,Final Soft G,JH,,"{""ge"": ""final""}",SoftSound,"[""after_long_vowel"", ""j_sound""]",final,after_long_vowel,"[]",3,"cage, age, huge",""
SILENT_KN,Silent K in KN,N,,"{""kn"": ""initial""}",SilentLetter,"[""k_is_silent"", ""initial_only""]",initial,,"[]",5,"know, knee, knife, knit",""
SILENT_WR,Silent W in WR,R,,"{""wr"": ""initial""}",SilentLetter,"[""w_is_silent"", ""initial_only""]",initial,,"[]",5,"write, wrong, wrap, wrist",""
SILENT_MB,Silent B in MB,M,,"{""mb"": ""final""}",SilentLetter,"[""b_is_silent"", ""final_only""]",final,,"[]",3,"lamb, thumb, climb, comb",""
ED_AS_T,ED pronounced /t/,T,,"{""ed"": ""final""}",EDEnding,"[""after_unvoiced"", ""t_sound""]",final,after_unvoiced_consonant,"[]",10,"jumped, walked, helped, looked",""
ED_AS_D,ED pronounced /d/,D,,"{""ed"": ""final""}",EDEnding,"[""after_voiced"", ""d_sound""]",final,after_voiced_sound,"[]",10,"played, called, loved, pulled",""
ED_AS_ED,ED pronounced /ed/,EH D,,"{""ed"": ""final""}",EDEnding,"[""after_t_or_d"", ""ed_sound"", ""extra_syllable""]",final,after_t_or_d,"[]",10,"planted, needed, wanted, started",""
TCH_CATCH_RULE,TCH after short vowel,CH,,"{""tch"": ""final""}",SpellingRule,"[""after_short_vowel"", ""one_syllable"", ""ch_sound""]",final,after_short_vowel,"[]",5,"catch, match, pitch, notch","much, such, which"
DGE_BRIDGE_RULE,DGE after short vowel,JH,,"{""dge"": ""final""}",SpellingRule,"[""after_short_vowel"", ""one_syllable"", ""j_sound""]",final,after_short_vowel,"[]",5,"badge, edge, bridge, lodge",""
SCHWA_O,Schwa O Sound,AH,,"{""o"": ""unstressed""}",Schwa,"[""unstressed_syllable"", ""uh_sound""]",medial,unstressed,"[]",3,"come, love, some, done",""
SCHWA_A,Schwa A Sound,AH,,"{""a"": ""unstressed""}",Schwa,"[""unstressed_syllable"", ""uh_sound""]",medial,unstressed,"[]",3,"about, ago, banana, sofa",""
COMPOUND_WORDS,Compound Words,MULTIPLE,,"{""compound"": ""boundary""}",Syllabication,"[""two_complete_words"", ""divide_between""]",boundary,,"[]",5,"backpack, homework, playground, cupcake",""
PREFIX_SUFFIX,Prefix/Suffix Division,MULTIPLE,,"{""prefix"": ""initial"", ""suffix"": ""final""}",Syllabication,"[""affix_boundary"", ""keep_affix_whole""]",boundary,,"[]",5,"un/tie, re/turn, jump/ing, tall/er",""
VCCV_PATTERN,VCCV Syllable Division,MULTIPLE,,"{""vccv"": ""between_consonants""}",Syllabication,"[""two_consonants_between_vowels"", ""divide_between""]",between_consonants,,"[]",5,"rab/bit, nap/kin, win/ter, hap/pen",""
VCV_OPEN,VCV Open Syllable,MULTIPLE,,"{""vcv"": ""after_vowel""}",Syllabication,"[""one_consonant_between_vowels"", ""divide_after_vowel"", ""first_vowel_long""]",after_first_vowel,,"[]",3,"ti/ger, pa/per, mu/sic, ro/bot",""
VCV_CLOSED,VCV Closed Syllable,MULTIPLE,,"{""vcv"": ""after_consonant""}",Syllabication,"[""one_consonant_between_vowels"", ""divide_after_consonant"", ""first_vowel_short""]",after_consonant,,"[]",3,"cam/el, nev/er, lim/it, vis/it",""
CLE_SYLLABLE,Consonant+LE Syllable,MULTIPLE,,"{""cle"": ""final""}",Syllabication,"[""consonant_plus_le"", ""count_back_three""]",final_syllable,,"[]",5,"tur/tle, ap/ple, ta/ble, sim/ple",""