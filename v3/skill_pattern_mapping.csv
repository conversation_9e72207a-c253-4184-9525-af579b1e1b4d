skill_id,pattern_ids,filter_criteria
1.1,SHORT_A_CVC,"strict_cvc,no_exceptions"
1.2,SHORT_E_CVC,"strict_cvc,no_exceptions"
1.3,SHORT_I_CVC,"strict_cvc,no_exceptions"
1.4,SHORT_<PERSON>_CVC,"strict_cvc,no_exceptions"
1.5,SHORT_U_CVC,"strict_cvc,no_exceptions"
2.1,LONG_E_CV,"specific_words_only"
2.2,LONG_O_CV,"specific_words_only"
2.3,LONG_I_CV,"specific_words_only"
3.1,HARD_C_RULE,"verify_sound"
3.2,K_BEFORE_E_I,"verify_sound"
4.1,CH_DIGRAPH,"exclude_exceptions"
4.2,SH_DIGRAPH,"exclude_split"
4.3,TH_DIGRAPH,"include_voiced_unvoiced"
4.4,WH_DIGRAPH,"initial_only"
4.5,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,"after_short_vowel_only"
5.1,<PERSON>_<PERSON><PERSON><PERSON>S_INITIAL,"verify_both_sounds"
5.2,<PERSON>_<PERSON><PERSON><PERSON>S_INITIAL,"verify_both_sounds,no_w_sound"
5.3,S_BLENDS_INITIAL,"verify_both_sounds"
5.4,THREE_LETTER_BLENDS,"verify_all_sounds"
6.1,FINAL_NT_ST_BLENDS,"verify_both_sounds"
6.2,FINAL_MP_FT_BLENDS,"verify_both_sounds"
6.3,FINAL_ND_LM_BLENDS,"verify_both_sounds"
7.0,FLOSS_RULE,"one_syllable_only"
8.0,COMPOUND_WORDS,"identify_boundaries"
9.0,TCH_CATCH_RULE,"after_short_vowel"
10.0,DGE_BRIDGE_RULE,"after_short_vowel"
11.0,"MAGIC_E_A,MAGIC_E_I,MAGIC_E_O,MAGIC_E_U","exclude_exceptions"
12.0,,"memorize_exceptions"
13.1,SOFT_G_INITIAL,"verify_j_sound"
13.2,SOFT_C_INITIAL,"verify_s_sound"
13.3,SOFT_G_FINAL,"after_long_vowel"
13.4,SOFT_C_FINAL,"after_long_vowel"
13.5,SOFT_S_FINAL,"after_long_vowel"
14.1,Y_AS_LONG_I,"one_syllable_only"
14.2,Y_AS_LONG_E,"two_syllable_only"
14.3,Y_AS_SHORT_I,"medial_only"
15.1,SCHWA_O,"verify_unstressed"
15.2,SCHWA_A,"verify_unstressed"
15.3,SCHWA_E,"verify_unstressed"
16.0,"SILENT_KN,SILENT_WR,SILENT_MB","verify_silent"
17.0,"ED_AS_T,ED_AS_D,ED_AS_ED","verify_pronunciation"
18.1,"LONG_A_AI,LONG_A_AY,LONG_A_EIGH","position_specific"
18.2,"LONG_E_EE,LONG_E_EA,LONG_E_EY","verify_sound"
18.3,"LONG_O_OA,LONG_O_OE,LONG_O_OW","verify_sound"
18.4,OW_DIPHTHONG,"verify_diphthong"
18.5,"LONG_I_IE,LONG_I_IGH","position_specific"
18.6,"LONG_U_UE,LONG_U_EW","verify_y_glide"
18.7,LONG_OO,"distinguish_from_short"
18.8,SHORT_OO,"distinguish_from_long"
18.9,SHORT_U_OU,"verify_sound"
18.10,AW_DIPHTHONG,"position_specific"
18.11,OY_DIPHTHONG,"position_specific"
18.12,SHORT_E_EA,"verify_sound"
19.1,,"verify_homophones"
19.2,,"verify_homophones"
19.3,,"verify_homophones"
20.1,R_CONTROLLED_ER,"three_spellings"
20.2,R_CONTROLLED_OR,"consistent_sound"
20.3,R_CONTROLLED_AR,"consistent_sound"
20.4,W_CHANGES_AR,"w_effect"
20.5,W_CHANGES_OR,"w_effect"
21.1,COMPOUND_SYLLABLE,"find_boundaries"
21.2,PREFIX_SUFFIX,"keep_affixes_whole"
21.3,VV_SYLLABLE,"not_vowel_teams"
21.4,VCCV_PATTERN,"most_common"
21.5,VCCCV_OSTRICH,"keep_blends"
21.6,VCCCV_HAMSTER,"keep_blends"
21.7,VCV_OPEN,"first_vowel_long"
21.8,VCV_CLOSED,"first_vowel_short"
21.9,CLE_SYLLABLE,"count_back_three"
22.0,PREFIX_PATTERNS,"identify_base"
23.1,SUFFIX_PATTERNS,"identify_base"
23.2,SPECIAL_SUFFIX_PATTERNS,"pronunciation_change"
24.0,ONE_ONE_ONE_DOUBLING,"check_pattern"
25.0,E_DROP_RULE,"before_vowel_suffix"
26.0,Y_TO_I_RULE,"except_ing"
27.1,PLURAL_S,"regular_plural"
27.2,PLURAL_ES,"after_sibilant"
27.3,F_TO_V_PLURAL,"special_change"
27.5,IRREGULAR_PLURAL,"memorize"
28.0,TWO_ONE_ONE_DOUBLING,"check_stress"
29.0,SILENT_E_VS_PLURAL,"context"
30.0,CONTRACTIONS,"identify_missing"