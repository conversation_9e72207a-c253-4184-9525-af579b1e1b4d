rule_id,rule_type,description,implementation,parameters,examples,counter_examples
position_0_equals,orthographic,First letter must be a specific character,word[0] == parameter,letter: str,"k->keep,kit,key","k->cat,bat"
position_1_in_set,orthographic,Second letter must be in a set of characters,word[1] in parameter_set,"letters: set[str]","[e,i,y]->keep,kit,<PERSON>","[e,i,y]->know,kale"
position_n_equals,orthographic,Character at position N must equal specific value,"word[n] == value","position: int, value: str","2=t->cat,bat,sit","2=t->can,ban,sin"
starts_with_grapheme,orthographic,Word must start with specific letter combination,word.startswith(grapheme),grapheme: str,"kn->knee,know,knife","kn->keep,neat"
ends_with_grapheme,orthographic,Word must end with specific letter combination,word.endswith(grapheme),grapheme: str,"ck->back,pack,deck","ck->cat,bake"
contains_grapheme,orthographic,Word must contain specific letter combination,grapheme in word,grapheme: str,"ch->chair,much,chop","ch->cat,sit"
grapheme_at_position,orthographic,Specific grapheme must occur at given position,word[pos:pos+len(grapheme)] == grapheme,"position: int, grapheme: str","0,ch->chair,chop,chin","0,ch->share,car"
initial_phoneme_is,phonetic,First sound must be specific phoneme,phonemes[0] == target_phoneme,phoneme: str,"K->cat,keep,queen","K->cent,city"
final_phoneme_is,phonetic,Last sound must be specific phoneme,phonemes[-1] == target_phoneme,phoneme: str,"T->cat,sit,caught","T->can,sad"
contains_phoneme,phonetic,Word must contain specific phoneme,target_phoneme in phonemes,phoneme: str,"AE->cat,hand,trap","AE->cake,boat"
phoneme_count,phonetic,Word must have exact number of phonemes,len(phonemes) == count,count: int,"3->cat,dog,hop","3->cats,train"
vowel_sound_is,phonetic,Primary vowel sound must be specific phoneme,get_vowel_phoneme(word) == target,phoneme: str,"EY->cake,make,late","EY->cat,can"
grapheme_followed_by,positional,Grapheme must be followed by specific pattern,"find_grapheme(word,g) and word[pos+len(g)] in following_set","grapheme: str, following: set[str]","c,[a,o,u]->cat,cot,cup","c,[a,o,u]->cent,city"
not_followed_by,positional,Grapheme must NOT be followed by specific pattern,"find_grapheme(word,g) and word[pos+len(g)] not in excluded_set","grapheme: str, excluded: set[str]","c,[e,i,y]->cat,cot,cup","c,[e,i,y]->cent,city"
preceded_by,positional,Grapheme must be preceded by specific pattern,"find_grapheme(word,g) and word[pos-1] in preceding_set","grapheme: str, preceding: set[str]","ck,[short_vowels]->back,deck","ck,[short_vowels]->bark"
between_patterns,positional,Pattern must occur between two other patterns,"pattern_before in word[:pos] and pattern_after in word[pos+len(g):]","before: str, after: str, grapheme: str","a,e,_->make,cake,late","a,e,_->mat,mad"
word_length_exact,structural,Word must be exactly N characters long,len(word) == length,length: int,"3->cat,dog,hop","3->cats,hope"
word_length_range,structural,Word length must be within range,min_len <= len(word) <= max_len,"min: int, max: int","2,4->go,cat,hope","2,4->a,house"
syllable_count,structural,Word must have exact number of syllables,count_syllables(word) == count,count: int,"1->cat,hope,tree","1->happy,running"
one_syllable_only,structural,Word must have exactly one syllable,count_syllables(word) == 1,none,"->cat,hope,tree","->happy,about"
consonant_vowel_consonant,structural,Word must follow CVC pattern,matches_pattern(word,'CVC'),none,"->cat,hop,sit","->car,eat,cats"
no_blends,structural,Word must not contain consonant blends,not contains_blend(word),none,"->cat,hop,mat","->stop,black"
no_digraphs,structural,Word must not contain digraphs,not contains_digraph(word),none,"->cat,mat,hop","->chat,ship"
exclude_pattern,filter,Word must not match excluded pattern,not matches_pattern(word,excluded),pattern: str,"MAGIC_E->cat,sit,hop","MAGIC_E->cake,hope"
exclude_exceptions,filter,Word must not be in exception list,word not in exceptions_list,exceptions: list[str],"[have,come,some]->make,cake","[have,come,some]->have,come"
specific_words_only,filter,Word must be in allowed word list,word in allowed_words,words: list[str],"[be,he,me,we,she]->be,he,me","[be,he,me,we,she]->bee,her"
after_short_vowel,context,Pattern must follow a short vowel sound,preceding_vowel_is_short(word,pos),none,"->back,deck,sick","->bark,duke"
before_vowel_suffix,context,Pattern occurs before vowel suffix,following_suffix_starts_with_vowel(word,pos),none,"->hoping,making","->hoped,makes"
memorize_pattern,instruction,Pattern must be memorized as irregular,mark_as_sight_word(word),none,"->rough,though","->regular patterns"
verify_sound,instruction,Sound must be verified in pronunciation,verify_pronunciation(word,expected_sound),sound: str,"->cent as /s/","->cent as /k/"
true_blend,validation,Both consonant sounds must be heard,all_consonants_pronounced(blend),none,"->black,clap,trip","->write,knee"
silent_letter,validation,Specified letter must be silent,letter_is_silent(word,letter),"letter: str, position: int","k,0->knee,know","k,0->keep,kit"
syllable_boundary_at,syllabication,Marks where syllables divide in a word,syllable_breaks[n] == position,"position: int, type: str","3,VC/CV->rab/bit,win/ter","3,VC/CV->ra/bbit"
syllable_pattern,syllabication,Defines syllable division pattern type,matches_syllable_pattern(word,pattern),pattern: str,"VCCV->rab-bit,win-ter,nap-kin","VCCV->ti-ger,pa-per"
compound_boundary_at,syllabication,Marks boundary between compound words,compound_break == position,position: int,"4->back/pack,home/work","4->backp/ack"
morpheme_boundary_at,syllabication,Marks boundary at prefix/suffix,morpheme_break == position,"position: int, type: str","2,prefix->un/tie,re/turn","2,prefix->unt/ie"
stressed_syllable_is,syllabication,Identifies which syllable has primary stress,stressed_syllable_index == n,index: int,"1->a-BOUT,re-TURN","1->A-bout,RE-turn"
vowel_in_syllable_n,syllabication,Checks vowel sound in specific syllable,get_syllable_vowel(word,n) == phoneme,"syllable: int, phoneme: str","1,AE->CAT-er,HAP-py","1,AE->MAKE-er,GO-ing"
syllable_type_is,syllabication,Identifies syllable type (open/closed/CLE/etc),get_syllable_type(word,n) == type,"syllable: int, type: str","1,open->TI-ger,PA-per","1,open->RAB-bit,WIN-ter"
cle_pattern,syllabication,Word ends with consonant+le pattern,word.endswith(consonant+'le') and is_syllable,none,"->ta-ble,sim-ple,tur-tle","->smile,ale"
vcv_division_type,syllabication,Determines if VCV divides as open or closed,vcv_division_point(word) == type,type: str,"open->ti/ger,pa/per,ro/bot","open->cam/el,nev/er"
vcccv_division_type,syllabication,Determines VCCCV division (ostrich vs hamster),vcccv_keeps_blend_together(word) == bool,keep_blend: bool,"true->in-struct,com-plete","true->ins-truct"
syllable_has_schwa,syllabication,Syllable contains schwa sound,syllable_contains_schwa(word,n) == true,syllable: int,"2->a-BOUT,ba-NA-na","2->CAT-fish,MAKE-up"