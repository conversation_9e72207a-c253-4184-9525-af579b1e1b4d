# V4 Word Generator Implementation Guide

## System Overview

The V4 system uses a hierarchical approach to pattern management:

1. **Base Patterns** (`pattern_definitions_v4.csv`) - Pure phoneme-grapheme relationships
2. **Position Variants** (`pattern_positions_v4.csv`) - Context-specific applications
3. **Teaching Sequences** (`teaching_sequences_v4.csv`) - Ordered curriculum progression

## Key Design Principles

### 1. Numbered Sequential References

Every pattern, rule, and sequence has a unique ID for easy reordering:
- Patterns: P001-P085
- Position Variants: PV001-PV056
- Syllable Types: ST01-ST16
- Morphology Rules: MR01-MR20
- Teaching Sequences: SEQ01-SEQ36

### 2. Position-Aware Pattern System

Patterns work across different positions:
- `word_final`: End of complete word only
- `syllable_final`: End of any syllable
- `morpheme_final`: End of base word (before suffix)
- `medial`: Within syllable boundaries
- `initial`: Beginning of word/syllable

### 3. Morphology Integration

The system maintains pattern integrity through inflections:
- Base word patterns persist in inflected forms
- Spelling rules apply systematically
- Morpheme boundaries are preserved

## Implementation Examples

### Example 1: AY Pattern Across Contexts

```python
# Base Pattern (P065)
pattern = {
    'id': 'P065',
    'grapheme': 'ay',
    'phoneme': 'EY',  # Long A sound
}

# Position Variants
variants = [
    {
        'id': 'PV008',
        'position': 'word_final',
        'examples': ['play', 'day', 'way']
    },
    {
        'id': 'PV009', 
        'position': 'syllable_final',
        'examples': ['playing', 'player', 'payment']
    }
]

# The pattern maintains its sound regardless of position
assert get_phoneme('play') == 'EY'
assert get_phoneme('playing') == 'EY'  # Same sound at syllable boundary
```

### Example 2: CK Digraph Handling

```python
# Base Pattern (P019 - K sound)
# Position Variant (PV022 - word final CK)
# Position Variant (PV023 - syllable final CK)

def validate_ck_pattern(word):
    if word.endswith('ck'):
        # Check if after short vowel
        return is_short_vowel(word[-3])
    elif 'ck' in word:
        # Check if at syllable boundary
        syllables = syllabify(word)
        for syl in syllables:
            if syl.endswith('ck'):
                return is_short_vowel(syl[-3])
    return False

# Examples
assert validate_ck_pattern('pack')     # True - word final
assert validate_ck_pattern('packing')  # True - syllable final
assert validate_ck_pattern('package')  # False - not after short vowel
```

### Example 3: Morphology Rule Application

```python
# 1-1-1 Doubling Rule (MR03)
def apply_doubling_rule(base, suffix):
    if (count_syllables(base) == 1 and
        count_vowels(base) == 1 and
        base[-1] in CONSONANTS and
        suffix[0] in VOWELS):
        return base + base[-1] + suffix
    return base + suffix

# Examples
assert apply_doubling_rule('run', 'ing') == 'running'
assert apply_doubling_rule('jump', 'ing') == 'jumping'
assert apply_doubling_rule('mix', 'ing') == 'mixing'  # Exception
```

### Example 4: Teaching Sequence Integration

```python
def get_decodable_words(week_number):
    """Get words decodable by a specific week in the sequence"""
    
    # Get all sequences up to this week
    sequences = get_sequences_up_to(week_number)
    
    # Collect all pattern variants taught
    available_patterns = []
    for seq in sequences:
        available_patterns.extend(seq.pattern_variants)
    
    # Filter word list by available patterns
    decodable_words = []
    for word in word_database:
        if all_patterns_available(word, available_patterns):
            decodable_words.append(word)
    
    return decodable_words

# Week 5: Should include CVC + s-blends
assert 'stop' in get_decodable_words(5)
assert 'bike' not in get_decodable_words(5)  # Magic E not taught yet
```

### Example 5: Syllable Type Recognition

```python
def identify_syllable_type(syllable):
    """Identify REVLOC syllable type"""
    
    if syllable.endswith('e') and len(syllable) > 2:
        if is_vowel(syllable[-3]) and is_consonant(syllable[-2]):
            return 'ST02'  # Magic E (VCe)
    
    elif ends_with_vowel(syllable):
        return 'ST03'  # Open
    
    elif has_vowel_team(syllable):
        return 'ST06'  # Vowel Team
    
    elif has_r_controlled(syllable):
        return 'ST05'  # R-Controlled
    
    elif syllable.endswith(('ble', 'dle', 'tle', 'ple')):
        return 'ST04'  # Consonant-LE
    
    else:
        return 'ST01'  # Closed (default)
```

## Usage Patterns

### 1. Generate Words for Specific Week

```python
def generate_word_list(week, word_count=20):
    sequence = get_sequence(week)
    patterns = sequence.pattern_variants
    ratio = sequence.base_inflected_ratio
    
    base_count = int(word_count * ratio[0] / 100)
    inflected_count = word_count - base_count
    
    words = []
    words.extend(generate_base_words(patterns, base_count))
    words.extend(generate_inflected_words(patterns, inflected_count))
    
    return words
```

### 2. Check Word Decodability

```python
def calculate_decodability(word, taught_patterns):
    """Calculate percentage of word using taught patterns"""
    
    patterns_in_word = extract_patterns(word)
    taught_count = 0
    
    for pattern in patterns_in_word:
        if pattern in taught_patterns:
            taught_count += 1
    
    return (taught_count / len(patterns_in_word)) * 100
```

### 3. Apply Morphology Rules

```python
def add_suffix(base_word, suffix):
    """Apply appropriate morphology rules when adding suffix"""
    
    # Check each rule in sequence
    for rule in morphology_rules:
        if rule.conditions_met(base_word, suffix):
            return rule.apply(base_word, suffix)
    
    # Default: just add
    return base_word + suffix
```

## Best Practices

1. **Always Check Position Context**: A pattern's behavior may change based on position
2. **Preserve Morpheme Boundaries**: Don't break base words when adding suffixes
3. **Follow Teaching Sequence**: Introduce patterns in the prescribed order
4. **Track Prerequisites**: Ensure prerequisite patterns are mastered first
5. **Use Appropriate Ratios**: Follow base:inflected ratios for each week

## Common Pitfalls to Avoid

1. **Don't Assume Word-Final = Syllable-Final**: "play" vs "playing"
2. **Don't Ignore Morphology Rules**: "run" + "ing" ≠ "runing"
3. **Don't Skip Prerequisites**: Magic E requires CVC mastery
4. **Don't Mix Pattern Types**: Keep diphthongs separate from long vowels
5. **Don't Forget Exceptions**: Track common exception words

## Integration with Existing Systems

The V4 CSVs can be loaded into any system that needs:
- Pattern-based word generation
- Decodability scoring
- Morphology-aware text creation
- O-G aligned curriculum materials

The numbered reference system allows easy customization of teaching sequences while maintaining pattern integrity.