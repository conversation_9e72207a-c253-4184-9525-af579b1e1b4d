# V4 Orton-<PERSON>ingham Word Generator

A comprehensive, linguistically-accurate word generation system designed for systematic reading instruction based on Orton-Gillingham principles and the Science of Reading.

## Table of Contents
- [Overview](#overview)
- [Key Features](#key-features)
- [System Architecture](#system-architecture)
- [File Structure](#file-structure)
- [Getting Started](#getting-started)
- [Usage Examples](#usage-examples)
- [Teaching Progression](#teaching-progression)
- [Pattern System](#pattern-system)
- [Morphology Rules](#morphology-rules)
- [Customization](#customization)
- [FAQ](#faq)

## Overview

The V4 Word Generator addresses a critical challenge in reading instruction: generating decodable words that respect both phonics patterns AND morphological rules. Unlike traditional word lists, this system understands that patterns like "ay" in "play" maintain their sound in "playing" (syllable-final) not just in "play" (word-final).

### Why V4?

Previous versions treated patterns as fixed to word positions. V4 introduces:
- **Position-aware patterns** that work across word, syllable, and morpheme boundaries
- **Morphology integration** that maintains pattern integrity through inflections
- **Teaching sequence alignment** with <PERSON><PERSON>-<PERSON>ingham scope and sequence
- **Decodability scoring** to ensure words match student skill levels

## Key Features

- **Base Patterns**: Pure phoneme-grapheme relationships (85 patterns)
- **Position Variants**: Context-specific applications

### 2. REVLOC Syllable Type System
```
R - Closed (cat, rab•bit)
E - Magic E (make, com•pete)
V - Open (go, ti•ger)
L - Consonant-LE (ta•ble, sim•ple)
O - R-Controlled (car, tur•tle)
C - Vowel Team (rain, boa•ting)
```

### 3. Morphology-Aware Generation
- Three Great Spelling Rules (1-1-1, Drop E, Y to I)
- 20 morphological rules for accurate inflections
- Base word integrity preservation

### 4. Sequential Reference System
Every element has a unique ID for easy customization:
- Patterns: P001-P085
- Position Variants: PV001-PV056
- Morphology Rules: MR01-MR20
- Teaching Sequences: SEQ01-SEQ36

## System Architecture

```
┌─────────────────────────────────────┐
│     Pattern Definitions (Base)       │
│         85 Core Patterns             │
└────────────────┬────────────────────┘
                 │
┌────────────────▼────────────────────┐
│      Position Variants              │
│   56 Context-Specific Applications   │
└────────────────┬────────────────────┘
                 │
┌────────────────▼────────────────────┐
│     Teaching Sequences              │
│      36-Week Progression            │
└─────────────────────────────────────┘
                 │
         Supported By
                 │
┌─────────────────┴────────────────────┐
│  Syllable Types │ Morphology Rules   │
│   16 Types      │    20 Rules        │
└─────────────────────────────────────┘
```

## File Structure

```
v4/
├── README.md                      # This file
├── PLAN.md                        # Strategic overview
├── TASKS.md                       # Implementation roadmap
├── IMPLEMENTATION_GUIDE.md        # Technical guide with code examples
├── ENHANCED_SCOPE_SEQUENCE.md     # Detailed 36-week curriculum
│
├── pattern_definitions_v4.csv     # Base phoneme-grapheme mappings
├── pattern_positions_v4.csv       # Position-specific variants
├── syllable_types_v4.csv          # REVLOC syllable definitions
├── morphology_rules_v4.csv        # Suffix and spelling rules
├── teaching_sequences_v4.csv      # Weekly teaching progression
└── pattern_examples_v4.csv        # Example words demonstrating patterns
```

## Getting Started

### Quick Start

1. **Week 1 - Generate Short A CVC Words**:
```python
# Get words for Week 1 (SEQ01)
words = generate_words(sequence_id='SEQ01', count=10)
# Returns: ['cat', 'hat', 'mat', 'sat', 'bat', 'fat', 'rat', 'pat', 'map', 'tap']
```

2. **Week 21 - Generate AI/AY Words**:
```python
# Get words with AI/AY patterns
words = generate_words(sequence_id='SEQ21', count=10)
# Returns mix of: ['rain', 'train', 'play', 'day', 'main', 'way', 'plain', 'stay']
```

3. **Check Word Decodability**:
```python
# Is "playing" decodable by week 21?
decodable = check_decodability('playing', week=21)
# Returns: True (student knows 'play' + '-ing' suffix)
```

### Understanding the Data

Each CSV file serves a specific purpose:

- **pattern_definitions_v4.csv**: Maps sounds to spellings
- **pattern_positions_v4.csv**: Shows where patterns can appear
- **teaching_sequences_v4.csv**: Defines what to teach when
- **morphology_rules_v4.csv**: Handles spelling changes
- **syllable_types_v4.csv**: Categorizes syllable patterns

## Usage Examples

### Example 1: Generate Decodable Word Lists

```python
def get_week_words(week_number):
    """Generate words appropriate for a specific week"""
    
    # Get the teaching sequence
    sequence = load_sequence(f'SEQ{week_number:02d}')
    
    # Get pattern variants for this week
    patterns = sequence['pattern_variants']
    
    # Apply base:inflected ratio
    ratio = sequence['base_inflected_ratio']
    
    # Generate appropriate words
    return generate_by_patterns(patterns, ratio)
```

### Example 2: Apply Morphology Rules

```python
def add_suffix(base_word, suffix):
    """Add suffix with appropriate spelling changes"""
    
    # Check 1-1-1 doubling rule
    if should_double(base_word, suffix):
        return base_word + base_word[-1] + suffix
    
    # Check drop E rule
    elif should_drop_e(base_word, suffix):
        return base_word[:-1] + suffix
    
    # Check Y to I rule
    elif should_change_y(base_word, suffix):
        return base_word[:-1] + 'i' + suffix
    
    # Default: just add
    return base_word + suffix

# Examples:
add_suffix('run', 'ing')    # → 'running' (doubling)
add_suffix('make', 'ing')   # → 'making' (drop e)
add_suffix('happy', 'ness') # → 'happiness' (y to i)
```

### Example 3: Syllable Division

```python
def divide_syllables(word):
    """Divide word into syllables using REVLOC types"""
    
    # Check for compound words first
    if is_compound(word):
        return divide_at_morphemes(word)
    
    # Check for affixes
    base, prefixes, suffixes = extract_morphemes(word)
    
    # Apply syllable division rules
    if 'VCCV' in word:
        return divide_between_consonants(word)
    elif 'VCV' in word:
        return divide_vcv_pattern(word)
    # ... etc
```

## Teaching Progression

### Phase 1: Foundation (Weeks 1-12)
- Short vowels in CVC words
- Initial consonant blends
- Digraphs (ch, sh, th, wh)
- FLOSS rule (ff, ll, ss, zz)

### Phase 2: Long Vowels (Weeks 13-24)
- Magic E patterns
- Open syllables
- Y as a vowel
- Common vowel teams

### Phase 3: Complex Patterns (Weeks 25-36)
- R-controlled vowels
- Diphthongs
- Advanced vowel patterns
- Morphology and multisyllables

## Pattern System

### Base Pattern Example
```csv
pattern_id: P006
pattern_name: Long A
target_phoneme: EY
graphemes: a,ai,ay,a_e,ei,eigh,ea,ey
```

### Position Variants
The same pattern works differently in different positions:
- **PV008**: AY word-final (play, day)
- **PV009**: AY syllable-final (playing, player)
- **PV010**: Long A before suffix (raining, played)

### Key Innovation
Patterns maintain their phoneme regardless of position, solving the "play→playing" problem that traditional systems struggle with.

## Morphology Rules

### The Three Great Spelling Rules

1. **1-1-1 Doubling Rule (MR03)**
   - One syllable + One vowel + One final consonant
   - Double when adding vowel suffix
   - run + ing = running

2. **Drop E Rule (MR02)**
   - Drop silent E before vowel suffix
   - Keep E before consonant suffix
   - make + ing = making, hope + ful = hopeful

3. **Y to I Rule (MR04)**
   - Change Y to I before suffix (except -ing)
   - happy + ness = happiness
   - cry + ing = crying (exception)

## Customization

### Adjusting Teaching Sequence

The numbered reference system allows easy reordering:

```python
# Original sequence
sequences = load_teaching_sequences()

# Swap weeks 15 and 16
sequences['SEQ15'], sequences['SEQ16'] = sequences['SEQ16'], sequences['SEQ15']

# Add custom sequence
sequences['SEQ37'] = {
    'pattern_variants': ['PV070', 'PV071'],
    'morphology_rules': ['MR17'],
    'focus': 'Latin suffixes'
}
```

### Adding New Patterns

1. Add to pattern_definitions_v4.csv:
```csv
P086,New Pattern,PHONEME,,grapheme,PatternClass,86,Notes
```

2. Create position variants:
```csv
PV057,P086,position_type,description,[rules],examples,non_examples,notes
```

3. Add to teaching sequence:
```csv
SEQ37,37,Phase,Focus,PV057,MR01,ST01,SEQ36,80:20,errors,assessment,notes
```

## FAQ

### Q: How does this differ from traditional word lists?
A: Traditional lists are static. This system generates words dynamically based on taught patterns, ensuring 100% decodability for each student's skill level.

### Q: Can I use this with other reading programs?
A: Yes! While designed for Orton-Gillingham, the pattern system works with any systematic phonics approach.

### Q: How do I handle exceptions?
A: Exception words are documented in the pattern definitions. The system can flag or exclude them based on your needs.

### Q: What about regional pronunciation differences?
A: The phoneme system uses CMU pronunciation dictionary standards but includes variants where needed (see phoneme_variants in patterns).

### Q: Can I generate sentences, not just words?
A: The system provides decodable words. Sentence generation would layer on top, using only words meeting decodability criteria.

## Contributing

To contribute:
1. Maintain the numbered reference system
2. Document pattern behaviors across positions
3. Include examples and non-examples
4. Test with real student data when possible

## Research Foundation

This system is based on:
- Orton-Gillingham methodology
- Science of Reading research
- Structured Literacy principles
- Evidence from cognitive psychology on reading acquisition

See ENHANCED_SCOPE_SEQUENCE.md for detailed research citations.

## License

This word generation system is designed for educational use in teaching reading through systematic, explicit phonics instruction.