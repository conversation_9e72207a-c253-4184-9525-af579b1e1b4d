rule_id,rule_name,rule_type,description,conditions,base_change,suffix,examples,exceptions,notes
MR01,Just Add,no_change,Add suffix with no change to base word,"[ends_with:consonant, syllables:any, not_ends_with:[y,e]]",,"-s, -ing, -er, -est, -ness, -less, -ful, -ly","cats, jumping, helper, fastest, kindness, hopeless, helpful, slowly",,Most common and easiest rule
MR02,Drop E,drop_e,Drop silent E before vowel suffix,"[ends_with:silent_e, suffix_starts_with:vowel]",drop_final_e,"-ing, -er, -est, -ed, -able","making, hoped, nicer, latest, likable","being, seeing",Keep E before consonant suffix (hopeful)
MR03,1-1-1 Doubling,doubling,Double final consonant when adding vowel suffix,"[syllables:1, vowels:1, final_consonants:1, final_is_consonant:true, suffix_starts_with:vowel]",double_final_consonant,"-ing, -er, -est, -ed","running, bigger, hottest, stopped","fixed, mixed",Check all three conditions
MR04,Change Y to I,y_to_i,Change Y to I before suffix,"[ends_with:consonant_y, suffix_not:ing]",change_y_to_i,"-es, -ed, -er, -est, -ness, -ly","cries, tried, happier, busiest, happiness, easily","playing, boys",Keep Y before -ing
MR05,Add ES,add_es,Add ES for plurals/verbs after certain endings,"[ends_with:[s,x,z,ch,sh]]",,-es,"buses, boxes, fizzes, churches, dishes",gas→gases,Makes extra syllable
MR06,F to VES,f_to_ves,Change F/FE to VES for certain plurals,"[ends_with:[f,fe], is_noun:true]",change_f_to_v,-es,"calves, knives, wolves, leaves, wives","chiefs, roofs, cliffs",Limited set of words
MR07,Keep Y plural,keep_y_plural,Keep Y and add S when Y follows vowel,[ends_with:vowel_y],,-s,"boys, days, keys, plays, toys",,Vowel before Y
MR08,Double + ES,double_es,Double final consonant and add ES,"[ends_with:single_z, after_short_vowel:true]",double_final_consonant,-es,"buzzes, fizzes, whizzes",,Rare pattern for Z
MR09,CK + ED,ck_suffix,Add suffix to CK words treating as single unit,[ends_with:ck],,"-ed, -ing, -er","packed, packing, packer, sticker",,CK acts as single consonant
MR10,IC + AL,ic_to_ical,Add AL to IC words,[ends_with:ic],,-al,"magical, musical, historical, tropical",publicly,Common with Latin roots
MR11,2-1-1 Doubling,two_one_one,Double when stress on final syllable,"[syllables:2+, stress:final, ends_with:CVC, suffix_starts_with:vowel]",double_final_consonant,"-ing, -er, -ed","beginning, forgetting, occurred","opening, entered",Check stress pattern
MR12,No Double 2-1-1,no_double_two,Don't double when stress not on final,"[syllables:2+, stress:not_final, ends_with:CVC]",,"-ing, -er, -ed","opening, entering, listening, happening",,Stress determines doubling
MR13,ING forms,ing_progressive,Form present participle with -ing,[is_verb:true],apply_applicable_rules,-ing,"playing, running, making, trying",being (irregular),May trigger other rules
MR14,ED past,ed_past,Form past tense with -ed,"[is_verb:true, regular:true]",apply_applicable_rules,"-ed, -ing, -er","played, stopped, hoped, tried","went, saw, came",Sound varies by final phoneme
MR15,Plural S voicing,s_voicing,S sounds like /z/ after voiced sounds,[ends_with:voiced_sound],,-s,"dogs/z/, runs/z/, plays/z/, trees/z/","cats/s/, tops/s/",Phonetic not spelling rule
MR16,Consonant + LE,consonant_le,Add suffix to words ending in consonant+le,[ends_with:consonant_le],,"-s, -d","tables, settled, purpled, simpler",,Treat C+LE as unit
MR17,Latin Roots,latin_suffix,Special suffixes for Latin roots,"[origin:latin, ends_with:[t,te,de,se]]",various,"-tion, -sion, -ture","education, explosion, nature, picture",,Advanced morphology
MR18,Greek Combining,greek_forms,Greek roots combine with specific patterns,[origin:greek],none,"-ology, -phobia, -graph","biology, claustrophobia, photograph",,Advanced study
MR19,Compound Words,compounds,Two words combine with no change,[type:compound],none,none,"playground, homework, sunshine, everybody",,Keep both words intact
MR20,Prefix Addition,prefix_add,Add prefix with no base change,[adding:prefix],none,"un-, re-, dis-, pre-, mis-","undo, replay, disagree, preview, mistake",irregular,Never change base spelling