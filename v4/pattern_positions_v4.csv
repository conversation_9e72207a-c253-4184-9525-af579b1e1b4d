position_variant_id,base_pattern_id,position_type,position_description,validation_rules,examples,non_examples,teaching_notes
PV001,P001,medial,Short A in closed syllable,"[word_structure:CVC, syllable_type:closed, vowel_sound:short]","cat, hat, mat, sat, bat","cake, car, day",First pattern taught
PV002,P002,medial,Short E in closed syllable,"[word_structure:CVC, syllable_type:closed, vowel_sound:short]","bed, red, wet, met, pen","bee, her, me",Distinguish from long E
PV003,P003,medial,Short I in closed syllable,"[word_structure:CVC, syllable_type:closed, vowel_sound:short]","sit, hit, big, win, fit","bike, bird, hi",Common pattern
PV004,P004,medial,Short O in closed syllable,"[word_structure:CVC, syllable_type:closed, vowel_sound:short]","hot, top, got, dog, not","boat, for, go",Watch for regional variations
PV005,P005,medial,Short U in closed syllable,"[word_structure:CVC, syllable_type:closed, vowel_sound:short]","cup, run, but, sun, nut","cute, turn, flu",Often sounds like schwa
PV006,P006,split,Long A with magic E,"[pattern:VCe, final_e:silent, vowel_sound:long]","make, cake, late, name","have, are",Silent E exception words exist
PV007,P064,medial,AI vowel team medial,"[position:not_final, vowel_team:ai, sound:long_a]","rain, train, plain, main","fair, said",AI never ends words
PV008,P065,word_final,AY vowel team final,"[position:word_final, vowel_team:ay, sound:long_a]","play, day, way, say, may","playing, player",Stays in inflected forms
PV009,P065,syllable_final,AY at syllable boundary,"[position:syllable_final, before_suffix:true]","playing, player, payment","daylight",Maintains long A sound
PV010,P006,morpheme_final,Long A before suffix,"[position:morpheme_boundary, pattern:various]","raining, played, playful","airplane",Base word integrity
PV011,P007,word_final,Long E in open syllable,"[syllable_type:open, position:final, sound:long_e]","he, she, me, we, be","her, the",High frequency words
PV012,P066,any,EE vowel team,"[vowel_team:ee, sound:long_e, position:any]","see, tree, need, feet","been",Consistent sound
PV013,P067,any,EA vowel team (long E),"[vowel_team:ea, sound:long_e]","eat, read, beach, team","bread, great",Has variant sounds
PV014,P008,split,Long I with magic E,"[pattern:VCe, final_e:silent, vowel_sound:long]","bike, like, time, kite","give, live",Common exceptions
PV015,P008,word_final,Y as long I,"[position:word_final, letter:y, syllables:1]","cry, fly, by, try, sky","happy, very",One syllable only
PV016,P009,split,Long O with magic E,"[pattern:VCe, final_e:silent, vowel_sound:long]","hope, home, rope, note","come, some, love",Several exceptions
PV017,P068,medial,OA vowel team,"[vowel_team:oa, position:not_final, sound:long_o]","boat, coat, road, soap","boa",Rarely final
PV018,P069,word_final,OW as long O,"[position:word_final, sound:long_o]","grow, show, snow, flow","cow, now",Can be diphthong
PV019,P010,split,Long U with magic E,"[pattern:VCe, final_e:silent, vowel_sound:long]","cute, cube, use, huge","sure",Y-glide present
PV020,P015,initial,Hard C before A/O/U,"[position:initial, followed_by:[a,o,u], sound:k]","cat, cop, cup, can","city, cent",Spelling rule
PV021,P019,initial,K before E/I/Y,"[position:initial, followed_by:[e,i,y], sound:k]","kit, key, kind, keep","knee, know",Not before N
PV022,P019,word_final,CK after short vowel,"[position:word_final, after:short_vowel, syllables:1]","back, pack, sick, duck","bark, make",FLOSS-like rule
PV023,P019,syllable_final,CK at syllable boundary,"[position:syllable_final, before_suffix:true]","packing, sticker, ducky","package",Maintains digraph
PV024,P020,any,CH digraph,"[digraph:ch, sound:ch, position:any]","chip, much, lunch, chat","chef, chute",Regular CH sound
PV025,P021,any,SH digraph,"[digraph:sh, sound:sh, position:any]","ship, wish, shop, push","mishap",True digraph
PV026,P024,initial,WH digraph initial,"[digraph:wh, position:initial, sound:w_or_hw]","when, what, where, why","who",WHO is exception
PV027,P029,initial,S-blends initial,"[blend:s+consonant, position:initial]","stop, skip, smell, spot","fast, best",Initial only
PV028,P030,initial,L-blends initial,"[blend:consonant+l, position:initial]","black, clap, flag, glad","help, milk",Initial only
PV029,P031,initial,R-blends initial,"[blend:consonant+r, position:initial]","bring, crab, drip, from","car, for",Initial only
PV030,P032,word_final,Final blends with T,"[blend:consonant+t, position:final]","ant, best, lift, kept","at, bit",Two consonants
PV031,P032,syllable_final,Final T blends at syllable,"[blend:consonant+t, position:syllable_final]","resting, lifting, painted","water",Before suffix
PV032,P038,medial,OI diphthong medial,"[diphthong:oi, position:medial, sound:oy]","oil, coin, voice, point","going",Never final
PV033,P038,word_final,OY diphthong final,"[diphthong:oy, position:final, sound:oy]","boy, toy, joy, enjoy","boyish",Final position
PV034,P039,medial,OU diphthong medial,"[diphthong:ou, position:medial, sound:ow]","out, house, about, found","soul, you",Not long U
PV035,P039,word_final,OW diphthong final,"[diphthong:ow, position:final, sound:ow]","cow, now, how, wow","show, grow",Not long O
PV036,P041,initial,Silent K before N,"[position:initial, pattern:kn, silent:k]","knee, know, knife, knit","key, kit",K is silent
PV037,P042,initial,Silent W before R,"[position:initial, pattern:wr, silent:w]","write, wrong, wrap, wrist","wait, work",W is silent
PV038,P043,word_final,Silent B after M,"[position:final, pattern:mb, silent:b]","lamb, thumb, climb, bomb","member, number",B is silent
PV039,P047,word_final,Magic E pattern,"[position:final, letter:e, function:make_vowel_long]","make, hope, cute, time","have, love",Changes vowel sound
PV040,P049,word_final,Y as long I final,"[position:final, letter:y, syllables:1, sound:long_i]","cry, fly, try, sky","happy, baby",One syllable words
PV041,P050,word_final,Y as long E final,"[position:final, letter:y, syllables:2+, sound:long_e]","happy, baby, silly, funny","cry, by",Multisyllable words
PV042,P051,word_final,Double F after short vowel,"[position:final, pattern:ff, after:short_vowel]","stuff, cliff, off, puff","if, of",FLOSS rule
PV043,P052,word_final,Double L after short vowel,"[position:final, pattern:ll, after:short_vowel]","bell, tell, will, pull","pal, coal",FLOSS rule
PV044,P053,word_final,Double S after short vowel,"[position:final, pattern:ss, after:short_vowel]","pass, miss, less, boss","gas, this",FLOSS rule
PV045,P054,word_final,Double Z after short vowel,"[position:final, pattern:zz, after:short_vowel]","buzz, fizz, jazz, fuzz","quiz",FLOSS rule
PV046,P055,word_final,TCH after short vowel,"[position:final, pattern:tch, after:short_vowel]","catch, match, witch, fetch","much, rich",Not after consonant
PV047,P056,word_final,DGE after short vowel,"[position:final, pattern:dge, after:short_vowel]","badge, edge, bridge, judge","age, cage",Not after consonant
PV048,P012,any,AR r-controlled,"[r_controlled:ar, sound:ar, position:any]","car, star, farm, start","war, dollar",Consistent sound
PV049,P013,any,OR r-controlled,"[r_controlled:or, sound:or, position:any]","for, corn, sport, short","word, work",Watch for W
PV050,P014,any,ER/IR/UR r-controlled,"[r_controlled:er_ir_ur, sound:er, position:any]","her, bird, turn, first","here, fire",Three spellings
PV051,P057,word_final,ED as /t/ sound,"[suffix:ed, after:unvoiced, sound:t]","jumped, looked, helped","played, wanted",After p,k,f,s,ch,sh
PV052,P058,word_final,ED as /d/ sound,"[suffix:ed, after:voiced, sound:d]","played, called, turned","jumped, wanted",After vowels,voiced consonants
PV053,P059,word_final,ED as /id/ sound,"[suffix:ed, after:[t,d], sound:id, syllables:+1]","wanted, needed, started","jumped, played",Adds syllable
PV054,P061,word_final,ING suffix,"[suffix:ing, verb_form:progressive]","jumping, playing, running","ring, bring",Suffix not part of base
PV055,P016,initial,Soft C before E/I/Y,"[position:initial, followed_by:[e,i,y], sound:s]","city, cent, cycle, cell","cat, cup",Makes S sound
PV056,P018,initial,Soft G before E/I/Y,"[position:initial, followed_by:[e,i,y], sound:j]","gem, giant, gym, gentle","get, go",Makes J sound