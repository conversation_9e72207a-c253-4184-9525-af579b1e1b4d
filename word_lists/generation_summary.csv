skill_id,skill_name,word_count,min_length,max_length,filename
1.1,CVC short a,50,3,3,word_lists/skill_1.1_CVC_short_a.csv
1.2,CVC short e,38,3,3,word_lists/skill_1.2_CVC_short_e.csv
1.3,CVC short i,41,3,3,word_lists/skill_1.3_CVC_short_i.csv
1.4,CVC short o,42,3,3,word_lists/skill_1.4_CVC_short_o.csv
1.5,CVC short u,30,3,3,word_lists/skill_1.5_CVC_short_u.csv
10.0,Bridge rule,22,5,6,word_lists/skill_10.0_Bridge_rule.csv
11.0,Magic E,50,4,5,word_lists/skill_11.0_Magic_E.csv
12.1,Closed syllable exceptions,50,3,6,word_lists/skill_12.1_Closed_syllable_exceptions.csv
13.1,Soft g,50,4,7,word_lists/skill_13.1_Soft_g.csv
13.2,Soft c,50,4,7,word_lists/skill_13.2_Soft_c.csv
14.1,Y as vowel,50,3,6,word_lists/skill_14.1_Y_as_vowel.csv
15.1,o says uh,50,4,8,word_lists/skill_15.1_o_says_uh.csv
15.2,a says uh,50,4,8,word_lists/skill_15.2_a_says_uh.csv
15.3,e says uh,50,4,8,word_lists/skill_15.3_e_says_uh.csv
16.0,Silent letters,50,4,7,word_lists/skill_16.0_Silent_letters.csv
17.0,3 sounds of ed,50,4,8,word_lists/skill_17.0_3_sounds_of_ed.csv
18.1,Long a teams,50,4,8,word_lists/skill_18.1_Long_a_teams.csv
18.10,aw sound,50,4,8,word_lists/skill_18.10_aw_sound.csv
18.11,oy sound,50,4,8,word_lists/skill_18.11_oy_sound.csv
18.12,ea says e,50,4,8,word_lists/skill_18.12_ea_says_e.csv
18.2,Long e teams,50,4,8,word_lists/skill_18.2_Long_e_teams.csv
18.3,Long o teams,50,4,8,word_lists/skill_18.3_Long_o_teams.csv
18.4,ow sound,50,4,8,word_lists/skill_18.4_ow_sound.csv
18.5,Long i teams,50,4,8,word_lists/skill_18.5_Long_i_teams.csv
18.6,Long u teams,50,4,8,word_lists/skill_18.6_Long_u_teams.csv
18.7,Long oo,50,4,8,word_lists/skill_18.7_Long_oo.csv
18.8,Short oo,50,4,8,word_lists/skill_18.8_Short_oo.csv
18.9,ou says u,50,4,8,word_lists/skill_18.9_ou_says_u.csv
19.0,Homophones,29,2,10,word_lists/skill_19.0_Homophones.csv
2.1,CV long e,9,2,2,word_lists/skill_2.1_CV_long_e.csv
2.2,CV long o,6,2,2,word_lists/skill_2.2_CV_long_o.csv
2.3,CV long i,4,2,2,word_lists/skill_2.3_CV_long_i.csv
20.1,er sound,50,3,10,word_lists/skill_20.1_er_sound.csv
20.2,or sound,50,3,10,word_lists/skill_20.2_or_sound.csv
20.3,ar sound,50,3,10,word_lists/skill_20.3_ar_sound.csv
20.4,war sound,50,3,10,word_lists/skill_20.4_war_sound.csv
20.5,wor sound,50,3,10,word_lists/skill_20.5_wor_sound.csv
21.1,Compound words,50,6,12,word_lists/skill_21.1_Compound_words.csv
21.2,Turtle words,50,5,10,word_lists/skill_21.2_Turtle_words.csv
21.5,Rabbit words,50,5,10,word_lists/skill_21.5_Rabbit_words.csv
22.0,Common prefixes,50,5,15,word_lists/skill_22.0_Common_prefixes.csv
23.1,Common suffixes,50,5,15,word_lists/skill_23.1_Common_suffixes.csv
23.2,Irregular suffixes,50,5,15,word_lists/skill_23.2_Irregular_suffixes.csv
24.0,1-1-1 doubling,50,5,12,word_lists/skill_24.0_1-1-1_doubling.csv
25.0,E-dropping rule,9,5,12,word_lists/skill_25.0_E-dropping_rule.csv
26.0,Change y to i,9,5,12,word_lists/skill_26.0_Change_y_to_i.csv
27.1,Plural -s,50,3,12,word_lists/skill_27.1_Plural_-s.csv
27.2,Plural -es,50,4,12,word_lists/skill_27.2_Plural_-es.csv
27.3,f to v plural,8,4,12,word_lists/skill_27.3_f_to_v_plural.csv
27.5,Irregular plurals,8,3,12,word_lists/skill_27.5_Irregular_plurals.csv
28.0,2-1-1 doubling,8,6,15,word_lists/skill_28.0_2-1-1_doubling.csv
29.0,Silent e not plural,50,4,10,word_lists/skill_29.0_Silent_e_not_plural.csv
3.1,"c before a,o,u",50,3,4,"word_lists/skill_3.1_c_before_a,o,u.csv"
3.2,"k before e,i,y",18,3,4,"word_lists/skill_3.2_k_before_e,i,y.csv"
30.0,Contractions,34,3,10,word_lists/skill_30.0_Contractions.csv
4.1,ch digraph,11,3,4,word_lists/skill_4.1_ch_digraph.csv
4.2,sh digraph,30,3,4,word_lists/skill_4.2_sh_digraph.csv
4.3,th digraph,26,3,4,word_lists/skill_4.3_th_digraph.csv
4.4,wh digraph,7,3,4,word_lists/skill_4.4_wh_digraph.csv
4.5,ck digraph,28,3,4,word_lists/skill_4.5_ck_digraph.csv
5.1,l-blends,50,4,5,word_lists/skill_5.1_l-blends.csv
5.2,r-blends,50,4,5,word_lists/skill_5.2_r-blends.csv
5.3,s-blends,50,4,5,word_lists/skill_5.3_s-blends.csv
5.4,3-letter blends,50,5,6,word_lists/skill_5.4_3-letter_blends.csv
6.1,nt ending,46,4,5,word_lists/skill_6.1_nt_ending.csv
6.10,mp ending,17,4,5,word_lists/skill_6.10_mp_ending.csv
6.11,nd ending,37,4,5,word_lists/skill_6.11_nd_ending.csv
6.12,ng ending,45,4,5,word_lists/skill_6.12_ng_ending.csv
6.13,sk ending,10,4,5,word_lists/skill_6.13_sk_ending.csv
6.14,st ending,50,4,5,word_lists/skill_6.14_st_ending.csv
6.15,ft ending,13,4,5,word_lists/skill_6.15_ft_ending.csv
6.2,nd ending,37,4,5,word_lists/skill_6.2_nd_ending.csv
6.3,nk ending,28,4,5,word_lists/skill_6.3_nk_ending.csv
6.4,lt ending,21,4,5,word_lists/skill_6.4_lt_ending.csv
6.5,ld ending,21,4,5,word_lists/skill_6.5_ld_ending.csv
6.6,lf ending,8,4,5,word_lists/skill_6.6_lf_ending.csv
6.7,lk ending,7,4,5,word_lists/skill_6.7_lk_ending.csv
6.8,lm ending,6,4,5,word_lists/skill_6.8_lm_ending.csv
6.9,lp ending,3,4,5,word_lists/skill_6.9_lp_ending.csv
7.0,FLOSS rule,50,4,6,word_lists/skill_7.0_FLOSS_rule.csv
8.0,Backpack rule,50,6,10,word_lists/skill_8.0_Backpack_rule.csv
9.0,Catch rule,20,5,6,word_lists/skill_9.0_Catch_rule.csv
