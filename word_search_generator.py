"""
Word Search Puzzle Generator
Creates educational word search puzzles from word lists
"""

import random
import string
from copy import deepcopy

class WordSearchGenerator:
    def __init__(self):
        self.directions = [
            (0, 1),   # horizontal (left to right)
            (1, 0),   # vertical (top to bottom)
            (1, 1),   # diagonal (top-left to bottom-right)
            (-1, 1),  # diagonal (bottom-left to top-right)
            (0, -1),  # horizontal (right to left) - optional
            (-1, 0),  # vertical (bottom to top) - optional
            (-1, -1), # diagonal (bottom-right to top-left) - optional
            (1, -1)   # diagonal (top-right to bottom-left) - optional
        ]
    
    def generate_puzzle(self, words, size=15, difficulty='medium', 
                       title="Word Search", uppercase=True):
        """
        Generate a word search puzzle
        
        Args:
            words: List of words to hide in the puzzle
            size: Grid size (size x size)
            difficulty: 'easy' (horizontal/vertical only), 
                       'medium' (+ diagonal), 
                       'hard' (all directions)
            title: Puzzle title
            uppercase: Convert words to uppercase
            
        Returns:
            dict with puzzle grid, solution, and word list
        """
        # Prepare words
        if uppercase:
            words = [word.upper() for word in words]
        
        # Filter words that are too long
        words = [w for w in words if len(w) <= size]
        
        # Sort by length (place longer words first)
        words.sort(key=len, reverse=True)
        
        # Set allowed directions based on difficulty
        if difficulty == 'easy':
            allowed_directions = self.directions[:2]  # horizontal and vertical only
        elif difficulty == 'medium':
            allowed_directions = self.directions[:4]  # + diagonals
        else:  # hard
            allowed_directions = self.directions  # all directions
        
        # Create empty grid
        grid = [['' for _ in range(size)] for _ in range(size)]
        solution_grid = [['' for _ in range(size)] for _ in range(size)]
        
        # Track placed words
        placed_words = []
        word_positions = {}
        
        # Try to place each word
        for word in words:
            placed = self._place_word(grid, word, allowed_directions, size)
            if placed:
                placed_words.append(word)
                word_positions[word] = placed
                
                # Mark solution
                start_row, start_col, direction = placed
                for i, letter in enumerate(word):
                    row = start_row + i * direction[0]
                    col = start_col + i * direction[1]
                    solution_grid[row][col] = letter
        
        # Fill empty cells with random letters
        for i in range(size):
            for j in range(size):
                if grid[i][j] == '':
                    grid[i][j] = random.choice(string.ascii_uppercase)
        
        # Create display grid
        display_grid = self._format_grid(grid)
        solution_display = self._format_grid(solution_grid, show_empty=True)
        
        return {
            'title': title,
            'size': size,
            'difficulty': difficulty,
            'grid': grid,
            'display_grid': display_grid,
            'solution_grid': solution_display,
            'words': placed_words,
            'word_positions': word_positions,
            'total_words': len(placed_words),
            'html': self._generate_html(grid, placed_words, title)
        }
    
    def _place_word(self, grid, word, directions, size):
        """Try to place a word in the grid"""
        # Randomize starting positions
        positions = [(r, c) for r in range(size) for c in range(size)]
        random.shuffle(positions)
        random.shuffle(directions)
        
        for row, col in positions:
            for direction in directions:
                if self._can_place_word(grid, word, row, col, direction, size):
                    # Place the word
                    for i, letter in enumerate(word):
                        new_row = row + i * direction[0]
                        new_col = col + i * direction[1]
                        grid[new_row][new_col] = letter
                    
                    return (row, col, direction)
        
        return None
    
    def _can_place_word(self, grid, word, start_row, start_col, direction, size):
        """Check if a word can be placed at the given position"""
        for i, letter in enumerate(word):
            new_row = start_row + i * direction[0]
            new_col = start_col + i * direction[1]
            
            # Check boundaries
            if new_row < 0 or new_row >= size or new_col < 0 or new_col >= size:
                return False
            
            # Check if cell is empty or has the same letter
            if grid[new_row][new_col] != '' and grid[new_row][new_col] != letter:
                return False
        
        return True
    
    def _format_grid(self, grid, show_empty=False):
        """Format grid for display"""
        formatted = []
        for row in grid:
            formatted_row = []
            for cell in row:
                if cell == '' and show_empty:
                    formatted_row.append('·')
                elif cell == '':
                    formatted_row.append(' ')
                else:
                    formatted_row.append(cell)
            formatted.append(' '.join(formatted_row))
        
        return '\n'.join(formatted)
    
    def _generate_html(self, grid, words, title):
        """Generate HTML for the puzzle"""
        html = f"""
        <html>
        <head>
            <title>{title}</title>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    margin: 20px;
                }}
                h1 {{
                    color: #1e3d59;
                    text-align: center;
                }}
                .puzzle-container {{
                    display: flex;
                    justify-content: space-around;
                    align-items: flex-start;
                    margin-top: 20px;
                }}
                .grid {{
                    border-collapse: collapse;
                    margin: 20px;
                }}
                .grid td {{
                    width: 30px;
                    height: 30px;
                    border: 1px solid #ccc;
                    text-align: center;
                    font-size: 18px;
                    font-weight: bold;
                }}
                .word-list {{
                    margin: 20px;
                }}
                .word-list h3 {{
                    color: #3e5c76;
                }}
                .word-list ul {{
                    list-style-type: none;
                    padding: 0;
                }}
                .word-list li {{
                    padding: 5px;
                    margin: 5px 0;
                    background-color: #f5f5f5;
                    border-radius: 4px;
                }}
                @media print {{
                    .no-print {{
                        display: none;
                    }}
                }}
            </style>
        </head>
        <body>
            <h1>{title}</h1>
            <div class="puzzle-container">
                <table class="grid">
        """
        
        # Add grid
        for row in grid:
            html += "<tr>"
            for cell in row:
                html += f"<td>{cell}</td>"
            html += "</tr>"
        
        html += """
                </table>
                <div class="word-list">
                    <h3>Find These Words:</h3>
                    <ul>
        """
        
        # Add word list
        for word in sorted(words):
            html += f"<li>{word}</li>"
        
        html += """
                    </ul>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html
    
    def create_themed_puzzle(self, word_list_df, theme, size=12, max_words=15):
        """
        Create a themed puzzle from a word list dataframe
        
        Args:
            word_list_df: DataFrame with word information
            theme: Theme name (used in title)
            size: Puzzle size
            max_words: Maximum number of words to include
            
        Returns:
            Generated puzzle dictionary
        """
        # Select appropriate words
        words = word_list_df['word'].tolist()
        
        # Filter by length and limit count
        suitable_words = [w for w in words if 3 <= len(w) <= size-2]
        selected_words = random.sample(
            suitable_words, 
            min(max_words, len(suitable_words))
        )
        
        # Generate puzzle
        return self.generate_puzzle(
            selected_words,
            size=size,
            title=f"{theme} Word Search",
            difficulty='medium'
        )